package com.anonymous.videodownloaderapp.integration

import java.io.File
import android.content.Context
import com.anonymous.videodownloaderapp.cache.DownloadCacheManager
import com.anonymous.videodownloaderapp.data.DownloadItem
import com.anonymous.videodownloaderapp.optimization.*
import com.anonymous.videodownloaderapp.services.EnhancedDownloadService
import kotlinx.coroutines.*

object PerformanceIntegration {
    
    private var enhancedDownloadService: EnhancedDownloadService? = null
    private var cacheManager: DownloadCacheManager? = null
    private var batteryOptimizer: BatteryOptimizationManager? = null
    private var storageManager: StorageSegmentationManager? = null
    private var parallelManager: ParallelDownloadManager? = null
    private var startupOptimizer: StartupOptimizer? = null
    
    fun initialize(context: Context) {
        enhancedDownloadService = EnhancedDownloadService(context)
        cacheManager = DownloadCacheManager(context)
        batteryOptimizer = BatteryOptimizationManager(context)
        storageManager = StorageSegmentationManager(context)
        parallelManager = ParallelDownloadManager(context)
        startupOptimizer = StartupOptimizer(context)
    }
    
    fun upgradeDownloadSystem() {
        // Start startup optimization
        startupOptimizer?.optimizeStartup()
        startupOptimizer?.preloadCriticalData()
    }
    
    fun startEnhancedDownload(downloadItem: DownloadItem) {
        enhancedDownloadService?.let { service ->
            CoroutineScope(Dispatchers.IO).launch {
                // Check if we should use parallel downloads for large files
                if (downloadItem.totalBytes > 50 * 1024 * 1024) { // 50MB+
                    parallelManager?.startParallelDownload(downloadItem)
                } else {
                    service.startDownload(downloadItem)
                }
            }
        }
    }
    
    fun pauseDownload(downloadId: String) {
        enhancedDownloadService?.pauseDownload(downloadId)
        parallelManager?.pauseDownload(downloadId)
    }
    
    fun resumeDownload(downloadId: String, downloadItem: DownloadItem) {
        enhancedDownloadService?.resumeDownload(downloadId)
        parallelManager?.resumeDownload(downloadId, downloadItem)
    }
    
    fun cancelDownload(downloadId: String) {
        enhancedDownloadService?.cancelDownload(downloadId)
        parallelManager?.cancelDownload(downloadId)
    }
    
    fun cacheUrlData(originalUrl: String, resolvedUrl: String, fileName: String, fileSize: Long, mimeType: String) {
        cacheManager?.cacheUrlData(originalUrl, resolvedUrl, fileName, fileSize, mimeType)
    }
    
    fun getCachedUrlData(originalUrl: String) = cacheManager?.getCachedUrlData(originalUrl)
    
    fun shouldRestrictDownloads(): Boolean {
        return batteryOptimizer?.shouldRestrictDownloads() ?: false
    }
    
    fun getOptimalStorageLocation(fileSize: Long): File? {
        return storageManager?.chooseOptimalStorageLocation(fileSize)
    }
    
    fun startBatteryMonitoring() {
        batteryOptimizer?.startBatteryMonitoring()
    }
    
    fun stopBatteryMonitoring() {
        batteryOptimizer?.stopBatteryMonitoring()
    }
    
    fun getOptimizationRecommendations(): List<String> {
        return batteryOptimizer?.getOptimizationRecommendations() ?: emptyList()
    }
    
    fun cleanup() {
        enhancedDownloadService?.shutdown()
        parallelManager?.shutdown()
        startupOptimizer?.shutdown()
        batteryOptimizer?.stopBatteryMonitoring()
    }
}
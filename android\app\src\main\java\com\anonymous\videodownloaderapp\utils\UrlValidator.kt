package com.anonymous.videodownloaderapp.utils

import android.webkit.URLUtil
import java.net.URL

object UrlValidator {

    private val videoExtensions = listOf(
        ".mp4", ".avi", ".mov", ".wmv", ".flv",
        ".webm", ".mkv", ".m4v", ".3gp", ".ogv"
    )

    fun isValidUrl(url: String): Boolean {
        return URLUtil.isValidUrl(url)
    }

    fun isDirectVideoUrl(url: String): Boolean {
        val lowerUrl = url.lowercase()
        return videoExtensions.any { ext -> lowerUrl.contains(ext) }
    }

    fun normalizeUrl(url: String): String {
        return try {
            var normalized = url
            if (!normalized.startsWith("http")) {
                normalized = "https://$normalized"
            }

            val urlObj = URL(normalized)

            // Normalize YouTube URLs
            if (urlObj.host.contains("youtube.com") || urlObj.host.contains("youtu.be")) {
                val videoId = if (urlObj.host.contains("youtu.be")) {
                    urlObj.path.substring(1).split("?")[0]
                } else {
                    val queryParams = urlObj.query?.split("&")
                    queryParams?.find { it.startsWith("v=") }?.substringAfter("v=")
                }
                if (videoId != null) {
                    return "https://www.youtube.com/watch?v=$videoId"
                }
            }

            // Normalize Vimeo URLs
            if (urlObj.host.contains("vimeo.com")) {
                val videoId = urlObj.path.split("/").lastOrNull()
                if (videoId != null && videoId.matches("\\d+".toRegex())) {
                    return "https://vimeo.com/$videoId"
                }
            }
            normalized
        } catch (e: Exception) {
            url
        }
    }

    fun extractUrlInfo(url: String): UrlInfo {
        return try {
            val normalizedUrl = normalizeUrl(url)
            val urlObj = URL(normalizedUrl)
            val hostname = urlObj.host.lowercase().replace("www.", "")

            var platform = "unknown"
            var videoId: String? = null

            when {
                hostname.contains("youtube.com") || hostname.contains("youtu.be") -> {
                    platform = "youtube"
                    videoId = if (hostname.contains("youtu.be")) {
                        urlObj.path.substring(1).split("?")[0]
                    } else {
                        val queryParams = urlObj.query?.split("&")
                        queryParams?.find { it.startsWith("v=") }?.substringAfter("v=")
                    }
                }
                hostname.contains("vimeo.com") -> {
                    platform = "vimeo"
                    videoId = urlObj.path.split("/").lastOrNull()
                }
                hostname.contains("tiktok.com") -> {
                    platform = "tiktok"
                    val match = "/video/(\\d+)".toRegex().find(urlObj.path)
                    videoId = match?.groups?.get(1)?.value
                }
                hostname.contains("instagram.com") -> {
                    platform = "instagram"
                    val match = "/(p|reel)/([^/]+)".toRegex().find(urlObj.path)
                    videoId = match?.groups?.get(2)?.value
                }
                hostname.contains("facebook.com") || hostname.contains("fb.watch") -> {
                    platform = "facebook"
                }
                hostname.contains("twitter.com") || hostname.contains("x.com") -> {
                    platform = "twitter"
                }
                isDirectVideoUrl(url) -> {
                    platform = "direct"
                }
            }

            UrlInfo(
                originalUrl = url,
                normalizedUrl = normalizedUrl,
                hostname = hostname,
                platform = platform,
                videoId = videoId,
                isSupported = true,
                isDirect = isDirectVideoUrl(url)
            )
        } catch (e: Exception) {
            UrlInfo(
                originalUrl = url,
                normalizedUrl = url,
                hostname = null,
                platform = "unknown",
                videoId = null,
                isSupported = false,
                isDirect = false,
                error = e.localizedMessage
            )
        }
    }
}

data class UrlInfo(
    val originalUrl: String,
    val normalizedUrl: String,
    val hostname: String?,
    val platform: String,
    val videoId: String?,
    val isSupported: Boolean,
    val isDirect: Boolean,
    val error: String? = null
)

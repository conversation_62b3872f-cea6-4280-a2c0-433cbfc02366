package com.anonymous.videodownloaderapp.data

import android.content.Context
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext

class DownloadRepository(
    private val dao: DownloadDao,
    private val io: CoroutineDispatcher = Dispatchers.IO
) {

    fun observeAll(): Flow<List<DownloadItem>> = dao.observeAll()

    fun observeById(id: Long): Flow<DownloadItem?> = dao.observeById(id)

    suspend fun getAll(): List<DownloadItem> = withContext(io) { dao.getAll() }

    suspend fun getById(id: Long): DownloadItem? = withContext(io) { dao.getById(id) }

    suspend fun upsert(item: DownloadItem): Long = withContext(io) { dao.upsert(item) }

    suspend fun upsertAll(items: List<DownloadItem>): List<Long> = withContext(io) { dao.upsertAll(items) }

    suspend fun update(item: DownloadItem) = withContext(io) { dao.update(item) }

    suspend fun delete(item: DownloadItem) = withContext(io) { dao.delete(item) }

    suspend fun deleteById(id: Long) = withContext(io) { dao.deleteById(id) }

    suspend fun pruneFinished() = withContext(io) { dao.pruneFinished() }

    suspend fun enqueue(url: String, title: String? = null, mimeType: String? = null): Long = withContext(io) {
        val item = DownloadItem(
            url = url,
            title = title,
            mimeType = mimeType,
            status = DownloadItem.Status.Queued,
            progress = 0,
            bytesDownloaded = 0,
            totalBytes = 0,
            mediaStoreUri = null,
            error = null
        )
        dao.upsert(item)
    }

    suspend fun markRunning(id: Long) = withContext(io) { dao.markStatus(id, DownloadItem.Status.Running) }

    suspend fun markPaused(id: Long) = withContext(io) { dao.markStatus(id, DownloadItem.Status.Paused) }

    suspend fun markProgress(id: Long, bytesDownloaded: Long, totalBytes: Long) = withContext(io) {
        val progress = if (totalBytes > 0) ((bytesDownloaded * 100L) / totalBytes).toInt().coerceIn(0, 100) else 0
        dao.markStatus(
            id = id,
            status = DownloadItem.Status.Running,
            progress = progress,
            bytesDownloaded = bytesDownloaded,
            totalBytes = totalBytes
        )
    }

    suspend fun markCompleted(id: Long, mediaStoreUri: String?, totalBytes: Long) = withContext(io) {
        dao.getById(id)?.let { current ->
            dao.upsert(
                current.copy(
                    status = DownloadItem.Status.Completed,
                    progress = 100,
                    bytesDownloaded = totalBytes,
                    totalBytes = totalBytes,
                    mediaStoreUri = mediaStoreUri,
                    error = null,
                    updatedAt = System.currentTimeMillis()
                )
            )
        }
    }

    suspend fun markFailed(id: Long, error: String?) = withContext(io) {
        dao.markStatus(id, DownloadItem.Status.Failed, error = error)
    }

    suspend fun markCanceled(id: Long) = withContext(io) {
        dao.markStatus(id, DownloadItem.Status.Canceled)
    }

    suspend fun isVideoDownloaded(url: String): Boolean = withContext(io) {
        val downloads = dao.getAll()
        downloads.any { item ->
            item.url == url && item.status == DownloadItem.Status.Completed
        }
    }

    suspend fun delete(id: Long) = withContext(io) {
        dao.deleteById(id)
    }

    companion object {
        @Volatile private var INSTANCE: DownloadRepository? = null

        fun get(context: Context): DownloadRepository {
            val db = AppDatabase.get(context)
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DownloadRepository(db.downloadDao()).also { INSTANCE = it }
            }
        }
    }
}
package com.anonymous.videodownloaderapp.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\n\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00050\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0018\u0010\u000e\u001a\u0004\u0018\u00010\u00052\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJN\u0010\u000f\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00020\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00132\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0017H\u0097@\u00a2\u0006\u0002\u0010\u0018J\u0014\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\f0\u001aH\'J\u0018\u0010\u001b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00050\u001a2\u0006\u0010\b\u001a\u00020\tH\'J\"\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\f0\u001a2\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00170\fH\'J\u000e\u0010\u001e\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0016\u0010\u001f\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010 \u001a\u00020\t2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\"\u0010!\u001a\b\u0012\u0004\u0012\u00020\t0\f2\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00050\fH\u00a7@\u00a2\u0006\u0002\u0010#\u00a8\u0006$"}, d2 = {"Lcom/anonymous/videodownloaderapp/data/DownloadDao;", "", "delete", "", "item", "Lcom/anonymous/videodownloaderapp/data/DownloadItem;", "(Lcom/anonymous/videodownloaderapp/data/DownloadItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAll", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getById", "markStatus", "status", "Lcom/anonymous/videodownloaderapp/data/DownloadItem$Status;", "progress", "", "bytesDownloaded", "totalBytes", "error", "", "(JLcom/anonymous/videodownloaderapp/data/DownloadItem$Status;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "observeAll", "Lkotlinx/coroutines/flow/Flow;", "observeById", "observeByStatuses", "statuses", "pruneFinished", "update", "upsert", "upsertAll", "items", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface DownloadDao {
    
    @androidx.room.Query(value = "SELECT * FROM downloads ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.anonymous.videodownloaderapp.data.DownloadItem>> observeAll();
    
    @androidx.room.Query(value = "SELECT * FROM downloads WHERE id = :id")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.anonymous.videodownloaderapp.data.DownloadItem> observeById(long id);
    
    @androidx.room.Query(value = "SELECT * FROM downloads WHERE status IN (:statuses) ORDER BY createdAt ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.anonymous.videodownloaderapp.data.DownloadItem>> observeByStatuses(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> statuses);
    
    @androidx.room.Query(value = "SELECT * FROM downloads ORDER BY createdAt DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAll(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.anonymous.videodownloaderapp.data.DownloadItem>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM downloads WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.anonymous.videodownloaderapp.data.DownloadItem> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object upsert(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadItem item, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object upsertAll(@org.jetbrains.annotations.NotNull()
    java.util.List<com.anonymous.videodownloaderapp.data.DownloadItem> items, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.Long>> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object update(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadItem item, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object delete(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadItem item, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM downloads WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM downloads WHERE status IN (\'Completed\',\'Canceled\',\'Failed\')")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object pruneFinished(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Transaction()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object markStatus(long id, @org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadItem.Status status, @org.jetbrains.annotations.Nullable()
    java.lang.Integer progress, @org.jetbrains.annotations.Nullable()
    java.lang.Long bytesDownloaded, @org.jetbrains.annotations.Nullable()
    java.lang.Long totalBytes, @org.jetbrains.annotations.Nullable()
    java.lang.String error, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
        
        @androidx.room.Transaction()
        @org.jetbrains.annotations.Nullable()
        public static java.lang.Object markStatus(@org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.data.DownloadDao $this, long id, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.data.DownloadItem.Status status, @org.jetbrains.annotations.Nullable()
        java.lang.Integer progress, @org.jetbrains.annotations.Nullable()
        java.lang.Long bytesDownloaded, @org.jetbrains.annotations.Nullable()
        java.lang.Long totalBytes, @org.jetbrains.annotations.Nullable()
        java.lang.String error, @org.jetbrains.annotations.NotNull()
        kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
            return null;
        }
    }
}
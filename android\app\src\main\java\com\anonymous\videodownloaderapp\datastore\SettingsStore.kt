package com.anonymous.videodownloaderapp.datastore

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

private const val SETTINGS_NAME = "settings_prefs"

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = SETTINGS_NAME)

class SettingsStore private constructor(private val context: Context) {

    object Keys {
        val WIFI_ONLY = booleanPreferencesKey("wifi_only")
        val ENABLE_PIP = booleanPreferencesKey("enable_pip")
        val CONCURRENCY_LIMIT = intPreferencesKey("concurrency_limit")
        // New persisted settings
        val AUTO_DOWNLOAD = booleanPreferencesKey("auto_download")
        val FAST_DOWNLOAD = booleanPreferencesKey("fast_download")
        val BG_DOWNLOAD = booleanPreferencesKey("bg_download")
        val VIDEO_QUALITY = intPreferencesKey("video_quality_index")
        val AUDIO_QUALITY = intPreferencesKey("audio_quality_index")
        val DOWNLOAD_PATH = intPreferencesKey("download_path_placeholder") // placeholder for future SAF (persist index/flag)
        val MAX_RETRIES = intPreferencesKey("max_retries")
        val AD_BLOCK = booleanPreferencesKey("ad_block")
        val JAVASCRIPT = booleanPreferencesKey("javascript")
        val COOKIES = booleanPreferencesKey("cookies")
        val CACHE = booleanPreferencesKey("cache")
        val MAX_CACHE_MB = intPreferencesKey("max_cache_mb")
        val NOTIFICATIONS = booleanPreferencesKey("notifications")
        val VIBRATION = booleanPreferencesKey("vibration")
        val DARK_MODE = booleanPreferencesKey("dark_mode")
        val ANALYTICS = booleanPreferencesKey("analytics")
        val AUTO_UPDATE = booleanPreferencesKey("auto_update")
        val CONNECT_TIMEOUT = intPreferencesKey("connect_timeout_s")
        val READ_TIMEOUT = intPreferencesKey("read_timeout_s")
    }

    data class Settings(
        val wifiOnly: Boolean = false,
        val enablePip: Boolean = true,
        val concurrencyLimit: Int = 20,
        val autoDownload: Boolean = false,
        val fastDownload: Boolean = true,
        val backgroundDownload: Boolean = true,
        val videoQualityIndex: Int = 0, // 0=Auto, 1=2160p, 2=1080p, 3=720p, 4=480p, 5=360p
        val audioQualityIndex: Int = 0, // 0=High, 1=Medium, 2=Low
        val downloadPathFlag: Int = 0, // placeholder
        val maxRetries: Int = 3,
        val adBlock: Boolean = false,
        val javascript: Boolean = true,
        val cookies: Boolean = true,
        val cache: Boolean = true,
        val maxCacheMb: Int = 500,
        val notifications: Boolean = true,
        val vibration: Boolean = true,
        val darkMode: Boolean = false,
        val analytics: Boolean = false,
        val autoUpdate: Boolean = true,
        val connectTimeoutS: Int = 30,
        val readTimeoutS: Int = 60
    )

    val settingsFlow: Flow<Settings> = context.dataStore.data.map { prefs ->
        Settings(
            wifiOnly = prefs[Keys.WIFI_ONLY] ?: false,
            enablePip = prefs[Keys.ENABLE_PIP] ?: true,
            concurrencyLimit = (prefs[Keys.CONCURRENCY_LIMIT] ?: 20).coerceIn(1, 20),
            autoDownload = prefs[Keys.AUTO_DOWNLOAD] ?: false,
            fastDownload = prefs[Keys.FAST_DOWNLOAD] ?: true,
            backgroundDownload = prefs[Keys.BG_DOWNLOAD] ?: true,
            videoQualityIndex = (prefs[Keys.VIDEO_QUALITY] ?: 0).coerceIn(0, 5),
            audioQualityIndex = (prefs[Keys.AUDIO_QUALITY] ?: 0).coerceIn(0, 2),
            downloadPathFlag = prefs[Keys.DOWNLOAD_PATH] ?: 0,
            maxRetries = (prefs[Keys.MAX_RETRIES] ?: 3).coerceIn(0, 9),
            adBlock = prefs[Keys.AD_BLOCK] ?: false,
            javascript = prefs[Keys.JAVASCRIPT] ?: true,
            cookies = prefs[Keys.COOKIES] ?: true,
            cache = prefs[Keys.CACHE] ?: true,
            maxCacheMb = (prefs[Keys.MAX_CACHE_MB] ?: 500).coerceIn(10, 10000),
            notifications = prefs[Keys.NOTIFICATIONS] ?: true,
            vibration = prefs[Keys.VIBRATION] ?: true,
            darkMode = prefs[Keys.DARK_MODE] ?: false,
            analytics = prefs[Keys.ANALYTICS] ?: false,
            autoUpdate = prefs[Keys.AUTO_UPDATE] ?: true,
            connectTimeoutS = (prefs[Keys.CONNECT_TIMEOUT] ?: 30).coerceIn(5, 300),
            readTimeoutS = (prefs[Keys.READ_TIMEOUT] ?: 60).coerceIn(5, 600)
        )
    }

    suspend fun setWifiOnly(enabled: Boolean) {
        context.dataStore.edit { it[Keys.WIFI_ONLY] = enabled }
    }

    suspend fun setEnablePip(enabled: Boolean) {
        context.dataStore.edit { it[Keys.ENABLE_PIP] = enabled }
    }

    suspend fun setConcurrencyLimit(limit: Int) {
        context.dataStore.edit { it[Keys.CONCURRENCY_LIMIT] = limit.coerceIn(1, 20) }
    }
    suspend fun setAutoDownload(enabled: Boolean) {
        context.dataStore.edit { it[Keys.AUTO_DOWNLOAD] = enabled }
    }
    suspend fun setFastDownload(enabled: Boolean) {
        context.dataStore.edit { it[Keys.FAST_DOWNLOAD] = enabled }
    }
    suspend fun setBackgroundDownload(enabled: Boolean) {
        context.dataStore.edit { it[Keys.BG_DOWNLOAD] = enabled }
    }
    suspend fun setVideoQualityIndex(index: Int) {
        context.dataStore.edit { it[Keys.VIDEO_QUALITY] = index.coerceIn(0, 5) }
    }
    suspend fun setAudioQualityIndex(index: Int) {
        context.dataStore.edit { it[Keys.AUDIO_QUALITY] = index.coerceIn(0, 2) }
    }
    suspend fun setDownloadPathFlag(flag: Int) {
        context.dataStore.edit { it[Keys.DOWNLOAD_PATH] = flag }
    }
    suspend fun setMaxRetries(value: Int) {
        context.dataStore.edit { it[Keys.MAX_RETRIES] = value.coerceIn(0, 9) }
    }
    suspend fun setAdBlock(enabled: Boolean) {
        context.dataStore.edit { it[Keys.AD_BLOCK] = enabled }
    }
    suspend fun setJavascript(enabled: Boolean) {
        context.dataStore.edit { it[Keys.JAVASCRIPT] = enabled }
    }
    suspend fun setCookies(enabled: Boolean) {
        context.dataStore.edit { it[Keys.COOKIES] = enabled }
    }
    suspend fun setCache(enabled: Boolean) {
        context.dataStore.edit { it[Keys.CACHE] = enabled }
    }
    suspend fun setMaxCacheMb(value: Int) {
        context.dataStore.edit { it[Keys.MAX_CACHE_MB] = value.coerceIn(10, 10000) }
    }
    suspend fun setNotifications(enabled: Boolean) {
        context.dataStore.edit { it[Keys.NOTIFICATIONS] = enabled }
    }
    suspend fun setVibration(enabled: Boolean) {
        context.dataStore.edit { it[Keys.VIBRATION] = enabled }
    }
    suspend fun setDarkMode(enabled: Boolean) {
        context.dataStore.edit { it[Keys.DARK_MODE] = enabled }
    }
    suspend fun setAnalytics(enabled: Boolean) {
        context.dataStore.edit { it[Keys.ANALYTICS] = enabled }
    }
    suspend fun setAutoUpdate(enabled: Boolean) {
        context.dataStore.edit { it[Keys.AUTO_UPDATE] = enabled }
    }
    suspend fun setConnectTimeout(value: Int) {
        context.dataStore.edit { it[Keys.CONNECT_TIMEOUT] = value.coerceIn(5, 300) }
    }
    suspend fun setReadTimeout(value: Int) {
        context.dataStore.edit { it[Keys.READ_TIMEOUT] = value.coerceIn(5, 600) }
    }

    companion object {
        @Volatile private var INSTANCE: SettingsStore? = null

        fun get(context: Context): SettingsStore =
            INSTANCE ?: synchronized(this) {
                INSTANCE ?: SettingsStore(context.applicationContext).also { INSTANCE = it }
            }
    }
}
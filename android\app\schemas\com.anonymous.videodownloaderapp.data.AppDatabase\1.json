{"formatVersion": 1, "database": {"version": 1, "identityHash": "e223229dc5b384528a85e7b425e62a70", "entities": [{"tableName": "downloads", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `url` TEXT NOT NULL, `title` TEXT, `mimeType` TEXT, `status` TEXT NOT NULL, `progress` INTEGER NOT NULL, `bytesDownloaded` INTEGER NOT NULL, `totalBytes` INTEGER NOT NULL, `mediaStoreUri` TEXT, `error` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mimeType", "columnName": "mimeType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "progress", "columnName": "progress", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bytesDownloaded", "columnName": "bytesDownloaded", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalBytes", "columnName": "totalBytes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mediaStoreUri", "columnName": "mediaStoreUri", "affinity": "TEXT", "notNull": false}, {"fieldPath": "error", "columnName": "error", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e223229dc5b384528a85e7b425e62a70')"]}}
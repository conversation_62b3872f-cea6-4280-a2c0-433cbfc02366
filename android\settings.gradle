pluginManagement {
    repositories {
        google()
        mavenCentral()
        // Explicit Google Maven URL for reliability
        maven { url "https://dl.google.com/dl/android/maven2" }
        // JitPack as fallback if some transitive dependency requires it
        maven { url "https://jitpack.io" }
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_PROJECT)
    repositories {
        google()
        mavenCentral()
        // Explicit Google Maven mirror
        maven { url "https://dl.google.com/dl/android/maven2" }
        // Fallbacks
        maven { url "https://jitpack.io" }
    }
}

rootProject.name = "VideoDownloader"
include(":app")


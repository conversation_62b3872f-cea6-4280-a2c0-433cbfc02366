package com.anonymous.videodownloaderapp.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u001a%\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\u0011\u0010\u000b\u001a\r\u0012\u0004\u0012\u00020\b0\f\u00a2\u0006\u0002\b\rH\u0007\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u000e\u0010\u0006\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"AppShapes", "Landroidx/compose/material3/Shapes;", "AppTypography", "Landroidx/compose/material3/Typography;", "DarkColors", "Landroidx/compose/material3/ColorScheme;", "LightColors", "AppTheme", "", "useDarkTheme", "", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "app_debug"})
public final class ThemeKt {
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.ColorScheme LightColors = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.ColorScheme DarkColors = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.Typography AppTypography = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.material3.Shapes AppShapes = null;
    
    @androidx.compose.runtime.Composable()
    public static final void AppTheme(boolean useDarkTheme, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
}
package com.anonymous.videodownloaderapp.services

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Handler
import android.os.Looper
import android.view.ViewTreeObserver
import androidx.lifecycle.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * Gestão de Lifecycle Mobile Especializada
 * Otimizações de Bateria e Performance para detecção de vídeo
 */
class MobileLifecycleManager(
    private val context: Context,
    private val activity: Activity?
) : DefaultLifecycleObserver {

    data class LifecycleState(
        val isActive: Boolean = true,
        val isBackgrounded: Boolean = false,
        val batteryLevel: Int = 100,
        val isLowBattery: Boolean = false,
        val scanInterval: Long = DEFAULT_SCAN_INTERVAL,
        val isThrottled: Boolean = false
    )

    companion object {
        private const val DEFAULT_SCAN_INTERVAL = 1000L // 1 segundo
        private const val THROTTLED_SCAN_INTERVAL = 3000L // 3 segundos
        private const val LOW_BATTERY_SCAN_INTERVAL = 5000L // 5 segundos
        private const val LOW_BATTERY_THRESHOLD = 20 // 20%
        private const val CRITICAL_BATTERY_THRESHOLD = 10 // 10%
        private const val BACKGROUND_PAUSE_DELAY = 2000L // 2 segundos
        
        @Volatile
        private var instance: MobileLifecycleManager? = null
        
        fun getInstance(context: Context, activity: Activity? = null): MobileLifecycleManager {
            return instance ?: synchronized(this) {
                instance ?: MobileLifecycleManager(context.applicationContext, activity).also {
                    instance = it
                }
            }
        }
    }

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private val mainHandler = Handler(Looper.getMainLooper())
    
    // Estado do lifecycle
    private val _lifecycleState = MutableStateFlow(LifecycleState())
    val lifecycleState: StateFlow<LifecycleState> = _lifecycleState
    
    // Controles de execução
    private val isDetectionActive = AtomicBoolean(true)
    private val lastScanTime = AtomicLong(0)
    private var backgroundPauseJob: Job? = null
    private var viewTreeObserver: ViewTreeObserver? = null
    
    // Listeners de callback
    private val lifecycleCallbacks = mutableSetOf<LifecycleCallback>()
    private val batteryReceiver = BatteryLevelReceiver()
    
    interface LifecycleCallback {
        fun onLifecycleStateChanged(state: LifecycleState)
        fun onDetectionShouldPause()
        fun onDetectionShouldResume()
        fun onBatteryOptimizationRequired(level: Int)
    }

    init {
        setupBatteryMonitoring()
        setupViewTreeObserver()
    }

    /**
     * Registrar callback de lifecycle
     */
    fun addLifecycleCallback(callback: LifecycleCallback) {
        lifecycleCallbacks.add(callback)
    }

    /**
     * Remover callback de lifecycle
     */
    fun removeLifecycleCallback(callback: LifecycleCallback) {
        lifecycleCallbacks.remove(callback)
    }

    /**
     * Verificar se detecção deve ser executada agora
     */
    fun shouldRunDetection(): Boolean {
        val currentState = _lifecycleState.value
        
        // Não executar se pausado ou em background
        if (!currentState.isActive || currentState.isBackgrounded) {
            return false
        }
        
        // Verificar intervalo mínimo
        val now = System.currentTimeMillis()
        val timeSinceLastScan = now - lastScanTime.get()
        
        if (timeSinceLastScan < currentState.scanInterval) {
            return false
        }
        
        // Atualizar último scan
        lastScanTime.set(now)
        return true
    }

    /**
     * Obter intervalo de scan otimizado baseado no estado
     */
    fun getOptimizedScanInterval(): Long {
        val currentState = _lifecycleState.value
        return currentState.scanInterval
    }

    /**
     * Pausar detecção manualmente
     */
    fun pauseDetection() {
        isDetectionActive.set(false)
        updateLifecycleState { it.copy(isActive = false) }
        notifyCallbacks { it.onDetectionShouldPause() }
    }

    /**
     * Retomar detecção manualmente
     */
    fun resumeDetection() {
        isDetectionActive.set(true)
        updateLifecycleState { it.copy(isActive = true) }
        notifyCallbacks { it.onDetectionShouldResume() }
    }

    // Lifecycle callbacks
    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        
        // Cancelar pausa de background se existir
        backgroundPauseJob?.cancel()
        backgroundPauseJob = null
        
        // Retomar detecção
        isDetectionActive.set(true)
        updateLifecycleState { 
            it.copy(
                isActive = true, 
                isBackgrounded = false
            )
        }
        
        notifyCallbacks { it.onDetectionShouldResume() }
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        
        // Pausar detecção após delay para evitar pausas desnecessárias
        backgroundPauseJob = scope.launch {
            delay(BACKGROUND_PAUSE_DELAY)
            
            isDetectionActive.set(false)
            updateLifecycleState { 
                it.copy(
                    isActive = false, 
                    isBackgrounded = true
                )
            }
            
            notifyCallbacks { it.onDetectionShouldPause() }
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        cleanup()
    }

    /**
     * Configurar monitoramento de bateria
     */
    private fun setupBatteryMonitoring() {
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_BATTERY_CHANGED)
            addAction(Intent.ACTION_BATTERY_LOW)
            addAction(Intent.ACTION_BATTERY_OKAY)
        }
        
        context.registerReceiver(batteryReceiver, filter)
        
        // Obter nível inicial da bateria
        updateBatteryLevel()
    }

    /**
     * Configurar ViewTreeObserver para mudanças de layout
     */
    private fun setupViewTreeObserver() {
        activity?.let { act ->
            viewTreeObserver = act.window.decorView.viewTreeObserver
            
            viewTreeObserver?.addOnGlobalLayoutListener {
                // Reagir a mudanças de layout (rotação, etc.)
                scope.launch {
                    delay(500) // Aguardar estabilização
                    if (isDetectionActive.get()) {
                        notifyCallbacks { it.onDetectionShouldResume() }
                    }
                }
            }
        }
    }

    /**
     * Atualizar nível da bateria e ajustar comportamento
     */
    private fun updateBatteryLevel() {
        val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as? BatteryManager
        val batteryLevel = batteryManager?.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY) ?: 100
        
        val isLowBattery = batteryLevel <= LOW_BATTERY_THRESHOLD
        val isCriticalBattery = batteryLevel <= CRITICAL_BATTERY_THRESHOLD
        
        // Ajustar intervalo de scan baseado na bateria
        val scanInterval = when {
            isCriticalBattery -> LOW_BATTERY_SCAN_INTERVAL * 2 // Muito lento
            isLowBattery -> LOW_BATTERY_SCAN_INTERVAL // Lento
            batteryLevel <= 50 -> THROTTLED_SCAN_INTERVAL // Moderado
            else -> DEFAULT_SCAN_INTERVAL // Normal
        }
        
        val isThrottled = scanInterval > DEFAULT_SCAN_INTERVAL
        
        updateLifecycleState { 
            it.copy(
                batteryLevel = batteryLevel,
                isLowBattery = isLowBattery,
                scanInterval = scanInterval,
                isThrottled = isThrottled
            )
        }
        
        // Notificar sobre otimização de bateria
        if (isLowBattery) {
            notifyCallbacks { it.onBatteryOptimizationRequired(batteryLevel) }
        }
    }

    /**
     * Atualizar estado do lifecycle
     */
    private fun updateLifecycleState(update: (LifecycleState) -> LifecycleState) {
        val newState = update(_lifecycleState.value)
        _lifecycleState.value = newState
        notifyCallbacks { it.onLifecycleStateChanged(newState) }
    }

    /**
     * Notificar todos os callbacks
     */
    private fun notifyCallbacks(action: (LifecycleCallback) -> Unit) {
        lifecycleCallbacks.forEach { callback ->
            try {
                action(callback)
            } catch (e: Exception) {
                // Ignorar erros em callbacks
            }
        }
    }

    /**
     * Limpeza de recursos
     */
    private fun cleanup() {
        try {
            context.unregisterReceiver(batteryReceiver)
        } catch (e: Exception) {
            // Receiver já foi removido
        }
        
        backgroundPauseJob?.cancel()
        scope.cancel()
        lifecycleCallbacks.clear()
        
        viewTreeObserver?.let { observer ->
            if (observer.isAlive) {
                // Remover listeners se necessário
            }
        }
        
        instance = null
    }

    /**
     * Receiver para mudanças de bateria
     */
    private inner class BatteryLevelReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                Intent.ACTION_BATTERY_CHANGED,
                Intent.ACTION_BATTERY_LOW,
                Intent.ACTION_BATTERY_OKAY -> {
                    updateBatteryLevel()
                }
            }
        }
    }

    /**
     * Obter estatísticas de performance
     */
    fun getPerformanceStats(): Map<String, Any> {
        val currentState = _lifecycleState.value
        
        return mapOf(
            "isActive" to currentState.isActive,
            "isBackgrounded" to currentState.isBackgrounded,
            "batteryLevel" to currentState.batteryLevel,
            "isLowBattery" to currentState.isLowBattery,
            "scanInterval" to currentState.scanInterval,
            "isThrottled" to currentState.isThrottled,
            "lastScanTime" to lastScanTime.get(),
            "callbackCount" to lifecycleCallbacks.size
        )
    }

    /**
     * Forçar otimização de bateria
     */
    fun forceBatteryOptimization(enable: Boolean) {
        val scanInterval = if (enable) {
            LOW_BATTERY_SCAN_INTERVAL
        } else {
            DEFAULT_SCAN_INTERVAL
        }
        
        updateLifecycleState { 
            it.copy(
                scanInterval = scanInterval,
                isThrottled = enable
            )
        }
    }
}
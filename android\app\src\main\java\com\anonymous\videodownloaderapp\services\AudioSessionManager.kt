package com.anonymous.videodownloaderapp.services

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.os.Build
import com.anonymous.videodownloaderapp.data.SettingsRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

class AudioSessionManager(private val context: Context) {

    private val audioManager: AudioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    private val settingsRepository = SettingsRepository(context)
    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    private var audioFocusRequest: AudioFocusRequest? = null

    suspend fun initialize() {
        // Load initial mode from settings and apply it
        val initialMode = settingsRepository.audioMixingMode.first()
        setAudioMode(initialMode)
    }

    suspend fun setAudioMode(mode: String) {
        val focusGain = when (mode) {
            "mix" -> AudioManager.AUDIOFOCUS_GAIN
            "duck" -> AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK
            "exclusive" -> AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
            else -> AudioManager.AUDIOFOCUS_GAIN // Default to mix
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val audioAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .setContentType(AudioAttributes.CONTENT_TYPE_MOVIE)
                .build()

            audioFocusRequest = AudioFocusRequest.Builder(focusGain)
                .setAudioAttributes(audioAttributes)
                .setAcceptsDelayedFocusGain(true)
                .setOnAudioFocusChangeListener { focusChange ->
                    // Handle focus change if needed
                }
                .build()

            audioFocusRequest?.let { request ->
                val res = audioManager.requestAudioFocus(request)
                if (res == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                    println("[AUDIO_SESSION] Audio focus granted for mode: $mode")
                } else {
                    println("[AUDIO_SESSION] Audio focus request failed for mode: $mode")
                }
            }
        } else {
            // For older Android versions, use deprecated requestAudioFocus
            val res = audioManager.requestAudioFocus(
                { focusChange ->
                    // Handle focus change if needed
                },
                AudioManager.STREAM_MUSIC,
                focusGain
            )
            if (res == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                println("[AUDIO_SESSION] Audio focus granted for mode (legacy): $mode")
            } else {
                println("[AUDIO_SESSION] Audio focus request failed for mode (legacy): $mode")
            }
        }

        settingsRepository.setAudioMixingMode(mode)
        println("[AUDIO_SESSION] Audio mode set successfully to: $mode")
    }

    suspend fun prepareForVideoPlayback() {
        // Re-apply the current audio mode to ensure proper focus
        val currentMode = settingsRepository.audioMixingMode.first()
        setAudioMode(currentMode)
    }

    suspend fun getAudioMixingMode(): String {
        return settingsRepository.audioMixingMode.first()
    }

    fun releaseAudioFocus() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            audioFocusRequest?.let { request ->
                audioManager.abandonAudioFocusRequest(request)
            }
        } else {
            audioFocusRequest?.let { audioManager.abandonAudioFocusRequest(it) }
        }
    }
}

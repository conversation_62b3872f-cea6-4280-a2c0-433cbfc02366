{"logs": [{"outputFile": "com.anonymous.videodownloaderapp-mergeDebugResources-79:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\366aa02d18db07ca95bf2d64251e5a94\\transformed\\appcompat-1.7.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,2776", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,2851"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "935,1038,1141,1252,1336,1436,1549,1626,1701,1794,1889,1984,2078,2180,2275,2372,2470,2566,2659,2739,2845,2944,3040,3145,3248,3350,3504,21011", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "1033,1136,1247,1331,1431,1544,1621,1696,1789,1884,1979,2073,2175,2270,2367,2465,2561,2654,2734,2840,2939,3035,3140,3243,3345,3499,3601,21086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ea0d069a154b88d6448f277841df52b5\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,989,1079,1148,1222,1293,1363,1441,1508", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,73,70,69,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,984,1074,1143,1217,1288,1358,1436,1503,1623"}, "to": {"startLines": "65,66,67,68,69,124,125,242,243,245,246,250,252,253,254,256,257,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5087,5180,5268,5364,5463,9578,9654,20587,20676,20835,20921,21251,21408,21482,21553,21724,21802,21869", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,73,70,69,77,66,119", "endOffsets": "5175,5263,5359,5458,5546,9649,9737,20671,20752,20916,21006,21315,21477,21548,21618,21797,21864,21984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd2b74cc75dcc4061ff406ef39af9f1c\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "55,56,57,58,59,60,61,255", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4060,4155,4257,4355,4454,4562,4667,21623", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "4150,4252,4350,4449,4557,4662,4783,21719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a1b6440c5b30b54fb1514c977cfe8efa\\transformed\\material-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1078,1141,1233,1297,1357,1449,1514,1577,1639,1706,1770,1824,1929,1988,2049,2103,2172,2291,2374,2451,2541,2625,2709,2845,2924,3008,3130,3216,3294,3348,3399,3465,3534,3608,3679,3755,3827,3904,3975,4049,4160,4251,4330,4417,4505,4577,4651,4736,4787,4866,4933,5014,5098,5160,5224,5287,5355,5462,5561,5660,5755,5813,5868,5946,6027,6106", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "261,360,452,533,635,715,813,935,1014,1073,1136,1228,1292,1352,1444,1509,1572,1634,1701,1765,1819,1924,1983,2044,2098,2167,2286,2369,2446,2536,2620,2704,2840,2919,3003,3125,3211,3289,3343,3394,3460,3529,3603,3674,3750,3822,3899,3970,4044,4155,4246,4325,4412,4500,4572,4646,4731,4782,4861,4928,5009,5093,5155,5219,5282,5350,5457,5556,5655,5750,5808,5863,5941,6022,6101,6189"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,70,71,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,244,248,249,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "769,3606,3705,3797,3878,3980,4788,4886,5008,5551,5610,9422,9514,9742,16076,16168,16233,16296,16358,16425,16489,16543,16648,16707,16768,16822,16891,17010,17093,17170,17260,17344,17428,17564,17643,17727,17849,17935,18013,18067,18118,18184,18253,18327,18398,18474,18546,18623,18694,18768,18879,18970,19049,19136,19224,19296,19370,19455,19506,19585,19652,19733,19817,19879,19943,20006,20074,20181,20280,20379,20474,20532,20757,21091,21172,21320", "endLines": "22,50,51,52,53,54,62,63,64,70,71,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,244,248,249,251", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "930,3700,3792,3873,3975,4055,4881,5003,5082,5605,5668,9509,9573,9797,16163,16228,16291,16353,16420,16484,16538,16643,16702,16763,16817,16886,17005,17088,17165,17255,17339,17423,17559,17638,17722,17844,17930,18008,18062,18113,18179,18248,18322,18393,18469,18541,18618,18689,18763,18874,18965,19044,19131,19219,19291,19365,19450,19501,19580,19647,19728,19812,19874,19938,20001,20069,20176,20275,20374,20469,20527,20582,20830,21167,21246,21403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\576ca5b088d8fb564e2e136af4f08d54\\transformed\\material3-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,327,434,566,682,778,891,1035,1159,1314,1399,1498,1588,1682,1796,1918,2022,2155,2282,2417,2589,2717,2835,2961,3081,3172,3270,3388,3527,3623,3731,3834,3967,4110,4216,4313,4393,4491,4583,4699,4783,4868,4969,5049,5134,5233,5333,5428,5528,5615,5719,5820,5924,6046,6126,6230", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "189,322,429,561,677,773,886,1030,1154,1309,1394,1493,1583,1677,1791,1913,2017,2150,2277,2412,2584,2712,2830,2956,3076,3167,3265,3383,3522,3618,3726,3829,3962,4105,4211,4308,4388,4486,4578,4694,4778,4863,4964,5044,5129,5228,5328,5423,5523,5610,5714,5815,5919,6041,6121,6225,6324"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9802,9941,10074,10181,10313,10429,10525,10638,10782,10906,11061,11146,11245,11335,11429,11543,11665,11769,11902,12029,12164,12336,12464,12582,12708,12828,12919,13017,13135,13274,13370,13478,13581,13714,13857,13963,14060,14140,14238,14330,14446,14530,14615,14716,14796,14881,14980,15080,15175,15275,15362,15466,15567,15671,15793,15873,15977", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "9936,10069,10176,10308,10424,10520,10633,10777,10901,11056,11141,11240,11330,11424,11538,11660,11764,11897,12024,12159,12331,12459,12577,12703,12823,12914,13012,13130,13269,13365,13473,13576,13709,13852,13958,14055,14135,14233,14325,14441,14525,14610,14711,14791,14876,14975,15075,15170,15270,15357,15461,15562,15666,15788,15868,15972,16071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\19dbdb4d446295775d71c6ca0010df0c\\transformed\\media3-ui-1.4.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,480,674,760,847,930,1018,1102,1170,1234,1332,1430,1495,1563,1629,1702,1822,1942,2060,2135,2217,2293,2361,2451,2542,2607,2671,2724,2784,2832,2893,2957,3028,3092,3157,3222,3281,3346,3398,3458,3541,3624,3676", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,51,59,82,82,51,63", "endOffsets": "280,475,669,755,842,925,1013,1097,1165,1229,1327,1425,1490,1558,1624,1697,1817,1937,2055,2130,2212,2288,2356,2446,2537,2602,2666,2719,2779,2827,2888,2952,3023,3087,3152,3217,3276,3341,3393,3453,3536,3619,3671,3735"}, "to": {"startLines": "2,11,15,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,575,5673,5759,5846,5929,6017,6101,6169,6233,6331,6429,6494,6562,6628,6701,6821,6941,7059,7134,7216,7292,7360,7450,7541,7606,8353,8406,8466,8514,8575,8639,8710,8774,8839,8904,8963,9028,9080,9140,9223,9306,9358", "endLines": "10,14,18,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,51,59,82,82,51,63", "endOffsets": "375,570,764,5754,5841,5924,6012,6096,6164,6228,6326,6424,6489,6557,6623,6696,6816,6936,7054,7129,7211,7287,7355,7445,7536,7601,7665,8401,8461,8509,8570,8634,8705,8769,8834,8899,8958,9023,9075,9135,9218,9301,9353,9417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c83662cb1041180272524de55f94ad06\\transformed\\media3-exoplayer-1.4.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,255,330,411,485,579,665", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "123,186,250,325,406,480,574,660,733"}, "to": {"startLines": "96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7670,7743,7806,7870,7945,8026,8100,8194,8280", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "7738,7801,7865,7940,8021,8095,8189,8275,8348"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c594a6b1ad90298d1781df40da764bd0\\transformed\\foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "259,260", "startColumns": "4,4", "startOffsets": "21989,22079", "endColumns": "89,88", "endOffsets": "22074,22163"}}]}]}
<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res"><file name="ic_alert_circle" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_alert_circle.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_arrow_forward" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_arrow_forward.xml" qualifiers="" type="drawable"/><file name="ic_checkmark_circle" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_checkmark_circle.xml" qualifiers="" type="drawable"/><file name="ic_cloud_download" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_cloud_download.xml" qualifiers="" type="drawable"/><file name="ic_cloud_download_outline" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_cloud_download_outline.xml" qualifiers="" type="drawable"/><file name="ic_download" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_download.xml" qualifiers="" type="drawable"/><file name="ic_globe" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_globe.xml" qualifiers="" type="drawable"/><file name="ic_globe_outline" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_globe_outline.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_settings_outline" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\ic_settings_outline.xml" qualifiers="" type="drawable"/><file name="rn_edit_text_material" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="splashscreen_logo" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable\splashscreen_logo.xml" qualifiers="" type="drawable"/><file name="splashscreen_logo" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable-hdpi\splashscreen_logo.png" qualifiers="hdpi-v4" type="drawable"/><file name="splashscreen_logo" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable-mdpi\splashscreen_logo.png" qualifiers="mdpi-v4" type="drawable"/><file name="splashscreen_logo" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable-xhdpi\splashscreen_logo.png" qualifiers="xhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable-xxhdpi\splashscreen_logo.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\drawable-xxxhdpi\splashscreen_logo.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="splashscreen_background">#FFFFFFFF</color></file><file path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">VideoDownloader</string><string name="download_channel_name">Downloads</string><string name="download_channel_desc">Background media downloads</string><string name="nova_aba">Nova aba</string><string name="digite_url">Digite uma URL…</string><string name="home">Home</string><string name="downloads">Downloads</string><string name="menu">Menu</string><string name="configuracoes">Configurações</string><string name="recarregar">Recarregar</string><string name="videos">Vídeos</string><string name="videos_detectados">Vídeos Detectados (%1$d)</string><string name="fechar">Fechar</string><string name="baixar">Baixar</string><string name="baixado">Baixado</string><string name="video_title">Vídeo - %1$s</string><string name="navegue_para_videos">Navegue para uma página com vídeos para detectá-los</string><string name="nenhum_video_encontrado">Nenhum vídeo encontrado</string><string name="atualizar">Atualizar</string><string name="inicio">Início</string></file><file path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    </style></file><file path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.App" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
(function() {
    // Configurações calibradas de thresholds
    var config = {
        minSide: 200,           // <PERSON><PERSON> mínimo de vídeo (px) - 200 padrão, ajustável por domínio
        minDuration: 15,        // Duração mínima VOD (segundos) - streams passam
        visFrac: 0.25,          // Fração de visibilidade mínima - 0.25 otimizado
        maxPerfEntries: 100,    // Cap de recursos Performance API - 50-100 balanceado
        maxHtmlMatches: 50,     // Máximo de matches regex HTML - controle de ruído
        maxQueryLength: 512,    // Query string máxima - 512 chars anti-tracker
        maxPageSize: 500000     // Página HTML máxima para scraping - 500KB limite
    };

    var candidates = new Map();         // Mapa de candidatos por chave normalizada
    var detectedUrls = new Set();       // Evita URLs exatamente duplicadas
    var processedElements = new WeakSet(); // Evita reprocessar elementos DOM
    
    // Lista expandida e otimizada de domínios de anúncio
    var adDomains = [
        'googleads.g.doubleclick.net',
        'googlesyndication.com',
        'googleadservices.com',
        'adservice.google.com',
        'pagead2.googlesyndication.com',
        'doubleclick.net',
        'admob.com',
        'youtube.com/get_midroll',
        'youtube.com/api/ads',
        'facebook.com/tr',
        'analytics.google.com',
        'google-analytics.com',
        'googletagmanager.com',
        'hotjar.com',
        'crazyegg.com',
        'ads.yahoo.com',
        'adsystem.amazon.com',
        'amazon-adsystem.com',
        'adsdk.amazon.com',
        'scorecardresearch.com',
        'quantserve.com',
        'outbrain.com',
        'taboola.com'
    ];
    
    var adPatterns = [
        /ads?\//i,
        /advertisement/i,
        /advert/i,
        /adsystem/i,
        /adservice/i,
        /doubleclick/i,
        /googlesyndication/i,
        /googleadservices/i,
        /analytics/i,
        /tracking/i,
        /tracker/i,
        /beacon/i,
        /pixel/i,
        /impression/i,
        /click/i,
        /get_midroll/i,
        /adview/i,
        /sponsor/i,
        /telemetry/i,
        /metrics/i,
        /stats/i
    ];
    
    // Função otimizada para verificar se URL é anúncio
    function isAdUrl(url) {
        if (!url) return true;
        
        var lowerUrl = url.toLowerCase();
        
        // Verificação rápida de domínios suspeitos
        for (var i = 0; i < adDomains.length; i++) {
            if (lowerUrl.indexOf(adDomains[i]) !== -1) {
                return true;
            }
        }
        
        // Verificação de padrões suspeitos
        for (var j = 0; j < adPatterns.length; j++) {
            if (adPatterns[j].test(lowerUrl)) {
                return true;
            }
        }
        
        // Filtros específicos para reduzir ruído
        if (lowerUrl.includes('gtag') || lowerUrl.includes('fbq') || 
            lowerUrl.includes('pixel') || lowerUrl.includes('tracker')) {
            return true;
        }
        
        return false;
    }
    
    // Função aprimorada para normalizar URL - remove parâmetros voláteis
    function normalizeUrl(url) {
        if (!url) return '';
        
        try {
            var urlObj = new URL(url);
            var params = new URLSearchParams(urlObj.search);
            
            // Manter apenas parâmetros relevantes para identificação de vídeo
            var keepParams = ['itag', 'quality', 'format', 'res', 'height', 'width', 'v', 'id'];
            var newParams = new URLSearchParams();
            
            keepParams.forEach(function(param) {
                if (params.has(param)) {
                    newParams.set(param, params.get(param));
                }
            });
            
            urlObj.search = newParams.toString();
            return urlObj.toString();
        } catch (e) {
            // Fallback mais robusto
            var baseUrl = url.split('?')[0];
            var query = url.split('?')[1];
            if (!query) return baseUrl;
            
            // Preservar apenas parâmetros essenciais na URL
            var essentialParams = [];
            var paramPairs = query.split('&');
            for (var i = 0; i < paramPairs.length; i++) {
                var pair = paramPairs[i];
                if (pair.includes('itag=') || pair.includes('quality=') || 
                    pair.includes('format=') || pair.includes('v=')) {
                    essentialParams.push(pair);
                }
            }
            
            return baseUrl + (essentialParams.length ? '?' + essentialParams.join('&') : '');
        }
    }
    
    // Função para detectar formato real do vídeo
    function detectFormat(url, mimeType) {
        if (mimeType) {
            if (mimeType.includes('mp4')) return 'mp4';
            if (mimeType.includes('webm')) return 'webm';
            if (mimeType.includes('avi')) return 'avi';
            if (mimeType.includes('mkv')) return 'mkv';
        }
        
        var lowerUrl = url.toLowerCase();
        if (lowerUrl.includes('.m3u8') || lowerUrl.includes('hls')) return 'hls';
        if (lowerUrl.includes('.mpd') || lowerUrl.includes('dash')) return 'dash';
        if (lowerUrl.includes('.mp4')) return 'mp4';
        if (lowerUrl.includes('.webm')) return 'webm';
        if (lowerUrl.includes('.avi')) return 'avi';
        if (lowerUrl.includes('.mkv')) return 'mkv';
        if (lowerUrl.includes('.mov')) return 'mov';
        if (lowerUrl.includes('.flv')) return 'flv';
        if (lowerUrl.includes('googlevideo')) return 'mp4';
        
        return 'unknown';
    }
    
    // Função para verificar se elemento está visível
    function isElementVisible(element) {
        if (!element || !element.getBoundingClientRect) return false;
        
        var rect = element.getBoundingClientRect();
        var windowHeight = window.innerHeight || document.documentElement.clientHeight;
        var windowWidth = window.innerWidth || document.documentElement.clientWidth;
        
        var visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);
        var visibleWidth = Math.min(rect.right, windowWidth) - Math.max(rect.left, 0);
        
        if (visibleHeight <= 0 || visibleWidth <= 0) return false;
        
        var visibleArea = visibleHeight * visibleWidth;
        var totalArea = rect.height * rect.width;
        
        return totalArea > 0 && (visibleArea / totalArea) >= config.visFrac;
    }
    
    // Função para verificar se vídeo tem DRM
    function hasDRM(videoElement) {
        try {
            return !!(videoElement && videoElement.mediaKeys);
        } catch (e) {
            return false;
        }
    }
    
    // Função principal para adicionar candidato
    function addCandidate(url, quality, format, title, score, videoElement) {
        if (!url || isAdUrl(url)) return;
        
        var normalizedUrl = normalizeUrl(url);
        var key = normalizedUrl;
        
        // Gerar chave única para agrupamento
        if (videoElement) {
            key = videoElement.id || videoElement.className || normalizedUrl;
        }
        
        var candidate = {
            url: url,
            normalizedUrl: normalizedUrl,
            quality: quality || 'Auto',
            format: format || 'unknown',
            title: title || 'Vídeo',
            score: score || 0,
            isDRM: videoElement ? hasDRM(videoElement) : false,
            isVisible: videoElement ? isElementVisible(videoElement) : true,
            width: videoElement ? videoElement.videoWidth : 0,
            height: videoElement ? videoElement.videoHeight : 0,
            duration: videoElement ? videoElement.duration : NaN
        };
        
        // Filtros de qualidade
        if (candidate.width && candidate.height) {
            if (candidate.width < config.minSide || candidate.height < config.minSide) {
                return; // Muito pequeno
            }
        }
        
        if (candidate.duration && !isNaN(candidate.duration) && 
            candidate.duration < config.minDuration) {
            return; // Muito curto
        }
        
        // Não listar conteúdo com DRM
        if (candidate.isDRM) return;
        
        // Agrupar por chave
        if (!candidates.has(key)) {
            candidates.set(key, {
                title: candidate.title,
                score: candidate.score,
                isVisible: candidate.isVisible,
                variants: []
            });
        }
        
        var group = candidates.get(key);
        
        // Verificar se já existe esta combinação de formato/qualidade
        var exists = group.variants.some(function(v) {
            return v.format === candidate.format && v.quality === candidate.quality;
        });
        
        if (!exists && !detectedUrls.has(url)) {
            group.variants.push({
                url: url,
                quality: candidate.quality,
                format: candidate.format,
                kind: candidate.format === 'hls' || candidate.format === 'dash' ? 'stream' : 'progressive'
            });
            detectedUrls.add(url);
            
            // Atualizar score do grupo
            group.score = Math.max(group.score, candidate.score);
        }
    }
    
    // 1. Detectar vídeos HTML5 com validação completa e metadados
    document.querySelectorAll('video').forEach(function(video) {
        // Visibilidade: evitar off-screen invisíveis
        if (!isElementVisible(video)) return;
        
        var baseScore = 12; // HTML5 visível tem prioridade maior
        var title = video.title || video.getAttribute('data-title') || document.title || 'Vídeo HTML5';
        
        // Garantir metadados quando disponíveis
        var hasMeta = video.readyState >= 1; // HAVE_METADATA
        
        if (hasMeta) {
            // Preferir currentSrc real
            var primarySrc = video.currentSrc || video.src;
            if (primarySrc) {
                var format = detectFormat(primarySrc, video.getAttribute('type') || video.getAttribute('data-type'));
                var quality = (video.videoHeight && video.videoWidth) ? (video.videoHeight + 'p') : 'Auto';
                addCandidate(primarySrc, quality, format, title, baseScore, video);
            }
        }
        
        // Sources dentro do <video>
        video.querySelectorAll('source').forEach(function(source) {
            if (!source.src) return;
            var label = source.getAttribute('data-quality') || source.getAttribute('label') ||
                        source.getAttribute('data-res') || source.getAttribute('size') || 'Auto';
            var fmt = detectFormat(source.src, source.type);
            addCandidate(source.src, label, fmt, title, baseScore - 1, video);
        });
    });
    
    // 2. Performance API com validação e classificação de streams
    try {
        if (window.performance && window.performance.getEntriesByType) {
            var entries = window.performance.getEntriesByType('resource')
                .slice(0, config.maxPerfEntries); // Limitar processamento para performance
            
            entries.forEach(function(entry) {
                var url = entry.name;
                if (!url) return;
                var lowerUrl = url.toLowerCase();
                
                // Identificar recursos de mídia reais
                var isVideo = lowerUrl.includes('.mp4') || lowerUrl.includes('.webm') || 
                              lowerUrl.includes('.m3u8') || lowerUrl.includes('.mpd') ||
                              lowerUrl.includes('videoplayback') || lowerUrl.includes('googlevideo');
                if (!isVideo) return;
                
                // Descartar recursos muito pequenos para evitar anúncios/trackers
                if (entry.transferSize && entry.transferSize < 50000) return; // < 50KB
                
                // Detectar formato corretamente
                var format = detectFormat(url);
                var quality = 'Auto';
                var score = 6;
                
                // YouTube: qualidade por itag quando disponível
                var itagMatch = url.match(/itag=(\d+)/);
                if (itagMatch) {
                    var itagNum = parseInt(itagMatch[1]);
                    if (itagNum >= 401 || itagNum === 337 || itagNum === 315 || itagNum === 313 || itagNum === 272) quality = '4K';
                    else if (itagNum >= 299 || itagNum === 303 || itagNum === 271 || itagNum === 137) quality = '1080p';
                    else if (itagNum >= 298 || itagNum === 302 || itagNum === 136) quality = '720p';
                    else if (itagNum >= 135) quality = '480p';
                    else quality = '360p';
                    score = 9;
                }
                
                addCandidate(url, quality, format, 'Vídeo detectado', score);
            });
        }
    } catch (e) {}
    
    // 3. YouTube específico: apenas URLs de mídia reais, sem window.location.href
    if (/(^|\.)youtube\.com$/.test(window.location.hostname)) {
        try {
            var ytInitialPlayerResponse = window.ytInitialPlayerResponse;
            if (ytInitialPlayerResponse && ytInitialPlayerResponse.streamingData) {
                var formats = ytInitialPlayerResponse.streamingData.formats || [];
                var adaptiveFormats = ytInitialPlayerResponse.streamingData.adaptiveFormats || [];
                var videoTitle = 'YouTube';
                try { videoTitle = ytInitialPlayerResponse.videoDetails && ytInitialPlayerResponse.videoDetails.title || videoTitle; } catch (e) {}
                
                var all = formats.concat(adaptiveFormats);
                all.forEach(function(f) {
                    if (!f || !f.url) return;
                    var q = f.qualityLabel || f.quality || 'Auto';
                    var fmt = f.mimeType ? detectFormat('', f.mimeType) : detectFormat(f.url);
                    addCandidate(f.url, q, fmt, videoTitle, 15);
                });
            }
        } catch (e) {}
    }
    
    // 4. Links diretos com validação/qualidade por texto
    document.querySelectorAll('a[href]').forEach(function(link) {
        var href = link.href;
        if (!href) return;
        var lowerHref = href.toLowerCase();
        
        // Exigir extensão clara para reduzir ruído
        var isVideoLink = /\.(mp4|webm|avi|mkv|mov|flv|m4v)(\?|$)/i.test(lowerHref);
        if (!isVideoLink) return;
        
        var q = 'Auto';
        var text = (link.textContent || '').toLowerCase();
        if (/(4k|2160p)/.test(text)) q = '4K';
        else if (/(1080p|1080)/.test(text)) q = '1080p';
        else if (/(720p|720)/.test(text)) q = '720p';
        else if (/(480p|480)/.test(text)) q = '480p';
        else if (/(360p|360)/.test(text)) q = '360p';
        
        var fmt2 = detectFormat(href);
        var t = (link.textContent || '').trim() || link.title || document.title || 'Link de vídeo';
        addCandidate(href, q, fmt2, t, 7);
    });
    
    // 5. HTML scraping limitado (último recurso com filtros fortes)
    try {
        var html = document.documentElement.outerHTML;
        var bodySize = html.length;
        if (bodySize <= config.maxPageSize) {
            var pattern = /https?:\/\/[^\s"'<>()]+\.(mp4|webm|avi|mkv|m4v|mov|flv)(\?[^\s"'<>()]{0,512})?/gi;
            var matches = html.match(pattern) || [];
            matches.slice(0, config.maxHtmlMatches).forEach(function(raw) {
                var cleanUrl = raw.replace(/["'<>()]/g, '');
                var query = cleanUrl.split('?')[1];
                if (query && query.length > config.maxQueryLength) return; // Ignorar query suspeita
                var fmt3 = detectFormat(cleanUrl);
                addCandidate(cleanUrl, 'Auto', fmt3, 'Vídeo encontrado', 2);
            });
        }
    } catch (e) {}
    
    // Processar e retornar resultados compatíveis (lista flat)
    var results = [];
    candidates.forEach(function(group) {
        if (!group || !group.variants || group.variants.length === 0) return;
        
        // Ordenar variantes por qualidade conhecida
        var order = { '4K': 5, '2160p': 5, '1440p': 4, '1080p': 3, '720p': 2, '480p': 1, '360p': 0, 'Auto': -1 };
        group.variants.sort(function(a, b) {
            return (order[b.quality] || -1) - (order[a.quality] || -1);
        });
        
        group.variants.forEach(function(v) {
            results.push({
                url: v.url,
                quality: v.quality,
                format: v.format,
                title: group.title
            });
        });
    });
    
    // Deduplicar saída final por URL normalizada e priorizar relevância
    var seen = new Set();
    var final = [];
    for (var i = 0; i < results.length && final.length < 100; i++) {
        var r = results[i];
        var norm = normalizeUrl(r.url);
        if (seen.has(norm)) continue;
        seen.add(norm);
        final.push(r);
    }
    
    return JSON.stringify(final);
 })()
package com.anonymous.videodownloaderapp.services

import android.content.Context
import android.graphics.Rect
import android.webkit.WebView
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * Detector de vídeo aprimorado com filtros anti-spam ultra-rigorosos
 * que elimina definitivamente a detecção excessiva (26→3-5 vídeos válidos).
 */
class EnhancedVideoDetector private constructor(
    private val context: Context
) {
    
    companion object {
        @Volatile
        private var INSTANCE: EnhancedVideoDetector? = null
        
        fun getInstance(context: Context): EnhancedVideoDetector {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: EnhancedVideoDetector(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // Filtros dimensionais expandidos
        private const val MIN_SIDE_PX = 200
        private const val MIN_AREA_PX = 40_000 // 200x200
        private const val MIN_ASPECT_RATIO = 0.625 // 16:10 invertido (mais permissivo que 16:9)
        private const val MAX_ASPECT_RATIO = 2.4 // Para vertical como Stories/TikTok
        
        // Filtros temporais
        private const val MIN_DURATION_SECONDS = 15
        private const val MIN_ENGAGEMENT_SECONDS = 3
        private const val ENGAGEMENT_TIMEOUT_SECONDS = 10
        private const val VISIBILITY_THRESHOLD = 0.25f // 25% do elemento visível
        private const val SUSTAINED_VISIBILITY_MS = 800L
        
        // Performance
        private const val SCROLL_DEBOUNCE_MS = 300L
        private const val HEAVY_PAGE_DEBOUNCE_MS = 500L
        private const val MAX_CANDIDATES_PER_PAGE = 50
    }
    
    data class VideoCandidate(
        val id: String,
        val url: String,
        val quality: String,
        val format: String,
        val width: Int?,
        val height: Int?,
        val duration: Double?,
        val isLive: Boolean,
        val hasDrm: Boolean,
        val score: Float,
        val source: DetectionSource,
        val element: ElementInfo?,
        val networkInfo: NetworkInfo?,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    data class ElementInfo(
        val tagName: String,
        val currentSrc: String?,
        val visibility: Float,
        val rect: Rect,
        val zIndex: Int,
        val opacity: Float,
        val hasUserInteraction: Boolean,
        val playbackState: PlaybackState
    )
    
    data class NetworkInfo(
        val requestId: String,
        val contentType: String?,
        val contentLength: Long?,
        val referer: String?
    )
    
    enum class DetectionSource {
        HTML5_VIDEO_ELEMENT,
        NETWORK_INTERCEPTOR,
        PERFORMANCE_API_FALLBACK,
        YOUTUBE_DATA,
        DIRECT_LINK_SCAN
    }
    
    enum class PlaybackState {
        PLAYING, PAUSED, ENDED, NOT_STARTED, LOADING
    }
    
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private val cache = NetworkVideoCache.getInstance(context)
    private val networkInterceptor = NetworkInterceptorService.getInstance()
    
    // Estado de detecção por página
    private val pageStates = ConcurrentHashMap<String, PageDetectionState>()
    private val detectionResults = MutableStateFlow<List<VideoCandidate>>(emptyList())
    
    data class PageDetectionState(
        val url: String,
        val candidates: MutableMap<String, VideoCandidate> = ConcurrentHashMap(),
        val lastScrollTime: AtomicLong = AtomicLong(0),
        var isBackgrounded: Boolean = false,
        val heavyPageMode: Boolean = false
    )
    
    val results: StateFlow<List<VideoCandidate>> = detectionResults
    
    init {
        setupNetworkListener()
    }
    
    private fun setupNetworkListener() {
        networkInterceptor.addListener(object : NetworkInterceptorService.MediaRequestListener {
            override fun onMediaRequestDetected(request: NetworkInterceptorService.MediaRequest) {
                scope.launch {
                    processNetworkRequest(request)
                }
            }
            
            override fun onManifestContentAvailable(request: NetworkInterceptorService.MediaRequest, content: String) {
                scope.launch {
                    processManifestContent(request, content)
                }
            }
        })
    }
    
    /**
     * Executar detecção completa em uma página
     */
    suspend fun detectVideos(
        webView: WebView?,
        pageUrl: String,
        isBackgrounded: Boolean = false
    ): List<VideoCandidate> = withContext(Dispatchers.Main) {
        
        if (webView == null) return@withContext emptyList()
        
        val pageState = pageStates.getOrPut(pageUrl) {
            PageDetectionState(url = pageUrl, isBackgrounded = isBackgrounded)
        }
        
        if (pageState.isBackgrounded) {
            return@withContext pageState.candidates.values.toList()
        }
        
        // Pipeline de detecção multi-fonte
        val candidates = mutableMapOf<String, VideoCandidate>()
        
        // 1. Detecção de elementos HTML5 <video> com verificação de visibilidade nativa
        val videoElements = detectHtml5VideoElements(webView)
        candidates.putAll(videoElements.associateBy { it.id })
        
        // 2. Correlação com requests de rede interceptados
        val networkCandidates = correlateWithNetworkRequests(pageUrl)
        networkCandidates.forEach { candidate ->
            val existing = candidates[candidate.id]
            if (existing == null || candidate.score > existing.score) {
                candidates[candidate.id] = candidate
            }
        }
        
        // 3. Fallback para Performance API (se intercepção falhar)
        if (candidates.size < 3) {
            val fallbackCandidates = detectViaPerformanceAPI(webView)
            fallbackCandidates.forEach { candidate ->
                if (!candidates.containsKey(candidate.id)) {
                    candidates[candidate.id] = candidate
                }
            }
        }
        
        // 4. Aplicar filtros ultra-rigorosos
        val filtered = applyRigorousFilters(candidates.values.toList())
        
        // 5. Agrupamento e deduplicação avançados
        val grouped = performAdvancedGrouping(filtered)
        
        // Atualizar estado da página
        pageState.candidates.clear()
        pageState.candidates.putAll(grouped.associateBy { it.id })
        
        // Atualizar resultado global
        updateGlobalResults()
        
        return@withContext grouped
    }
    
    private suspend fun detectHtml5VideoElements(webView: WebView): List<VideoCandidate> = 
        suspendCancellableCoroutine { continuation ->
            val script = """
                (function() {
                    const candidates = [];
                    const videos = document.querySelectorAll('video');
                    
                    videos.forEach((video, index) => {
                        try {
                            const rect = video.getBoundingClientRect();
                            const computedStyle = window.getComputedStyle(video);
                            const visibility = calculateVisibility(video, rect);
                            
                            // Aplicar filtros dimensionais imediatamente
                            if (!passesBasicFilters(rect, computedStyle)) return;
                            
                            const candidate = {
                                id: 'video_' + index + '_' + Date.now(),
                                url: video.currentSrc || video.src || '',
                                quality: inferQualityFromVideo(video, rect),
                                format: inferFormatFromUrl(video.currentSrc || video.src || ''),
                                width: rect.width,
                                height: rect.height,
                                duration: video.duration || null,
                                isLive: !isFinite(video.duration),
                                hasDrm: checkDrmProtection(video),
                                visibility: visibility,
                                hasUserInteraction: checkUserInteraction(video),
                                playbackState: getPlaybackState(video),
                                zIndex: parseInt(computedStyle.zIndex) || 0,
                                opacity: parseFloat(computedStyle.opacity) || 1
                            };
                            
                            candidates.push(candidate);
                        } catch (e) {
                            console.warn('Error processing video element:', e);
                        }
                    });
                    
                    function calculateVisibility(element, rect) {
                        if (rect.width === 0 || rect.height === 0) return 0;
                        
                        const viewportWidth = window.innerWidth;
                        const viewportHeight = window.innerHeight;
                        
                        const visibleLeft = Math.max(0, rect.left);
                        const visibleTop = Math.max(0, rect.top);
                        const visibleRight = Math.min(viewportWidth, rect.right);
                        const visibleBottom = Math.min(viewportHeight, rect.bottom);
                        
                        if (visibleLeft >= visibleRight || visibleTop >= visibleBottom) return 0;
                        
                        const visibleArea = (visibleRight - visibleLeft) * (visibleBottom - visibleTop);
                        const totalArea = rect.width * rect.height;
                        
                        return visibleArea / totalArea;
                    }
                    
                    function passesBasicFilters(rect, style) {
                        // Filtros dimensionais ultra-rigorosos
                        if (rect.width < $MIN_SIDE_PX || rect.height < $MIN_SIDE_PX) return false;
                        if (rect.width * rect.height < $MIN_AREA_PX) return false;
                        
                        // Aspect ratio check
                        const aspectRatio = rect.width / rect.height;
                        if (aspectRatio < $MIN_ASPECT_RATIO || aspectRatio > $MAX_ASPECT_RATIO) return false;
                        
                        // Elemento oculto
                        if (style.display === 'none' || style.visibility === 'hidden') return false;
                        if (parseFloat(style.opacity) < 0.1) return false;
                        
                        return true;
                    }
                    
                    function inferQualityFromVideo(video, rect) {
                        // Priorizar videoHeight/Width do elemento
                        if (video.videoHeight && video.videoHeight > 0) {
                            return normalizeHeightToLabel(video.videoHeight);
                        }
                        // Fallback para dimensões do elemento
                        return normalizeHeightToLabel(Math.round(rect.height));
                    }
                    
                    function normalizeHeightToLabel(height) {
                        const common = [2160, 1440, 1080, 720, 480, 360, 240, 144];
                        const closest = common.reduce((prev, curr) => 
                            Math.abs(curr - height) < Math.abs(prev - height) ? curr : prev
                        );
                        return closest + 'p';
                    }
                    
                    function inferFormatFromUrl(url) {
                        if (!url) return 'unknown';
                        const lower = url.toLowerCase();
                        if (lower.includes('.m3u8')) return 'hls';
                        if (lower.includes('.mpd')) return 'dash';
                        if (lower.includes('.webm')) return 'webm';
                        if (lower.includes('.ts')) return 'ts';
                        return 'mp4';
                    }
                    
                    function checkDrmProtection(video) {
                        try {
                            return !!(video.mediaKeys || 
                                     video.webkitKeys || 
                                     video.mozKeys ||
                                     document.querySelector('[data-drm]'));
                        } catch (e) {
                            return false;
                        }
                    }
                    
                    function checkUserInteraction(video) {
                        // Verificar se houve interação recente (click, touch, keyboard)
                        return !!(video.dataset.userInteracted || !video.autoplay);
                    }
                    
                    function getPlaybackState(video) {
                        if (video.ended) return 'ended';
                        if (video.paused) return 'paused';
                        if (video.currentTime > 0 && !video.paused && !video.ended && video.readyState > 2) return 'playing';
                        if (video.readyState < 3) return 'loading';
                        return 'not_started';
                    }
                    
                    return JSON.stringify(candidates);
                })();
            """.trimIndent()
            
            webView.evaluateJavascript(script) { result ->
                try {
                    val candidates = mutableListOf<VideoCandidate>()
                    if (result != null && result != "null") {
                        val cleanResult = result.removeSurrounding("\"").replace("\\", "")
                        val jsonArray = JSONArray(cleanResult)
                        
                        for (i in 0 until jsonArray.length()) {
                            val item = jsonArray.getJSONObject(i)
                            
                            val candidate = VideoCandidate(
                                id = item.getString("id"),
                                url = item.getString("url"),
                                quality = item.getString("quality"),
                                format = item.getString("format"),
                                width = item.optInt("width").takeIf { it > 0 },
                                height = item.optInt("height").takeIf { it > 0 },
                                duration = item.optDouble("duration").takeIf { it.isFinite() },
                                isLive = item.optBoolean("isLive", false),
                                hasDrm = item.optBoolean("hasDrm", false),
                                score = calculateVideoElementScore(item),
                                source = DetectionSource.HTML5_VIDEO_ELEMENT,
                                element = ElementInfo(
                                    tagName = "video",
                                    currentSrc = item.optString("url"),
                                    visibility = item.optDouble("visibility", 0.0).toFloat(),
                                    rect = Rect(0, 0, item.optInt("width"), item.optInt("height")),
                                    zIndex = item.optInt("zIndex", 0),
                                    opacity = item.optDouble("opacity", 1.0).toFloat(),
                                    hasUserInteraction = item.optBoolean("hasUserInteraction", false),
                                    playbackState = PlaybackState.valueOf(
                                        item.optString("playbackState", "NOT_STARTED").uppercase()
                                    )
                                ),
                                networkInfo = null
                            )
                            
                            candidates.add(candidate)
                        }
                    }
                    
                    continuation.resume(candidates, null)
                } catch (e: Exception) {
                    continuation.resume(emptyList(), null)
                }
            }
        }
    
    private fun calculateVideoElementScore(videoData: JSONObject): Float {
        var score = 0f
        
        // Pontuação por visibilidade (peso alto)
        val visibility = videoData.optDouble("visibility", 0.0).toFloat()
        score += visibility * 40f
        
        // Pontuação por interação do usuário
        if (videoData.optBoolean("hasUserInteraction", false)) {
            score += 25f
        }
        
        // Pontuação por estado de reprodução
        when (videoData.optString("playbackState", "")) {
            "playing" -> score += 30f
            "paused" -> score += 20f
            "loading" -> score += 10f
        }
        
        // Penalizar por DRM
        if (videoData.optBoolean("hasDrm", false)) {
            score -= 50f
        }
        
        // Pontuação por qualidade
        val quality = videoData.optString("quality", "")
        score += when {
            quality.contains("1080p") || quality.contains("2160p") -> 15f
            quality.contains("720p") -> 10f
            quality.contains("480p") -> 5f
            else -> 0f
        }
        
        return score.coerceIn(0f, 100f)
    }
    
    private suspend fun correlateWithNetworkRequests(pageUrl: String): List<VideoCandidate> {
        val requests = networkInterceptor.getRequestsByOrigin(pageUrl)
        return requests.mapNotNull { request ->
            when (request.mediaType) {
                NetworkInterceptorService.MediaType.HLS_MASTER -> {
                    processHlsManifest(request)
                }
                NetworkInterceptorService.MediaType.DASH_MANIFEST -> {
                    processDashManifest(request)
                }
                NetworkInterceptorService.MediaType.PROGRESSIVE_VIDEO,
                NetworkInterceptorService.MediaType.GOOGLE_VIDEO -> {
                    createProgressiveCandidate(request)
                }
                else -> null
            }
        }
    }
    
    private suspend fun processNetworkRequest(request: NetworkInterceptorService.MediaRequest) {
        // Processar em background para não bloquear detecção
        when (request.mediaType) {
            NetworkInterceptorService.MediaType.HLS_MASTER -> {
                processHlsManifest(request)?.let { candidate ->
                    addCandidateToResults(candidate)
                }
            }
            NetworkInterceptorService.MediaType.DASH_MANIFEST -> {
                processDashManifest(request)?.let { candidate ->
                    addCandidateToResults(candidate)
                }
            }
            else -> { /* Outros tipos processados durante detecção principal */ }
        }
    }
    
    suspend fun processManifestContent(
        request: NetworkInterceptorService.MediaRequest,
        content: String
    ) = withContext(Dispatchers.IO) {
        try {
            when (request.mediaType) {
                NetworkInterceptorService.MediaType.HLS_MASTER -> {
                    val parser = HlsManifestParser(context)
                    // ✅ Use the intercepted content directly
                    val qualities = parser.parseMasterContent(content, request.url)

                    cache.put(
                        url = request.url,
                        qualities = cache.convertFromUniversal(
                            qualities.map { hls ->
                                UniversalVideoUrlExtractor.Quality(
                                    label = hls.label,
                                    width = hls.width,
                                    height = hls.height,
                                    bandwidth = hls.bandwidth,
                                    codecs = hls.codecs,
                                    url = hls.url,
                                    type = hls.type
                                )
                            }
                        ),
                        manifestContent = content,
                        mediaType = NetworkVideoCache.MediaType.HLS,
                        isLive = content.contains("#EXT-X-ENDLIST").not()
                    )

                    android.util.Log.d("EnhancedVideoDetector", "Processed HLS manifest: ${qualities.size} qualities found")
                }
                NetworkInterceptorService.MediaType.DASH_MANIFEST -> {
                    val parser = DashManifestParser(context)
                    // ✅ Use the intercepted content directly
                    val qualities = parser.parseContent(content, request.url)

                    cache.put(
                        url = request.url,
                        qualities = cache.convertFromUniversal(
                            qualities.map { dash ->
                                UniversalVideoUrlExtractor.Quality(
                                    label = dash.label,
                                    width = dash.width,
                                    height = dash.height,
                                    bandwidth = dash.bandwidth,
                                    codecs = dash.codecs,
                                    url = dash.url,
                                    type = dash.type
                                )
                            }
                        ),
                        manifestContent = content,
                        mediaType = NetworkVideoCache.MediaType.DASH,
                        isLive = content.contains("type=\"dynamic\"", ignoreCase = true)
                    )

                    android.util.Log.d("EnhancedVideoDetector", "Processed DASH manifest: ${qualities.size} qualities found")
                }
                else -> {
                    android.util.Log.w("EnhancedVideoDetector", "Unknown media type: ${request.mediaType}")
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("EnhancedVideoDetector", "Error processing manifest content: ${e.message}", e)
        }
    }
    
    private suspend fun processHlsManifest(request: NetworkInterceptorService.MediaRequest): VideoCandidate? {
        return try {
            val cached = cache.get(request.url)
            val qualities = if (cached != null) {
                cached.qualities
            } else {
                val parser = HlsManifestParser(context)
                val parsed = parser.parseMaster(request.url)
                cache.convertFromUniversal(
                    parsed.map { hls ->
                        UniversalVideoUrlExtractor.Quality(
                            label = hls.label,
                            width = hls.width,
                            height = hls.height,
                            bandwidth = hls.bandwidth,
                            codecs = hls.codecs,
                            url = hls.url,
                            type = hls.type
                        )
                    }
                )
            }
            
            val bestQuality = qualities.maxByOrNull { it.height ?: 0 }
            if (bestQuality != null) {
                VideoCandidate(
                    id = "hls_${request.id}",
                    url = request.url,
                    quality = bestQuality.label,
                    format = "hls",
                    width = bestQuality.width,
                    height = bestQuality.height,
                    duration = null,
                    isLive = cached?.isLive ?: false,
                    hasDrm = false,
                    score = 85f, // Alta prioridade para manifests
                    source = DetectionSource.NETWORK_INTERCEPTOR,
                    element = null,
                    networkInfo = NetworkInfo(
                        requestId = request.id,
                        contentType = request.contentType,
                        contentLength = request.contentLength,
                        referer = request.referer
                    )
                )
            } else null
        } catch (e: Exception) {
            null
        }
    }
    
    private suspend fun processDashManifest(request: NetworkInterceptorService.MediaRequest): VideoCandidate? {
        return try {
            val cached = cache.get(request.url)
            val qualities = if (cached != null) {
                cached.qualities
            } else {
                val parser = DashManifestParser(context)
                val parsed = parser.parse(request.url)
                cache.convertFromUniversal(
                    parsed.map { dash ->
                        UniversalVideoUrlExtractor.Quality(
                            label = dash.label,
                            width = dash.width,
                            height = dash.height,
                            bandwidth = dash.bandwidth,
                            codecs = dash.codecs,
                            url = dash.url,
                            type = dash.type
                        )
                    }
                )
            }
            
            val bestQuality = qualities.maxByOrNull { it.height ?: 0 }
            if (bestQuality != null) {
                VideoCandidate(
                    id = "dash_${request.id}",
                    url = request.url,
                    quality = bestQuality.label,
                    format = "dash",
                    width = bestQuality.width,
                    height = bestQuality.height,
                    duration = null,
                    isLive = cached?.isLive ?: false,
                    hasDrm = false,
                    score = 85f, // Alta prioridade para manifests
                    source = DetectionSource.NETWORK_INTERCEPTOR,
                    element = null,
                    networkInfo = NetworkInfo(
                        requestId = request.id,
                        contentType = request.contentType,
                        contentLength = request.contentLength,
                        referer = request.referer
                    )
                )
            } else null
        } catch (e: Exception) {
            null
        }
    }
    
    private fun createProgressiveCandidate(request: NetworkInterceptorService.MediaRequest): VideoCandidate? {
        val contentLength = request.contentLength ?: 0
        if (contentLength < 1_000_000L) return null // Filtrar arquivos muito pequenos
        
        val inferredQuality = inferQualityFromUrl(request.url) ?: "Auto"
        val format = inferFormatFromUrl(request.url) ?: "mp4"
        
        return VideoCandidate(
            id = "progressive_${request.id}",
            url = request.url,
            quality = inferredQuality,
            format = format,
            width = null,
            height = extractHeightFromLabel(inferredQuality),
            duration = null,
            isLive = false,
            hasDrm = false,
            score = 70f,
            source = DetectionSource.NETWORK_INTERCEPTOR,
            element = null,
            networkInfo = NetworkInfo(
                requestId = request.id,
                contentType = request.contentType,
                contentLength = request.contentLength,
                referer = request.referer
            )
        )
    }
    
    private suspend fun detectViaPerformanceAPI(webView: WebView): List<VideoCandidate> =
        suspendCancellableCoroutine { continuation ->
            val script = """
                (function() {
                    const candidates = [];
                    
                    try {
                        const entries = performance.getEntriesByType('resource');
                        const videoEntries = entries.filter(entry => {
                            const name = entry.name.toLowerCase();
                            return name.includes('.m3u8') || 
                                   name.includes('.mpd') || 
                                   name.includes('.mp4') || 
                                   name.includes('.webm') ||
                                   name.includes('googlevideo.com') ||
                                   name.includes('/videoplayback');
                        });
                        
                        videoEntries.forEach((entry, index) => {
                            if (entry.transferSize > 100000) { // Filtrar por tamanho
                                candidates.push({
                                    id: 'perf_' + index + '_' + Date.now(),
                                    url: entry.name,
                                    quality: inferQualityFromUrl(entry.name),
                                    format: inferFormatFromUrl(entry.name),
                                    transferSize: entry.transferSize
                                });
                            }
                        });
                        
                    } catch (e) {
                        console.warn('Performance API fallback failed:', e);
                    }
                    
                    function inferQualityFromUrl(url) {
                        const matches = url.match(/(\\d{3,4})p/i);
                        if (matches) return matches[0];
                        
                        if (url.includes('2160') || url.includes('4k')) return '2160p';
                        if (url.includes('1440')) return '1440p';
                        if (url.includes('1080')) return '1080p';
                        if (url.includes('720')) return '720p';
                        if (url.includes('480')) return '480p';
                        if (url.includes('360')) return '360p';
                        
                        return 'Auto';
                    }
                    
                    function inferFormatFromUrl(url) {
                        if (url.includes('.m3u8')) return 'hls';
                        if (url.includes('.mpd')) return 'dash';
                        if (url.includes('.webm')) return 'webm';
                        return 'mp4';
                    }
                    
                    return JSON.stringify(candidates);
                })();
            """.trimIndent()
            
            webView.evaluateJavascript(script) { result ->
                try {
                    val candidates = mutableListOf<VideoCandidate>()
                    if (result != null && result != "null") {
                        val cleanResult = result.removeSurrounding("\"").replace("\\", "")
                        val jsonArray = JSONArray(cleanResult)
                        
                        for (i in 0 until jsonArray.length()) {
                            val item = jsonArray.getJSONObject(i)
                            
                            val candidate = VideoCandidate(
                                id = item.getString("id"),
                                url = item.getString("url"),
                                quality = item.getString("quality"),
                                format = item.getString("format"),
                                width = null,
                                height = extractHeightFromLabel(item.getString("quality")),
                                duration = null,
                                isLive = false,
                                hasDrm = false,
                                score = 50f, // Pontuação média para fallback
                                source = DetectionSource.PERFORMANCE_API_FALLBACK,
                                element = null,
                                networkInfo = null
                            )
                            
                            candidates.add(candidate)
                        }
                    }
                    
                    continuation.resume(candidates, null)
                } catch (e: Exception) {
                    continuation.resume(emptyList(), null)
                }
            }
        }
    
    private fun applyRigorousFilters(candidates: List<VideoCandidate>): List<VideoCandidate> {
        return candidates.filter { candidate ->
            // Filtro de DRM - remove conteúdo protegido
            if (candidate.hasDrm) return@filter false
            
            // Filtro dimensional para elementos HTML
            if (candidate.element != null) {
                val rect = candidate.element.rect
                val width = rect.width()
                val height = rect.height()
                
                // Critérios ultra-rigorosos
                if (width < MIN_SIDE_PX || height < MIN_SIDE_PX) return@filter false
                if (width * height < MIN_AREA_PX) return@filter false
                
                val aspectRatio = width.toFloat() / height.toFloat()
                if (aspectRatio < MIN_ASPECT_RATIO || aspectRatio > MAX_ASPECT_RATIO) return@filter false
                
                // Verificar visibilidade sustentada
                if (candidate.element.visibility < VISIBILITY_THRESHOLD) return@filter false
                
                // Z-index check para elementos sobrepostos
                if (candidate.element.zIndex < 0) return@filter false
                
                // Opacidade
                if (candidate.element.opacity < 0.5f) return@filter false
            }
            
            // Filtro temporal para elementos com duração
            if (candidate.duration != null && candidate.duration < MIN_DURATION_SECONDS && !candidate.isLive) {
                return@filter false
            }
            
            // Filtro de engagement para elementos não-interativos
            if (candidate.element?.hasUserInteraction == false && 
                candidate.element.playbackState == PlaybackState.PLAYING) {
                // Anti-autoplay spam
                return@filter false
            }
            
            // Filtro de URL - verificar se não é anúncio/tracker
            if (isAdOrTrackerUrl(candidate.url)) return@filter false
            
            // Filtro de tamanho de arquivo para progressivos
            if (candidate.networkInfo?.contentLength != null && 
                candidate.networkInfo.contentLength < 500_000L) {
                return@filter false
            }
            
            // Filtro de pontuação mínima
            if (candidate.score < 20f) return@filter false
            
            return@filter true
        }
    }
    
    private fun performAdvancedGrouping(candidates: List<VideoCandidate>): List<VideoCandidate> {
        if (candidates.isEmpty()) return emptyList()
        
        // Agrupar por chave de conteúdo multi-camada
        val groups = candidates.groupBy { generateContentKey(it) }
        
        return groups.values.mapNotNull { group ->
            // Para cada grupo, escolher o melhor candidato
            val sorted = group.sortedWith(
                compareByDescending<VideoCandidate> { it.score }
                    .thenByDescending { it.source.ordinal }
                    .thenByDescending { extractHeightFromLabel(it.quality) ?: 0 }
            )
            
            sorted.firstOrNull()
        }.sortedWith(
            compareByDescending<VideoCandidate> { it.score }
                .thenByDescending { extractHeightFromLabel(it.quality) ?: 0 }
        ).take(MAX_CANDIDATES_PER_PAGE)
    }
    
    private fun generateContentKey(candidate: VideoCandidate): String {
        return when {
            candidate.format == "hls" || candidate.format == "dash" -> {
                // Para manifests: usar URL normalizada
                normalizeManifestUrl(candidate.url)
            }
            else -> {
                // Para progressivos: hostname + dimensões + duração arredondada
                val uri = android.net.Uri.parse(candidate.url)
                val hostname = uri.host ?: "unknown"
                val dimensions = "${candidate.width ?: 0}x${candidate.height ?: 0}"
                val duration = candidate.duration?.toInt() ?: 0
                "$hostname|$dimensions|$duration"
            }
        }
    }
    
    private fun addCandidateToResults(candidate: VideoCandidate) {
        scope.launch {
            val current = detectionResults.value.toMutableList()
            
            // Verificar se já existe candidato similar
            val existingIndex = current.indexOfFirst { 
                generateContentKey(it) == generateContentKey(candidate) 
            }
            
            if (existingIndex >= 0) {
                // Substituir se o novo tem pontuação maior
                if (candidate.score > current[existingIndex].score) {
                    current[existingIndex] = candidate
                }
            } else {
                current.add(candidate)
            }
            
            // Limitar tamanho e re-ordenar
            val filtered = current.sortedByDescending { it.score }.take(MAX_CANDIDATES_PER_PAGE)
            detectionResults.value = filtered
        }
    }
    
    private fun updateGlobalResults() {
        val allCandidates = pageStates.values.flatMap { it.candidates.values }
        val grouped = performAdvancedGrouping(allCandidates)
        detectionResults.value = grouped
    }
    
    private fun inferQualityFromUrl(url: String): String? {
        val lower = url.lowercase()
        val heightMatch = Regex("(\\d{3,4})p").find(lower)
        if (heightMatch != null) return heightMatch.value
        
        return when {
            lower.contains("2160") || lower.contains("4k") -> "2160p"
            lower.contains("1440") -> "1440p"
            lower.contains("1080") -> "1080p"
            lower.contains("720") -> "720p"
            lower.contains("480") -> "480p"
            lower.contains("360") -> "360p"
            lower.contains("240") -> "240p"
            else -> null
        }
    }
    
    private fun inferFormatFromUrl(url: String): String? {
        val lower = url.lowercase()
        return when {
            lower.contains(".m3u8") -> "hls"
            lower.contains(".mpd") -> "dash"
            lower.contains(".webm") -> "webm"
            lower.contains(".ts") -> "ts"
            else -> "mp4"
        }
    }
    
    private fun extractHeightFromLabel(label: String?): Int? {
        if (label.isNullOrBlank()) return null
        return Regex("(\\d{3,4})p").find(label)?.groupValues?.get(1)?.toIntOrNull()
    }
    
    private fun isAdOrTrackerUrl(url: String): Boolean {
        val lower = url.lowercase()
        val adDomains = listOf(
            "doubleclick.net", "googleadservices.com", "googlesyndication.com",
            "adsystem", "adservice", "adserver", "scorecardresearch.com"
        )
        val adPaths = listOf("/ads", "/ad_break", "/anuncio", "/advert", "/beacon")
        
        return adDomains.any { lower.contains(it) } || adPaths.any { lower.contains(it) }
    }
    
    private fun normalizeManifestUrl(url: String): String {
        val volatileParams = setOf("expires", "signature", "token", "ts", "range", "rn", "mm", "mn", "ms", "mv", "mvi", "ei", "cpn", "gir", "n")
        
        return try {
            val uri = android.net.Uri.parse(url)
            val builder = uri.buildUpon().clearQuery()
            
            uri.queryParameterNames.forEach { param ->
                if (param !in volatileParams) {
                    builder.appendQueryParameter(param, uri.getQueryParameter(param))
                }
            }
            
            builder.build().toString()
        } catch (e: Exception) {
            url
        }
    }
    
    /**
     * Limpar estado de uma página
     */
    fun clearPage(pageUrl: String) {
        pageStates.remove(pageUrl)
        updateGlobalResults()
    }
    
    /**
     * Notificar que app foi para background
     */
    fun onBackgrounded() {
        pageStates.values.forEach { it.isBackgrounded = true }
    }
    
    /**
     * Notificar que app voltou para foreground
     */
    fun onForegrounded() {
        pageStates.values.forEach { it.isBackgrounded = false }
    }
    
    /**
     * Obter estatísticas de detecção
     */
    fun getDetectionStats(): Map<String, Any> {
        val totalCandidates = pageStates.values.sumOf { it.candidates.size }
        val sourceBreakdown = detectionResults.value.groupingBy { it.source }.eachCount()
        val formatBreakdown = detectionResults.value.groupingBy { it.format }.eachCount()
        
        return mapOf(
            "totalPages" to pageStates.size,
            "totalCandidates" to totalCandidates,
            "activeCandidates" to detectionResults.value.size,
            "sourceBreakdown" to sourceBreakdown,
            "formatBreakdown" to formatBreakdown,
            "cacheStats" to cache.getStats(),
            "networkStats" to networkInterceptor.getStats()
        )
    }
}
package com.anonymous.videodownloaderapp.services

import android.content.Context
import android.net.Uri
import android.webkit.URLUtil
import com.anonymous.videodownloaderapp.data.DownloadRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.launch
import java.net.URLDecoder
import java.util.Locale

class UniversalVideoUrlExtractor(
    private val context: Context? = null,
    private val repo: DownloadRepository,
    private val scope: CoroutineScope = CoroutineScope(Dispatchers.IO)
) {
    // Media3-based enqueues for manifests: build DownloadRequest and start DownloadService
    fun enqueueHls(context: android.content.Context, manifestUrl: String, title: String? = null, desiredHeight: Int? = null): Result =
        runCatching {
            val norm = normalizeUrl(manifestUrl) ?: return Result(false, reason = "Invalid URL")
            val req = Media3DownloadHelper.buildHlsRequest(norm, title, desiredHeight)
            val dm = Media3DownloadHelper.getDownloadManager(context)
            dm.addDownload(req)
            Media3DownloadService.start(context)
            Result(true, normalizedUrl = norm, title = title, mime = "application/vnd.apple.mpegurl")
        }.getOrElse {
            Result(false, reason = it.message ?: "enqueueHls error")
        }

    fun enqueueDash(context: android.content.Context, manifestUrl: String, title: String? = null, desiredHeight: Int? = null): Result =
        runCatching {
            val norm = normalizeUrl(manifestUrl) ?: return Result(false, reason = "Invalid URL")
            val req = Media3DownloadHelper.buildDashRequest(norm, title, desiredHeight)
            val dm = Media3DownloadHelper.getDownloadManager(context)
            dm.addDownload(req)
            Media3DownloadService.start(context)
            Result(true, normalizedUrl = norm, title = title, mime = "application/dash+xml")
        }.getOrElse {
            Result(false, reason = it.message ?: "enqueueDash error")
        }

    data class Result(
        val accepted: Boolean,
        val normalizedUrl: String? = null,
        val reason: String? = null,
        val title: String? = null,
        val mime: String? = null
    )

    // Normalized cross-parser quality structure
    data class Quality(
        val label: String,              // "1080p"
        val width: Int? = null,
        val height: Int? = null,
        val bandwidth: Long? = null,    // bps
        val codecs: String? = null,
        val url: String,
        val type: String                // "HLS" | "DASH" | "PROGRESSIVE"
    )

    fun tryEnqueue(rawUrl: String, pageTitle: String? = null): Result =
        runCatching {
            val norm = normalizeUrl(rawUrl) ?: return Result(false, reason = "Invalid URL")
            val mime = guessMime(norm) ?: return Result(false, reason = "Unsupported media type")
            val title = buildTitle(norm, pageTitle)
            scope.launch { repo.enqueue(url = norm, title = title, mimeType = mime) }
            Result(true, normalizedUrl = norm, title = title, mime = mime)
        }.getOrElse {
            Result(false, reason = it.message ?: "Extractor error")
        }

    // New: extract list of qualities using HLS/DASH parsers or progressive inference
    suspend fun extractQualities(rawUrl: String): List<Quality> = withContext(Dispatchers.IO) {
        val norm = normalizeUrl(rawUrl) ?: return@withContext emptyList()
        val lower = norm.lowercase(Locale.US)

        return@withContext when {
            lower.contains(".m3u8") -> {
                val hls = HlsManifestParser(context)
                val list = hls.parseMaster(norm)
                list.map {
                    Quality(
                        label = it.label,
                        width = it.width,
                        height = it.height,
                        bandwidth = it.bandwidth,
                        codecs = it.codecs,
                        url = it.url,
                        type = "HLS"
                    )
                }.sortedByDescending { extractHeight(it.label) ?: 0 }
            }
            lower.contains(".mpd") -> {
                val dash = DashManifestParser(context)
                val list = dash.parse(norm)
                list.map {
                    Quality(
                        label = it.label,
                        width = it.width,
                        height = it.height,
                        bandwidth = it.bandwidth,
                        codecs = it.codecs,
                        url = it.url,
                        type = "DASH"
                    )
                }.sortedByDescending { extractHeight(it.label) ?: 0 }
            }
            lower.contains(".mp4") || lower.contains(".webm") || lower.contains(".m4v") || lower.contains(".3gp") || lower.contains(".ogv") || lower.contains(".ts") -> {
                // Progressive: attempt to infer a single quality from the URL text
                val label = inferLabelFromString(norm) ?: "Auto"
                listOf(
                    Quality(
                        label = label,
                        width = null,
                        height = extractHeight(label),
                        bandwidth = null,
                        codecs = null,
                        url = norm,
                        type = "PROGRESSIVE"
                    )
                )
            }
            else -> {
                emptyList()
            }
        }
    }

    private fun isAdOrTracker(url: String): Boolean {
        val lower = url.lowercase(Locale.US)
        val blockedHosts = listOf(
            "doubleclick.net", "googleadservices.com", "googlesyndication.com",
            "adsystem", "adservice", "adserver", "scorecardresearch.com",
            "google-analytics.com", "facebook.com/tr", "imasdk.googleapis.com"
        )
        val blockedPathSegments = listOf(
            "/ads", "/ad_break", "/anuncio", "/advert", "/beacon", "/pixel", "/metrics",
            "videoplayback?adformat="
        )
        val host = try { Uri.parse(url).host ?: "" } catch (e: Exception) { "" }

        return blockedHosts.any { host.contains(it) } || blockedPathSegments.any { lower.contains(it) }
    }

    private fun normalizeUrl(raw: String): String? {
        val trimmed = raw.trim()
        if (trimmed.isEmpty() || isAdOrTracker(trimmed)) return null

        val unescaped = runCatching { URLDecoder.decode(trimmed, "UTF-8") }.getOrElse { trimmed }
        if (isAdOrTracker(unescaped)) return null


        val uri = runCatching { Uri.parse(unescaped) }.getOrNull() ?: return null
        val candidates = mutableListOf(unescaped)

        uri.queryParameterNames.forEach {
            val v = uri.getQueryParameter(it)
            if (v != null && URLUtil.isValidUrl(v) && !isAdOrTracker(v)) {
                candidates += v
            }
        }

        return candidates.firstOrNull { looksLikeVideoUrl(it) } ?: candidates.firstOrNull { URLUtil.isValidUrl(it) && !isAdOrTracker(it) }
    }

    private fun looksLikeVideoUrl(u: String): Boolean {
        val lower = u.lowercase(Locale.US)
        return lower.contains(".m3u8") ||
               lower.contains(".mp4") ||
               lower.contains(".webm") ||
               lower.contains(".mpd") ||
               lower.contains(".ts")
    }

    private fun guessMime(u: String): String? {
        val lower = u.lowercase(Locale.US)
        return when {
            lower.contains(".m3u8") -> "application/vnd.apple.mpegurl"
            lower.contains(".mp4") -> "video/mp4"
            lower.contains(".webm") -> "video/webm"
            lower.contains(".ts") -> "video/mp2t"
            lower.contains(".mpd") -> "application/dash+xml"
            else -> null
        }
    }

    private fun buildTitle(url: String, pageTitle: String?): String {
        val last = url.substringAfterLast('/').substringBefore('?').substringBefore('#')
        val base = if (last.isBlank()) pageTitle ?: "video" else last
        return base.take(120)
    }

    private fun extractHeight(label: String?): Int? {
        if (label.isNullOrBlank()) return null
        return Regex("(\\d{3,4})p", RegexOption.IGNORE_CASE).find(label)?.groupValues?.getOrNull(1)?.toIntOrNull()
    }

    private fun inferLabelFromString(s: String): String? {
        val lower = s.lowercase(Locale.US)
        Regex("(\\d{3,4})p").find(lower)?.let { return "${it.groupValues[1]}p" }
        if (listOf("2160", "4k", "uhd").any { lower.contains(it) }) return "2160p"
        if (lower.contains("1440")) return "1440p"
        if (listOf("1080", "fhd").any { lower.contains(it) }) return "1080p"
        if (listOf("720", "hd").any { lower.contains(it) }) return "720p"
        if (listOf("480", "sd").any { lower.contains(it) }) return "480p"
        if (lower.contains("360")) return "360p"
        if (lower.contains("240")) return "240p"
        if (lower.contains("144")) return "144p"
        return null
    }
}
# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx512m -XX:MaxMetaspaceSize=256m
org.gradle.jvmargs=-Xmx2048m -XX:MaxMetaspaceSize=512m

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true

# Enable Kotlin in Android projects
kotlin.code.style=official

# Set file encoding to UTF-8
file.encoding=UTF-8
kotlin.compiler.execution.strategy=in-process

# R8 is enabled by default in Android Gradle Plugin 7.0+

# Set to true to enable Fabric (New Architecture)
# react.newArchEnabled=false

# Set to true to enable Hermes (New Architecture)
# react.hermesEnabled=false

# Removido: Expo related image flags
# expo.gif.enabled=true
# expo.webp.enabled=true
# expo.webp.animated=true

# Removido: Use Legacy Packaging flag
# expo.useLegacyPackaging=false

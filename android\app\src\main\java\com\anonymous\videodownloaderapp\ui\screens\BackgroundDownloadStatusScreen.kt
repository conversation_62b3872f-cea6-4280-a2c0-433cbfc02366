package com.anonymous.videodownloaderapp.ui.screens

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.anonymous.videodownloaderapp.R
import com.anonymous.videodownloaderapp.data.DownloadItem
import com.anonymous.videodownloaderapp.services.DownloadTracker
import kotlinx.coroutines.launch

@Composable
fun BackgroundDownloadsScreen(downloadTracker: DownloadTracker) {
    val downloads by downloadTracker.downloads.collectAsState(initial = emptyList())
    val coroutineScope = rememberCoroutineScope()

    val activeDownloads = downloads.filter { it.status == DownloadItem.Status.Running || it.status == DownloadItem.Status.Queued }
    val completedDownloads = downloads.filter { it.status == DownloadItem.Status.Completed }
    val failedDownloads = downloads.filter { it.status == DownloadItem.Status.Failed || it.status == DownloadItem.Status.Canceled }

    Column(modifier = Modifier.fillMaxSize().padding(16.dp)) {
        Text(text = "Downloads em Segundo Plano", style = androidx.compose.material3.MaterialTheme.typography.titleLarge)

        if (activeDownloads.isNotEmpty()) {
            Text(text = "Ativos", style = androidx.compose.material3.MaterialTheme.typography.titleMedium)
            LazyColumn {
                items(activeDownloads) { download ->
                    DownloadItemRow(download = download) {
                        coroutineScope.launch { downloadTracker.removeDownload(download.id) }
                    }
                }
            }
        }

        if (completedDownloads.isNotEmpty()) {
            Text(text = "Concluídos", style = androidx.compose.material3.MaterialTheme.typography.titleMedium)
            LazyColumn {
                items(completedDownloads) { download ->
                    DownloadItemRow(download = download) {
                        coroutineScope.launch { downloadTracker.removeDownload(download.id) }
                    }
                }
            }
        }

        if (failedDownloads.isNotEmpty()) {
            Text(text = "Falhos", style = androidx.compose.material3.MaterialTheme.typography.titleMedium)
            LazyColumn {
                items(failedDownloads) { download ->
                    DownloadItemRow(download = download) {
                        coroutineScope.launch { downloadTracker.removeDownload(download.id) }
                    }
                }
            }
        }

        if (downloads.isEmpty()) {
            Text(text = "Nenhum download em andamento.")
        }

        if (completedDownloads.isNotEmpty() || failedDownloads.isNotEmpty()) {
            Button(onClick = {
                coroutineScope.launch { downloadTracker.clearCompletedAndFailedDownloads() }
            }) {
                Text("Limpar Concluídos e Falhos")
            }
        }
    }
}

@Composable
fun DownloadItemRow(download: DownloadItem, onCancel: () -> Unit) {
    Row(modifier = Modifier.fillMaxWidth().padding(8.dp)) {
        val iconRes = when (download.status) {
            DownloadItem.Status.Running, DownloadItem.Status.Queued -> R.drawable.ic_cloud_download
            DownloadItem.Status.Completed -> R.drawable.ic_checkmark_circle
            else -> R.drawable.ic_alert_circle
        }
        val iconColor = when (download.status) {
            DownloadItem.Status.Running, DownloadItem.Status.Queued -> androidx.compose.ui.graphics.Color(0xFF6c5ce7)
            DownloadItem.Status.Completed -> androidx.compose.ui.graphics.Color(0xFF4CAF50)
            else -> androidx.compose.ui.graphics.Color(0xFFff4444)
        }

        Icon(painter = painterResource(id = iconRes), contentDescription = download.status.name, tint = iconColor)
        Column(modifier = Modifier.weight(1f).padding(start = 8.dp)) {
            Text(text = download.title ?: "")
            Text(text = "Status: ${download.status.name}")
            if (download.status == DownloadItem.Status.Running) {
                Text(text = "Progresso: ${download.progress}%")
            }
            if (download.totalBytes > 0) {
                Text(text = "${(download.bytesDownloaded / (1024 * 1024))} MB / ${(download.totalBytes / (1024 * 1024))} MB")
            }
        }
        if (download.status == DownloadItem.Status.Running || download.status == DownloadItem.Status.Queued) {
            Button(onClick = onCancel) {
                Text("Cancelar")
            }
        }
    }
}

// Serviço avançado para extração de vídeos com pipeline otimizado
// Integra coleta DOM + Performance API + filtros inteligentes + scoring

// ✅ Integrações opcionais - carregadas dinamicamente se disponíveis
// import UrlResolutionService from './UrlResolutionService.js';
// import VideoDetectionPerformanceService from './VideoDetectionPerformanceService.js';

class VideoExtractorService {
  
  // Configurações e thresholds (futuramente via Remote Config)
  static CONFIG = {
    thresholds: {
      minSide: 200,               // Largura/altura mínima em pixels
      minDurationVOD: 15,         // Duração mínima para VOD em segundos
      visFraction: 0.25,          // Fração mínima de visibilidade (25%)
      minScore: 3,                // Score mínimo para considerar válido
      highQualityScore: 7         // Score para classificar como alta qualidade
    },
    timeouts: {
      metadataWait: 350,          // Tempo para aguardar loadedmetadata
      performanceDelay: 500,      // Delay antes de coletar Performance API
      processTimeout: 2000        // Timeout máximo para processamento
    },
    limits: {
      maxPerformanceEntries: 100, // Máximo de entries da Performance API
      maxVideosCandidates: 50,    // Máximo de candidatos a processar
      maxResultsPerGroup: 5       // Máximo de vídeos por grupo
    },
    debug: {
      enableTelemetry: true,      // ✅ Ativar telemetria temporária para calibração
      enableRegexFallback: false, // 🔧 Flag para reativar regex scraper em debug
      verboseScoring: true        // 📊 Logs detalhados de pontuação
    }
  };

  // Domínios de ads conhecidos para filtrar
  static AD_DOMAINS = [
    'doubleclick.net', 'googlesyndication.com', 'googleadservices.com',
    'facebook.com/tr', 'amazon-adsystem.com', 'outbrain.com', 'taboola.com',
    'adsystem.com', 'adnxs.com', 'adscdn.com', 'adsafeprotected.com'
  ];

  // Parâmetros voláteis para normalização de URL (para agrupamento)
  static VOLATILE_PARAMS = [
    'expires', 'signature', 'token', 'ts', 'range', 'rn', 'mm', 'mn', 'ms', 
    'mv', 'mvi', 'ei', 'cpn', 'gir', 'n', 'c', 'clen', 'dur', 'lmt',
    'utm_source', 'utm_medium', 'utm_campaign', 'fbclid', 'gclid'
  ];

  // Parâmetros críticos para preservar quando necessários para reprodução/download
  static CRITICAL_PARAMS = [
    'signature', 'itag', 'quality', 'format', 'res', 'height', 'width', 'v', 'id'
  ];

  // === PIPELINE PRINCIPAL ===
  static async extractVideo(url) {
    try {
      console.log('🚀 Iniciando pipeline de extração avançada para:', url);
      
      const hostname = new URL(url).hostname.toLowerCase();
      const startTime = performance.now();

      // Detectar plataforma específica primeiro
      if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) {
        const result = await this.extractYouTubeVideo(url);
        console.log(`✅ YouTube extraído em ${(performance.now() - startTime).toFixed(1)}ms`);
        return result;
      } else if (hostname.includes('vimeo.com')) {
        const result = await this.extractVimeoVideo(url);
        console.log(`✅ Vimeo extraído em ${(performance.now() - startTime).toFixed(1)}ms`);
        return result;
      }

      // Pipeline genérico: DOM → Performance → Merge → Score → Output
      const pipeline = await this.runGenericPipeline(url);
      console.log(`✅ Pipeline genérico concluído em ${(performance.now() - startTime).toFixed(1)}ms`);
      return pipeline;

    } catch (error) {
      console.error('❌ Erro no pipeline de extração:', error);
      return [];
    }
  }

  // === PIPELINE GENÉRICO OTIMIZADO ===
  static async runGenericPipeline(pageUrl) {
    const pipeline = {
      domVideos: [],
      networkVideos: [],
      merged: [],
      scored: [],
      final: []
    };

    try {
      // 1. Coletar <video> do DOM com enriquecimento
      pipeline.domVideos = await this.collectEnrichedDOMVideos();
      console.log(`📹 DOM: ${pipeline.domVideos.length} vídeos coletados`);

      // 2. Aguardar delay e coletar dados da Performance API
      await this.delay(this.CONFIG.timeouts.performanceDelay);
      pipeline.networkVideos = await this.collectNetworkVideos();
      console.log(`🌐 Network: ${pipeline.networkVideos.length} recursos coletados`);

      // 3. Merge e agrupamento
      pipeline.merged = this.mergeAndGroupVideos(pipeline.domVideos, pipeline.networkVideos);
      console.log(`🔗 Merge: ${pipeline.merged.length} grupos criados`);

      // 4. Scoring e classificação
      pipeline.scored = this.scoreAndClassifyVideos(pipeline.merged, pageUrl);
      console.log(`⭐ Score: ${pipeline.scored.filter(v => v.score >= this.CONFIG.thresholds.minScore).length}/${pipeline.scored.length} aprovados`);

      // 5. Filtro final e ordenação
      pipeline.final = this.finalizeResults(pipeline.scored);
      console.log(`🎯 Final: ${pipeline.final.length} vídeos selecionados`);

      return pipeline.final;

    } catch (error) {
      console.error('❌ Erro no pipeline genérico:', error);
      return [];
    }
  }

  // === 1. COLETA ROBUSTA DE DOM VIDEOS ===
  static async collectEnrichedDOMVideos() {
    const videos = Array.from(document.querySelectorAll('video'));
    const enrichedVideos = [];

    for (const video of videos.slice(0, this.CONFIG.limits.maxVideosCandidates)) {
      try {
        const enriched = await this.enrichVideoElement(video);
        if (enriched) {
          enrichedVideos.push(enriched);
        }
      } catch (error) {
        console.warn('⚠️ Erro ao enriquecer vídeo:', error);
      }
    }

    return enrichedVideos;
  }

  static async enrichVideoElement(videoElement) {
    try {
      // Aguardar metadata com timeout
      await this.waitForMetadata(videoElement, this.CONFIG.timeouts.metadataWait);

      // Coletar URLs
      const urls = this.collectVideoUrls(videoElement);
      if (urls.length === 0) return null;

      // Calcular visibilidade
      const visibility = this.calculateVisibility(videoElement);

      // Obter propriedades
      const properties = {
        width: videoElement.videoWidth || 0,
        height: videoElement.videoHeight || 0,
        duration: isFinite(videoElement.duration) ? videoElement.duration : null,
        currentTime: videoElement.currentTime || 0,
        paused: videoElement.paused,
        muted: videoElement.muted,
        controls: videoElement.controls,
        autoplay: videoElement.autoplay,
        loop: videoElement.loop
      };

      // Detectar proteção DRM
      const isProtected = this.checkDRMProtection(videoElement);

      return {
        kind: 'dom',
        urls: urls,
        element: videoElement,
        visibility: visibility,
        properties: properties,
        isProtected: isProtected,
        detectedAt: Date.now()
      };

    } catch (error) {
      console.warn('⚠️ Erro no enriquecimento:', error);
      return null;
    }
  }

  static async waitForMetadata(video, timeout) {
    if (video.readyState >= 1) return true; // HAVE_METADATA

    return new Promise((resolve) => {
      const timer = setTimeout(() => resolve(false), timeout);
      
      const onLoadedMetadata = () => {
        clearTimeout(timer);
        video.removeEventListener('loadedmetadata', onLoadedMetadata);
        resolve(true);
      };

      video.addEventListener('loadedmetadata', onLoadedMetadata);
    });
  }

  static collectVideoUrls(videoElement) {
    const urls = [];
    
    // currentSrc (prioritário)
    if (videoElement.currentSrc) {
      urls.push({
        url: videoElement.currentSrc,
        source: 'currentSrc',
        format: this.detectFormat(videoElement.currentSrc)
      });
    }

    // src attribute
    if (videoElement.src && videoElement.src !== videoElement.currentSrc) {
      urls.push({
        url: videoElement.src,
        source: 'src',
        format: this.detectFormat(videoElement.src)
      });
    }

    // <source> children
    const sources = videoElement.querySelectorAll('source');
    sources.forEach(source => {
      if (source.src && !urls.some(u => u.url === source.src)) {
        urls.push({
          url: source.src,
          source: 'source',
          format: this.detectFormat(source.src, source.type)
        });
      }
    });

    return urls;
  }

  static calculateVisibility(element) {
    try {
      const rect = element.getBoundingClientRect();
      const computedStyle = window.getComputedStyle(element);
      
      // Verificar se está oculto por CSS
      if (computedStyle.display === 'none' || 
          computedStyle.visibility === 'hidden' || 
          computedStyle.opacity === '0') {
        return { fraction: 0, isVisible: false, reason: 'css-hidden' };
      }

      // Verificar se está fora da tela
      if (rect.width === 0 || rect.height === 0) {
        return { fraction: 0, isVisible: false, reason: 'zero-size' };
      }

      // Calcular intersecção com viewport
      const viewport = {
        width: window.innerWidth,
        height: window.innerHeight
      };

      const intersection = {
        left: Math.max(0, rect.left),
        top: Math.max(0, rect.top),
        right: Math.min(viewport.width, rect.right),
        bottom: Math.min(viewport.height, rect.bottom)
      };

      const intersectionArea = Math.max(0, intersection.right - intersection.left) * 
                              Math.max(0, intersection.bottom - intersection.top);
      const elementArea = rect.width * rect.height;
      const fraction = elementArea > 0 ? intersectionArea / elementArea : 0;

      return {
        fraction: fraction,
        isVisible: fraction >= this.CONFIG.thresholds.visFraction,
        rect: rect,
        reason: fraction >= this.CONFIG.thresholds.visFraction ? 'visible' : 'partially-hidden'
      };

    } catch (error) {
      return { fraction: 0, isVisible: false, reason: 'error' };
    }
  }

  static checkDRMProtection(videoElement) {
    try {
      // Verificar se há MediaKeys associadas
      if (videoElement.mediaKeys) return true;
      
      // Verificar se requestMediaKeySystemAccess foi chamado
      if (window.requestMediaKeySystemAccessCalled) return true;
      
      // Verificar atributos que sugerem conteúdo protegido
      const protectedIndicators = ['encrypted', 'drm', 'widevine', 'playready'];
      const elementHTML = videoElement.outerHTML.toLowerCase();
      
      return protectedIndicators.some(indicator => elementHTML.includes(indicator));
    } catch (error) {
      return false;
    }
  }

  // === 2. COLETA VIA PERFORMANCE API ===
  static async collectNetworkVideos() {
    try {
      const entries = performance.getEntriesByType('resource')
        .slice(-this.CONFIG.limits.maxPerformanceEntries)
        .filter(entry => this.isVideoResource(entry.name));

      const networkVideos = [];

      for (const entry of entries) {
        try {
          const format = this.detectFormat(entry.name);
          
          networkVideos.push({
            kind: 'network',
            url: entry.name,
            format: format,
            entry: entry,
            size: entry.transferSize || 0,
            duration: entry.duration || 0,
            detectedAt: Date.now()
          });
        } catch (error) {
          console.warn('⚠️ Erro ao processar entrada de rede:', error);
        }
      }

      return networkVideos.filter(video => !this.isAdDomain(video.url));
    } catch (error) {
      console.error('❌ Erro na coleta de rede:', error);
      return [];
    }
  }

  static isVideoResource(url) {
    const lowerUrl = url.toLowerCase();
    
    // Extensões de vídeo conforme especificação
    const videoExtensions = ['.mp4', '.webm', '.m3u8', '.mpd', '.avi', '.mov', '.mkv', '.ts', '.m4v'];
    if (videoExtensions.some(ext => lowerUrl.includes(ext))) return true;
    
    // Padrões específicos conforme especificação
    const videoPatterns = [
      '/videoplayback',      // YouTube
      'googlevideo.com',     // YouTube
      'video', 'stream',     // Genéricos
      'manifest', 'playlist' // HLS/DASH
    ];
    
    return videoPatterns.some(pattern => lowerUrl.includes(pattern));
  }

  static isAdDomain(url) {
    try {
      const hostname = new URL(url).hostname.toLowerCase();
      return this.AD_DOMAINS.some(domain => hostname.includes(domain));
    } catch (error) {
      return false;
    }
  }

  // === 3. MERGE E AGRUPAMENTO ===
  static mergeAndGroupVideos(domVideos, networkVideos) {
    const allCandidates = [];

    // Processar vídeos DOM
    domVideos.forEach(domVideo => {
      domVideo.urls.forEach(urlInfo => {
        allCandidates.push({
          ...domVideo,
          url: urlInfo.url,
          format: urlInfo.format,
          source: urlInfo.source,
          normUrl: this.normalizeUrl(urlInfo.url)
        });
      });
    });

    // Processar vídeos de rede
    networkVideos.forEach(networkVideo => {
      allCandidates.push({
        ...networkVideo,
        normUrl: this.normalizeUrl(networkVideo.url)
      });
    });

    // Agrupar por chave
    const groups = new Map();
    
    allCandidates.forEach(candidate => {
      const groupKey = this.generateGroupKey(candidate);
      
      if (!groups.has(groupKey)) {
        groups.set(groupKey, []);
      }
      
      // Evitar duplicatas por URL normalizada
      const group = groups.get(groupKey);
      if (!group.some(item => item.normUrl === candidate.normUrl)) {
        group.push(candidate);
      }
    });

    return Array.from(groups.values());
  }

  static normalizeUrl(url) {
    try {
      const urlObj = new URL(url);
      
      // Remover parâmetros voláteis
      this.VOLATILE_PARAMS.forEach(param => {
        urlObj.searchParams.delete(param);
      });
      
      return urlObj.toString();
    } catch (error) {
      return url;
    }
  }

  static generateGroupKey(candidate) {
    try {
      const urlObj = new URL(candidate.url);
      const hostname = urlObj.hostname;
      
      // Para vídeos DOM com dimensões
      if (candidate.kind === 'dom' && candidate.properties) {
        const { width, height, duration } = candidate.properties;
        const durationKey = duration && isFinite(duration) ? 
          Math.round(duration / 10) * 10 : 'stream';
        
        return `${hostname}|${width}x${height}|${durationKey}`;
      }
      
      // Para HLS/DASH - usar URL de manifesto normalizada
      if (candidate.format === 'hls' || candidate.format === 'dash') {
        return `${hostname}|${candidate.format}|${candidate.normUrl}`;
      }
      
      // Para outros - usar hostname + path base
      const pathBase = urlObj.pathname.split('/').slice(0, -1).join('/');
      return `${hostname}|${pathBase}`;
      
    } catch (error) {
      return `unknown|${candidate.url}`;
    }
  }

  // === 4. SCORING E CLASSIFICAÇÃO ===
  static scoreAndClassifyVideos(groups, pageUrl) {
    const scoredVideos = [];

    groups.forEach(group => {
      // Selecionar o melhor representante do grupo
      const representative = this.selectGroupRepresentative(group);
      const score = this.calculateVideoScore(representative, pageUrl);
      
      scoredVideos.push({
        ...representative,
        score: score,
        groupSize: group.length,
        alternatives: group.filter(item => item !== representative)
      });
    });

    return scoredVideos.sort((a, b) => b.score - a.score);
  }

  static selectGroupRepresentative(group) {
    // Priorizar vídeos DOM visíveis
    const visibleDom = group.filter(item => 
      item.kind === 'dom' && item.visibility?.isVisible
    );
    if (visibleDom.length > 0) return visibleDom[0];
    
    // Depois vídeos DOM não visíveis
    const anyDom = group.filter(item => item.kind === 'dom');
    if (anyDom.length > 0) return anyDom[0];
    
    // Por último, recursos de rede (maior tamanho primeiro)
    const networkSorted = group
      .filter(item => item.kind === 'network')
      .sort((a, b) => (b.size || 0) - (a.size || 0));
    
    return networkSorted[0] || group[0];
  }

  static calculateVideoScore(video, pageUrl) {
    let score = 0;
    const reasons = [];
    
    // === BONIFICAÇÕES ===
    
    // +4 se vídeo DOM visível
    if (video.kind === 'dom' && video.visibility?.isVisible) {
      score += 4; reasons.push('+4 visible DOM video');
    }
    
    // +3 se duração conhecida >= minDuration
    if (video.properties?.duration && 
        video.properties.duration >= this.CONFIG.thresholds.minDurationVOD) {
      score += 3; reasons.push('+3 duration >= minDurationVOD');
    }
    
    // +2 se HLS/DASH (provável conteúdo principal)
    if (video.format === 'hls' || video.format === 'dash') {
      score += 2; reasons.push('+2 adaptive (hls/dash)');
    }
    
    // +2 se altura >= 480p
    if (video.properties?.height && video.properties.height >= 480) {
      score += 2; reasons.push('+2 height >= 480p');
    }
    
    // +1 se tem controles (sugere conteúdo principal)
    if (video.properties?.controls) {
      score += 1; reasons.push('+1 has controls');
    }
    
    // +1 se tamanho significativo (rede)
    if (video.size && video.size > 1024 * 1024) { // > 1MB
      score += 1; reasons.push('+1 network size > 1MB');
    }
    
    // === PENALIZAÇÕES ===
    
    // -4 se oculto sem interação
    if (video.kind === 'dom' && !video.visibility?.isVisible && 
        video.properties?.paused) {
      score -= 4; reasons.push('-4 hidden & paused');
    }
    
    // -3 se área muito pequena
    if (video.properties?.width && video.properties?.height) {
      const area = video.properties.width * video.properties.height;
      if (area < this.CONFIG.thresholds.minSide * this.CONFIG.thresholds.minSide) {
        score -= 3; reasons.push('-3 too small area');
      }
    }
    
    // -2 se protegido por DRM
    if (video.isProtected) {
      score -= 2; reasons.push('-2 DRM protected');
    }
    
    // -1 se autoplay (pode ser ad)
    if (video.properties?.autoplay && !video.properties?.controls) {
      score -= 1; reasons.push('-1 autoplay without controls');
    }

    if (this.CONFIG.debug.enableTelemetry && this.CONFIG.debug.verboseScoring) {
      console.log('[Scoring]', {
        url: video.url,
        format: video.format,
        kind: video.kind,
        height: video.properties?.height,
        duration: video.properties?.duration,
        score: Math.max(0, score),
        reasons
      });
    }

    return Math.max(0, score);
  }

  // === 5. FINALIZAÇÃO ===
  static finalizeResults(scoredVideos) {
    // Filtrar por score mínimo
    const qualified = scoredVideos.filter(video => 
      video.score >= this.CONFIG.thresholds.minScore
    );

    // Classificar por qualidade
    const classified = qualified.map(video => {
      const classification = video.score >= this.CONFIG.thresholds.highQualityScore 
        ? 'high' : 'medium';
      
      return {
        url: video.url,
        quality: this.formatQuality(video),
        format: video.format,
        score: video.score,
        classification: classification,
        hasAudio: true, // Assumir áudio por padrão
        context: video.kind,
        isVisible: video.visibility?.isVisible || false
      };
    });

    // Limitar resultados
    return classified.slice(0, this.CONFIG.limits.maxResultsPerGroup);
  }

  static formatQuality(video) {
    if (video.properties?.height) {
      return `${video.properties.height}p`;
    }
    
    if (video.format === 'hls' || video.format === 'dash') {
      return 'Auto';
    }
    
    return 'Original';
  }

  // === DETECÇÃO DE FORMATO ===
  static detectFormat(url, mimeType = null) {
    const lowerUrl = url.toLowerCase();
    
    // HLS
    if (lowerUrl.includes('.m3u8') || mimeType?.includes('hls')) {
      return 'hls';
    }
    
    // DASH
    if (lowerUrl.includes('.mpd') || mimeType?.includes('dash')) {
      return 'dash';
    }
    
    // Extensões específicas
    if (lowerUrl.includes('.webm')) return 'webm';
    if (lowerUrl.includes('.ts')) return 'ts';
    if (lowerUrl.includes('.m4v')) return 'm4v';
    if (lowerUrl.includes('.avi')) return 'avi';
    if (lowerUrl.includes('.mov')) return 'mov';
    if (lowerUrl.includes('.mkv')) return 'mkv';
    
    // MP4 como último recurso (não como padrão)
    if (lowerUrl.includes('.mp4')) return 'mp4';
    
    // Para YouTube e outros padrões conhecidos
    if (lowerUrl.includes('googlevideo') || lowerUrl.includes('videoplayback')) {
      return 'mp4'; // YouTube geralmente serve MP4
    }
    
    return 'unknown';
  }

  // === EXTRACTORS ESPECÍFICOS ===
  static async extractYouTubeVideo(url) {
    try {
      console.log('🔴 Extraindo vídeo do YouTube:', url);
      
      // Verificar proteção EME/Widevine primeiro
      const hasEME = this.checkYouTubeEME();
      if (hasEME) {
        console.warn('⚠️ Conteúdo protegido por DRM detectado');
        return [];
      }

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const html = await response.text();
      
      // Não incluir a URL da página como "vídeo"
      const pageUrl = window.location.href;
      if (url === pageUrl) {
        console.log('🚫 Evitando incluir URL da página como vídeo');
      }

      // Procurar por ytInitialPlayerResponse
      const playerMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
      if (playerMatch) {
        try {
          const playerData = JSON.parse(playerMatch[1]);
          const streamingData = playerData.streamingData;
          
          if (streamingData) {
            const videoUrls = [];
            
            // Formatos progressivos (vídeo + áudio)
            if (streamingData.formats) {
              streamingData.formats.forEach(format => {
                if (format.url && format.qualityLabel) {
                  videoUrls.push({
                    url: format.url,
                    quality: format.qualityLabel,
                    format: 'mp4',
                    hasAudio: true,
                    filesize: format.contentLength
                  });
                }
              });
            }
            
            // Formatos adaptativos
            if (streamingData.adaptiveFormats) {
              const videoFormats = streamingData.adaptiveFormats.filter(f =>
                f.mimeType?.includes('video') && f.url && f.qualityLabel
              );

              videoFormats.forEach(format => {
                const existingQuality = videoUrls.find(v => v.quality === format.qualityLabel);
                if (!existingQuality) {
                  videoUrls.push({
                    url: format.url,
                    quality: format.qualityLabel,
                    format: 'mp4',
                    hasAudio: false,
                    filesize: format.contentLength
                  });
                }
              });
            }
            
            // Complementar com Performance API para itags
            const performanceVideos = await this.collectYouTubePerformanceVideos();
            performanceVideos.forEach(perfVideo => {
              if (!videoUrls.some(v => v.url === perfVideo.url)) {
                videoUrls.push(perfVideo);
              }
            });
            
            return videoUrls;
          }
        } catch (e) {
          console.error('❌ Erro ao analisar dados do YouTube:', e);
        }
      }
      
      return [];
    } catch (error) {
      console.error('❌ Erro na extração do YouTube:', error);
      return [];
    }
  }

  static checkYouTubeEME() {
    try {
      // Verificar se há elementos de vídeo com mediaKeys
      const videos = document.querySelectorAll('video');
      for (const video of videos) {
        if (video.mediaKeys) return true;
      }
      
      // Verificar se requestMediaKeySystemAccess foi interceptado/usado
      return false; // Simplificado para este exemplo
    } catch (error) {
      return false;
    }
  }

  static async collectYouTubePerformanceVideos() {
    try {
      const entries = performance.getEntriesByType('resource')
        .filter(entry => 
          entry.name.includes('googlevideo') || 
          entry.name.includes('videoplayback')
        );

      return entries.map(entry => {
        const url = new URL(entry.name);
        const itag = url.searchParams.get('itag');
        const quality = this.itagToQuality(itag) || 'Unknown';
        
        return {
          url: entry.name,
          quality: quality,
          format: 'mp4',
          hasAudio: this.itagHasAudio(itag),
          filesize: entry.transferSize
        };
      });
    } catch (error) {
      return [];
    }
  }

  static itagToQuality(itag) {
    const itagMap = {
      '22': '720p', '18': '360p', '137': '1080p', '136': '720p', 
      '135': '480p', '134': '360p', '298': '720p60', '299': '1080p60'
    };
    return itagMap[itag];
  }

  static itagHasAudio(itag) {
    const audioItags = ['22', '18', '43', '36', '17'];
    return audioItags.includes(itag);
  }

  static async extractVimeoVideo(url) {
    try {
      console.log('🔵 Extraindo vídeo do Vimeo:', url);
      
      // Evitar incluir URL da página
      const pageUrl = window.location.href;
      if (url === pageUrl) {
        console.log('🚫 Evitando incluir URL da página como vídeo');
        return [];
      }

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const html = await response.text();
      
      // Procurar por config_url
      const configMatch = html.match(/"config_url":"([^"]*)"/);
      if (configMatch) {
        try {
          const configUrl = configMatch[1].replace(/\\u002F/g, '/').replace(/\\/g, '');
          const configResponse = await fetch(configUrl);
          const config = await configResponse.json();
          
          const videoUrls = [];
          
          // Progressive (MP4)
          if (config.request?.files?.progressive) {
            config.request.files.progressive.forEach(file => {
              videoUrls.push({
                url: file.url,
                quality: `${file.height}p`,
                format: 'mp4',
                hasAudio: true,
                filesize: file.size
              });
            });
          }
          
          // HLS se disponível
          if (config.request?.files?.hls) {
            videoUrls.push({
              url: config.request.files.hls.url,
              quality: 'Auto',
              format: 'hls',
              hasAudio: true,
              filesize: null
            });
          }
          
          return videoUrls;
        } catch (e) {
          console.error('❌ Erro ao obter config do Vimeo:', e);
        }
      }
      
      return [];
    } catch (error) {
      console.error('❌ Erro na extração do Vimeo:', error);
      return [];
    }
  }

  // === INTEGRAÇÃO COM SERVIÇOS AUXILIARES ===
  static async resolveUrl(url) {
    // Integração com UrlResolutionService existente
    try {
      // Tenta carregar dinamicamente o UrlResolutionService
      if (typeof UrlResolutionService !== 'undefined') {
        const result = await UrlResolutionService.processVideoUrl(url);
        return result || this.normalizeUrl(url);
      }
    } catch (error) {
      console.warn('⚠️ UrlResolutionService não disponível, usando fallback:', error.message);
    }
    
    // Usa normalização local como fallback
    return this.normalizeUrl(url);
  }
  
  // === UTILITIES ===
  static async delay(ms) {
    // Integração com VideoDetectionPerformanceService existente
    try {
      if (typeof VideoDetectionPerformanceService !== 'undefined') {
        return VideoDetectionPerformanceService.delay(ms);
      }
    } catch (error) {
      console.warn('⚠️ VideoDetectionPerformanceService não disponível, usando fallback');
    }
    
    // Fallback básico
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // === MÉTODO PRINCIPAL COMPATÍVEL ===
  static async extractGenericVideo(url) {
    const result = await this.runGenericPipeline(url);

    // Fallback opcional de regex scraper (apenas debug)
    if (this.CONFIG.debug.enableRegexFallback && (!result || result.length === 0)) {
      try {
        const html = document.documentElement.innerHTML;
        const regexUrls = [];
        const patterns = [
          /(https?:\/\/[^\s"'<>]+\.(?:mp4|webm|m3u8|mpd))(?![^<]*>)/ig,
          /(https?:\/\/[^\\s\"'<>]+(?:videoplayback|googlevideo)[^\s"'<>]*)/ig
        ];
        for (const pattern of patterns) {
          let match;
          while ((match = pattern.exec(html)) !== null) {
            const u = match[1].replace(/\\\/\/g, '/');
            if (!regexUrls.includes(u)) regexUrls.push(u);
          }
        }
        if (regexUrls.length > 0) {
          console.warn('🔎 Regex fallback encontrou URLs:', regexUrls.length);
          return regexUrls.map(u => ({ url: u, quality: 'Original', format: this.detectFormat(u) }));
        }
      } catch (e) {
        console.warn('Regex fallback falhou:', e.message);
      }
    }

    return result;
  }

  // Método para detectar vídeo principal (compatibilidade)
  static detectMainVideo(videoUrls, pageUrl) {
    if (!videoUrls || videoUrls.length === 0) return [];
    if (videoUrls.length === 1) return videoUrls;
    
    // Usar o scoring do pipeline
    const scored = this.scoreAndClassifyVideos([videoUrls], pageUrl);
    return this.finalizeResults(scored);
  }
}

export default VideoExtractorService;

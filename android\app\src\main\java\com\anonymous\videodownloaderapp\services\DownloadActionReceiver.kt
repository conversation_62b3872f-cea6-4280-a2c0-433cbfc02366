package com.anonymous.videodownloaderapp.services

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.workDataOf
import com.anonymous.videodownloaderapp.data.DownloadRepository
import com.anonymous.videodownloaderapp.workers.VideoDownloadWorker
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Handles NotificationHelper actions (Pause/Resume/Cancel) with simple controls:
 * - Pause: cancels active workers and marks item Paused.
 * - Cancel: cancels active workers and marks item Canceled.
 * - Resume: enqueues a fresh worker from 0 for the same URL/title/mime (no partial resume).
 *
 * No UI or behavior changes beyond ensuring foreground compliance.
 */
class DownloadActionReceiver : BroadcastReceiver() {

    private val scope = CoroutineScope(Dispatchers.IO)

    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action ?: return
        val id = intent.getLongExtra("downloadId", 0L)
        if (id <= 0) return

        val repo = DownloadRepository.get(context)
        val wm = WorkManager.getInstance(context)

        when (action) {
            NotificationHelper.ACTION_PAUSE -> {
                wm.cancelAllWorkByTag(VideoDownloadWorker.TAG_DOWNLOAD)
                scope.launch { repo.markPaused(id) }
            }
            NotificationHelper.ACTION_CANCEL -> {
                wm.cancelAllWorkByTag(VideoDownloadWorker.TAG_DOWNLOAD)
                scope.launch { repo.markCanceled(id) }
            }
            NotificationHelper.ACTION_RESUME -> {
                scope.launch {
                    val item = repo.getById(id)
                    if (item != null) {
                        val req = OneTimeWorkRequestBuilder<VideoDownloadWorker>()
                            .addTag(VideoDownloadWorker.TAG_DOWNLOAD)
                            .setInputData(
                                workDataOf(
                                    "videoUrl" to item.url,
                                    "fileName" to item.title,
                                    "downloadId" to item.id
                                )
                            )
                            .build()
                        wm.enqueueUniqueWork(
                            "download_${item.id}",
                            ExistingWorkPolicy.REPLACE,
                            req
                        )
                    }
                }
            }
        }
    }
}
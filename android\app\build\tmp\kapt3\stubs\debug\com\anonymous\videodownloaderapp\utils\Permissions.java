package com.anonymous.videodownloaderapp.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0011\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\u0005\u001a\u00020\u0006J=\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\f0\u000b2\u0006\u0010\r\u001a\u00020\u000e2!\u0010\u000f\u001a\u001d\u0012\u0013\u0012\u00110\u0004\u00a2\u0006\f\b\u0011\u0012\b\b\u0012\u0012\u0004\b\b(\u0013\u0012\u0004\u0012\u00020\u00140\u0010J\"\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\u00172\u0012\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\f0\u000bJ\u0011\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\t0\f\u00a2\u0006\u0002\u0010\u001a\u00a8\u0006\u001b"}, d2 = {"Lcom/anonymous/videodownloaderapp/utils/Permissions;", "", "()V", "hasAll", "", "context", "Landroid/content/Context;", "missing", "", "", "registerRequester", "Landroidx/activity/result/ActivityResultLauncher;", "", "caller", "Landroidx/activity/result/ActivityResultCaller;", "onResult", "Lkotlin/Function1;", "Lkotlin/ParameterName;", "name", "granted", "", "requestIfNeeded", "activity", "Landroid/app/Activity;", "launcher", "requiredRuntimePermissions", "()[Ljava/lang/String;", "app_debug"})
public final class Permissions {
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.utils.Permissions INSTANCE = null;
    
    private Permissions() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String[] requiredRuntimePermissions() {
        return null;
    }
    
    public final boolean hasAll(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> missing(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * Helper to register a permission launcher and request missing permissions.
     * Usage from an Activity or Fragment:
     *
     * val launcher = Permissions.registerRequester(this) { granted -> ... }
     * Permissions.requestIfNeeded(this, launcher)
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.activity.result.ActivityResultLauncher<java.lang.String[]> registerRequester(@org.jetbrains.annotations.NotNull()
    androidx.activity.result.ActivityResultCaller caller, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onResult) {
        return null;
    }
    
    public final void requestIfNeeded(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    androidx.activity.result.ActivityResultLauncher<java.lang.String[]> launcher) {
    }
}
package com.anonymous.videodownloaderapp.features.browser;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u001a\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u0014\u001a\u00020\u0015J\u0010\u0010\u0016\u001a\u00020\u00152\b\u0010\u0017\u001a\u0004\u0018\u00010\u0018J\u0012\u0010\u0019\u001a\u00020\u00152\b\u0010\u0017\u001a\u0004\u0018\u00010\u0018H\u0002J\u0006\u0010\u001a\u001a\u00020\u0013J\u0010\u0010\u001b\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u0013H\u0002J\b\u0010\u001d\u001a\u00020\u0015H\u0002J\u000e\u0010\u001e\u001a\u00020\u00152\u0006\u0010\u001f\u001a\u00020 J\u000e\u0010!\u001a\u00020\u00152\u0006\u0010\"\u001a\u00020 J\u000e\u0010#\u001a\u00020\u00152\u0006\u0010$\u001a\u00020\u0013J\u000e\u0010%\u001a\u00020\u00152\u0006\u0010&\u001a\u00020 J\u000e\u0010\'\u001a\u00020\u00152\u0006\u0010(\u001a\u00020 J\b\u0010)\u001a\u00020\u0015H\u0014J\u001e\u0010*\u001a\u00020\u00152\u0006\u0010+\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u00132\u0006\u0010,\u001a\u00020\u0013J\u0010\u0010-\u001a\u00020\u00152\b\u0010\u0017\u001a\u0004\u0018\u00010\u0018J\u0010\u0010.\u001a\u00020\u00152\b\u0010\u0017\u001a\u0004\u0018\u00010\u0018J\u0010\u0010/\u001a\u00020\u00152\b\u0010\u0017\u001a\u0004\u0018\u00010\u0018J\u0010\u00100\u001a\u00020\u00152\b\u0010\u0017\u001a\u0004\u0018\u00010\u0018J\u000e\u00101\u001a\u00020\u00152\u0006\u00102\u001a\u00020 J\u0010\u00103\u001a\u00020\u00152\b\u0010\u0017\u001a\u0004\u0018\u00010\u0018J\u000e\u00104\u001a\u00020\u00152\u0006\u00105\u001a\u00020 J\u000e\u00106\u001a\u00020\u00152\u0006\u0010\u001c\u001a\u00020\u0013J\u000e\u00107\u001a\u00020\u00152\u0006\u00105\u001a\u00020 J\b\u00108\u001a\u00020\u0015H\u0002J\u000e\u00109\u001a\u00020\u00152\u0006\u0010$\u001a\u00020\u0013R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006:"}, d2 = {"Lcom/anonymous/videodownloaderapp/features/browser/BrowserViewModel;", "Landroidx/lifecycle/ViewModel;", "context", "Landroid/content/Context;", "downloadRepository", "Lcom/anonymous/videodownloaderapp/data/DownloadRepository;", "(Landroid/content/Context;Lcom/anonymous/videodownloaderapp/data/DownloadRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/anonymous/videodownloaderapp/features/browser/BrowserUiState;", "hybridDetector", "Lcom/anonymous/videodownloaderapp/services/HybridVideoDetectionService;", "storeListener", "Lcom/anonymous/videodownloaderapp/features/browser/VideoDetectionStore$Listener;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "videoDetectorScript", "", "clearError", "", "detectVideos", "webView", "Landroid/webkit/WebView;", "detectVideosJavaScriptFallback", "getTruncatedAddressText", "inferFormatFromUrl", "url", "loadVideoDetectorScript", "onAddressBarExpandedChanged", "isExpanded", "", "onAddressBarFocusChanged", "isFocused", "onAddressBarTextChanged", "text", "onCanGoBackChanged", "canGoBack", "onCanGoForwardChanged", "canGoForward", "onCleared", "onDownloadVideo", "quality", "format", "onGoBack", "onGoForward", "onGoToUrl", "onHomeClicked", "onLoadingStateChanged", "isLoading", "onRefresh", "onSettingsMenuVisibilityChanged", "isVisible", "onUrlChanged", "onVideoDialogVisibilityChanged", "setupHybridDetection", "updateAddressBarText", "app_debug"})
public final class BrowserViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.anonymous.videodownloaderapp.data.DownloadRepository downloadRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.anonymous.videodownloaderapp.features.browser.BrowserUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.features.browser.BrowserUiState> uiState = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String videoDetectorScript;
    @org.jetbrains.annotations.NotNull()
    private final com.anonymous.videodownloaderapp.services.HybridVideoDetectionService hybridDetector = null;
    @org.jetbrains.annotations.NotNull()
    private final com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore.Listener storeListener = null;
    
    public BrowserViewModel(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadRepository downloadRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.features.browser.BrowserUiState> getUiState() {
        return null;
    }
    
    private final java.lang.String inferFormatFromUrl(java.lang.String url) {
        return null;
    }
    
    private final void setupHybridDetection() {
    }
    
    private final void loadVideoDetectorScript() {
    }
    
    public final void onAddressBarTextChanged(@org.jetbrains.annotations.NotNull()
    java.lang.String text) {
    }
    
    public final void updateAddressBarText(@org.jetbrains.annotations.NotNull()
    java.lang.String text) {
    }
    
    public final void onUrlChanged(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
    }
    
    public final void onLoadingStateChanged(boolean isLoading) {
    }
    
    public final void onAddressBarExpandedChanged(boolean isExpanded) {
    }
    
    public final void onAddressBarFocusChanged(boolean isFocused) {
    }
    
    public final void onVideoDialogVisibilityChanged(boolean isVisible) {
    }
    
    public final void onSettingsMenuVisibilityChanged(boolean isVisible) {
    }
    
    public final void onGoToUrl(@org.jetbrains.annotations.Nullable()
    android.webkit.WebView webView) {
    }
    
    public final void onRefresh(@org.jetbrains.annotations.Nullable()
    android.webkit.WebView webView) {
    }
    
    public final void onHomeClicked(@org.jetbrains.annotations.Nullable()
    android.webkit.WebView webView) {
    }
    
    public final void onGoBack(@org.jetbrains.annotations.Nullable()
    android.webkit.WebView webView) {
    }
    
    public final void onGoForward(@org.jetbrains.annotations.Nullable()
    android.webkit.WebView webView) {
    }
    
    public final void onCanGoBackChanged(boolean canGoBack) {
    }
    
    public final void onCanGoForwardChanged(boolean canGoForward) {
    }
    
    public final void detectVideos(@org.jetbrains.annotations.Nullable()
    android.webkit.WebView webView) {
    }
    
    /**
     * Fallback para detecção JavaScript original (mantido para compatibilidade)
     */
    private final void detectVideosJavaScriptFallback(android.webkit.WebView webView) {
    }
    
    public final void onDownloadVideo(@org.jetbrains.annotations.NotNull()
    java.lang.String quality, @org.jetbrains.annotations.NotNull()
    java.lang.String url, @org.jetbrains.annotations.NotNull()
    java.lang.String format) {
    }
    
    public final void clearError() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTruncatedAddressText() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
}
package com.anonymous.videodownloaderapp.services;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0010\t\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0010J\u000e\u0010\u0011\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u0012J\u000e\u0010\u0013\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u0012J\u0016\u0010\u0014\u001a\u00020\u000e2\u0006\u0010\u0015\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010\u0018\u001a\u00020\u000e2\u0006\u0010\u0019\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0010R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u001a"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/DownloadTracker;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "downloadDao", "Lcom/anonymous/videodownloaderapp/data/DownloadDao;", "downloads", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/anonymous/videodownloaderapp/data/DownloadItem;", "getDownloads", "()Lkotlinx/coroutines/flow/Flow;", "addDownload", "", "item", "(Lcom/anonymous/videodownloaderapp/data/DownloadItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearAllDownloads", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearCompletedAndFailedDownloads", "removeDownload", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDownload", "updatedItem", "app_debug"})
public final class DownloadTracker {
    @org.jetbrains.annotations.NotNull()
    private final com.anonymous.videodownloaderapp.data.DownloadDao downloadDao = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<java.util.List<com.anonymous.videodownloaderapp.data.DownloadItem>> downloads = null;
    
    public DownloadTracker(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.anonymous.videodownloaderapp.data.DownloadItem>> getDownloads() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addDownload(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadItem item, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateDownload(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadItem updatedItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeDownload(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearAllDownloads(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearCompletedAndFailedDownloads(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}
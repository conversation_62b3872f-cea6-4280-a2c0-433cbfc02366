package com.anonymous.videodownloaderapp.services

import android.content.Context
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.workDataOf
import com.anonymous.videodownloaderapp.data.DownloadItem
import com.anonymous.videodownloaderapp.integration.PerformanceIntegration
import com.anonymous.videodownloaderapp.workers.VideoDownloadWorker
import com.google.gson.Gson
import java.util.UUID

class BackgroundDownloadService {
    companion object {
        fun startBackgroundDownload(context: Context, downloadItem: DownloadItem) {
            // Initialize performance integration
            PerformanceIntegration.initialize(context)
            
            // Use enhanced download service
            PerformanceIntegration.startEnhancedDownload(downloadItem)
            
            // Fallback to original worker for compatibility
            val gson = Gson()
            val workRequest = OneTimeWorkRequestBuilder<VideoDownloadWorker>()
                .setInputData(workDataOf("downloadItem" to gson.toJson(downloadItem)))
                .build()
            WorkManager.getInstance(context).enqueue(workRequest)
        }
        
        fun startOptimizedBackgroundDownload(context: Context, downloadItem: DownloadItem) {
            PerformanceIntegration.initialize(context)
            PerformanceIntegration.startEnhancedDownload(downloadItem)
        }
    }
}


package com.anonymous.videodownloaderapp.services

import android.app.NotificationManager
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import androidx.core.content.ContextCompat
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ForegroundInfo
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.Operation
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import com.anonymous.videodownloaderapp.data.DownloadRepository
import com.anonymous.videodownloaderapp.datastore.SettingsStore
import com.anonymous.videodownloaderapp.utils.FileUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.OutputStream
import java.util.concurrent.TimeUnit

class DownloadWorker(
    appContext: Context,
    params: WorkerParameters
) : CoroutineWorker(appContext, params) {

    private val context = appContext
    private val repo by lazy { DownloadRepository.get(context) }
    private val settings by lazy { SettingsStore.get(context) }
    private val client by lazy {
        run {
            val builder = OkHttpClient.Builder()
                .connectTimeout(20, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
            
            // Configurar com HybridVideoDetectionService para interceptação de rede
            HybridVideoDetectionService.getInstance(context).configureOkHttpClient(builder)
            
            builder.build()
        }
    }

    override suspend fun doWork(): Result {
        val raw = inputData.getString(KEY_URL) ?: return Result.failure()
        val title = inputData.getString(KEY_TITLE)
        val providedMime = inputData.getString(KEY_MIME)

        // Basic URL validation to avoid OkHttp IllegalArgumentException (accept only http/https)
        val urlString = raw.trim()
        if (!(urlString.startsWith("http://") || urlString.startsWith("https://"))) {
            return Result.failure()
        }

        val cfg = settings.settingsFlow.first()
        if (cfg.wifiOnly && !isOnWifi()) return Result.retry()

        val id = repo.enqueue(urlString, title, providedMime)

        setProgress(workDataOf(KEY_PROGRESS to 0))
        repo.markRunning(id)

        val mime = FileUtils.resolveMimeType(urlString, providedMime)
        val displayName = FileUtils.guessFileName(urlString, mime)

        val dest = FileUtils.createMediaInDownloads(context, displayName, mime)
            ?: return failWith(id, "Failed to create MediaStore item")

        var output: OutputStream? = null
        var response: Response? = null
        try {
            // Move foreground setup into try/catch to avoid process crash on notification errors
            runCatching {
                setForeground(createForegroundInfo(id, displayName, 0, isPaused = false))
            }

            output = FileUtils.openOutputStream(context, dest.uri)
                ?: return failWith(id, "Failed to open output stream")

            val headReq = Request.Builder().url(urlString).head().build()
            val headResp = runCatching { client.newCall(headReq).execute() }.getOrNull()
            val totalBytes = headResp?.header("Content-Length")?.toLongOrNull() ?: -1L

            var downloaded = 0L
            val reqBuilder = Request.Builder().url(urlString)
            if (downloaded > 0L) reqBuilder.addHeader("Range", "bytes=$downloaded-")
            response = client.newCall(reqBuilder.build()).execute()

            if (!response.isSuccessful) return failWith(id, "HTTP ${response.code}")

            val body = response.body ?: return failWith(id, "Empty body")
            val source = body.byteStream()

            val expectedLength = if (totalBytes > 0) totalBytes else body.contentLength().takeIf { it > 0 } ?: -1L

            val buffer = ByteArray(DEFAULT_BUFFER)
            var read: Int
            var lastProgressEmit = System.currentTimeMillis()

            while (true) {
                if (isStopped) {
                    cancelDownload(id)
                    return Result.failure()
                }

                read = withContext(Dispatchers.IO) { source.read(buffer) }
                if (read == -1) break

                withContext(Dispatchers.IO) { output.write(buffer, 0, read) }
                downloaded += read

                val progress = if (expectedLength > 0) ((downloaded * 100L) / expectedLength).toInt().coerceIn(0, 100) else -1
                val now = System.currentTimeMillis()
                if (progress >= 0 && now - lastProgressEmit > 350) {
                    lastProgressEmit = now
                    repo.markProgress(id, downloaded, expectedLength)
                    setProgress(workDataOf(KEY_PROGRESS to progress))
                    runCatching {
                        setForeground(createForegroundInfo(id, displayName, progress, isPaused = false))
                    }
                }
            }

            FileUtils.finalizePending(context, dest.uri)
            repo.markCompleted(id, dest.uri.toString(), if (expectedLength > 0) expectedLength else downloaded)

            runCatching { notifyCompleted(displayName) }

            return Result.success(
                workDataOf(
                    KEY_ID to id,
                    KEY_URI to dest.uri.toString(),
                    KEY_BYTES to (if (expectedLength > 0) expectedLength else downloaded)
                )
            )
        } catch (t: Throwable) {
            return failWith(id, t.message ?: "Unknown error")
        } finally {
            runCatching { response?.close() }
            runCatching { output?.flush() }
            runCatching { output?.close() }
        }
    }

    private fun isOnWifi(): Boolean {
        val cm = ContextCompat.getSystemService(context, ConnectivityManager::class.java) ?: return false
        val network = cm.activeNetwork ?: return false
        val caps = cm.getNetworkCapabilities(network) ?: return false
        return caps.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
    }

    private fun notifyCompleted(displayName: String) {
        val nm = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val n = NotificationHelper.simpleCompleted(
            context = context,
            title = "Download complete",
            text = displayName
        )
        nm.notify(NOTIFICATION_COMPLETE_BASE + (System.currentTimeMillis() % 10000).toInt(), n)
    }

    private suspend fun failWith(id: Long, message: String): Result {
        repo.markFailed(id, message)
        return Result.retry()
    }

    private suspend fun cancelDownload(id: Long) {
        repo.markCanceled(id)
    }

    private fun createForegroundInfo(id: Long, title: String, progress: Int, isPaused: Boolean): ForegroundInfo {
        val notification = NotificationHelper.foregroundNotification(
            context = context,
            title = title,
            text = if (progress in 0..100) "$progress%" else "Downloading...",
            progress = if (progress in 0..100) progress else null,
            isPaused = isPaused,
            downloadId = id
        )
        return ForegroundInfo(NotificationHelper.NOTIFICATION_ID_FOREGROUND + (id % 1000).toInt(), notification)
    }

    companion object {
        private const val DEFAULT_BUFFER = 64 * 1024
        private const val NOTIFICATION_COMPLETE_BASE = 4000

        const val KEY_ID = "key_id"
        const val KEY_URL = "key_url"
        const val KEY_TITLE = "key_title"
        const val KEY_MIME = "key_mime"
        const val KEY_PROGRESS = "key_progress"
        const val KEY_URI = "key_uri"
        const val KEY_BYTES = "key_bytes"

        fun enqueue(
            context: Context,
            url: String,
            title: String? = null,
            mimeType: String? = null,
            wifiOnly: Boolean? = null
        ): Operation {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(
                    if (wifiOnly == true) NetworkType.UNMETERED else NetworkType.CONNECTED
                )
                .build()

            val data = workDataOf(
                KEY_URL to url,
                KEY_TITLE to title,
                KEY_MIME to mimeType
            )

            val request = OneTimeWorkRequestBuilder<DownloadWorker>()
                .setConstraints(constraints)
                .setInputData(data)
                .addTag(TAG_DOWNLOAD)
                .build()

            return WorkManager.getInstance(context).enqueue(request)
        }

        const val TAG_DOWNLOAD = "download_task"
    }
}
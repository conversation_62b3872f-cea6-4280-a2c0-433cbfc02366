package com.anonymous.videodownloaderapp.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import androidx.core.app.NotificationCompat
import com.anonymous.videodownloaderapp.MainActivity

object NotificationHelper {

    const val CHANNEL_DOWNLOADS_ID = "downloads_channel"
    const val CHANNEL_DOWNLOADS_NAME = "Downloads"
    const val CHANNEL_DOWNLOADS_DESC = "Active and completed downloads"

    const val NOTIFICATION_ID_FOREGROUND = 1001
    const val ACTION_PAUSE = "com.anonymous.videodownloaderapp.action.PAUSE"
    const val ACTION_RESUME = "com.anonymous.videodownloaderapp.action.RESUME"
    const val ACTION_CANCEL = "com.anonymous.videodownloaderapp.action.CANCEL"

    fun ensureChannels(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val nm = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            val channel = NotificationChannel(
                CHANNEL_DOWNLOADS_ID,
                CHANNEL_DOWNLOADS_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = CHANNEL_DOWNLOADS_DESC
                enableLights(false)
                enableVibration(false)
                lightColor = Color.BLUE
                setShowBadge(true)
            }
            nm.createNotificationChannel(channel)
        }
    }

    fun foregroundNotification(
        context: Context,
        title: String,
        text: String,
        progress: Int?,
        isPaused: Boolean,
        downloadId: Long
    ): Notification {
        ensureChannels(context)

        val contentIntent = PendingIntent.getActivity(
            context,
            0,
            Intent(context, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            },
            pendingIntentFlags()
        )

        // Explicitly target our BroadcastReceiver to avoid implicit broadcast restrictions
        val pauseIntent = Intent(context, DownloadActionReceiver::class.java).apply {
            action = ACTION_PAUSE
            putExtra("downloadId", downloadId)
        }
        val resumeIntent = Intent(context, DownloadActionReceiver::class.java).apply {
            action = ACTION_RESUME
            putExtra("downloadId", downloadId)
        }
        val cancelIntent = Intent(context, DownloadActionReceiver::class.java).apply {
            action = ACTION_CANCEL
            putExtra("downloadId", downloadId)
        }

        val pausePI = PendingIntent.getBroadcast(
            context,
            downloadId.toInt(),
            pauseIntent,
            pendingIntentFlags()
        )
        val resumePI = PendingIntent.getBroadcast(
            context,
            10_000 + downloadId.toInt(),
            resumeIntent,
            pendingIntentFlags()
        )
        val cancelPI = PendingIntent.getBroadcast(
            context,
            20_000 + downloadId.toInt(),
            cancelIntent,
            pendingIntentFlags()
        )

        val builder = NotificationCompat.Builder(context, CHANNEL_DOWNLOADS_ID)
            .setSmallIcon(android.R.drawable.stat_sys_download)
            .setContentTitle(title)
            .setContentText(text)
            .setOnlyAlertOnce(true)
            .setOngoing(true)
            .setContentIntent(contentIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(Notification.CATEGORY_PROGRESS)

        if (progress != null) {
            builder.setProgress(100, progress.coerceIn(0, 100), false)
        } else {
            builder.setProgress(0, 0, true)
        }

        builder.addAction(
            0,
            if (isPaused) "Resume" else "Pause",
            if (isPaused) resumePI else pausePI
        )
        builder.addAction(0, "Cancel", cancelPI)

        return builder.build()
    }

    fun simpleCompleted(
        context: Context,
        title: String,
        text: String
    ): Notification {
        ensureChannels(context)
        val contentIntent = PendingIntent.getActivity(
            context,
            0,
            Intent(context, MainActivity::class.java),
            pendingIntentFlags()
        )
        return NotificationCompat.Builder(context, CHANNEL_DOWNLOADS_ID)
            .setSmallIcon(android.R.drawable.stat_sys_download_done)
            .setContentTitle(title)
            .setContentText(text)
            .setAutoCancel(true)
            .setContentIntent(contentIntent)
            .build()
    }

    private fun pendingIntentFlags(): Int {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
    }
}
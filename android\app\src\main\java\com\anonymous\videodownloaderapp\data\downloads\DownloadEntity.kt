package com.anonymous.videodownloaderapp.data.downloads

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "downloads")
data class DownloadEntity(
    @PrimaryKey val id: String,
    val fileName: String,
    val originalUrl: String,
    val filePath: String,
    val status: String,
    val progress: Float,
    val downloadedSize: Long,
    val totalSize: Long,
    val quality: String?,
    val timestamp: Long
)

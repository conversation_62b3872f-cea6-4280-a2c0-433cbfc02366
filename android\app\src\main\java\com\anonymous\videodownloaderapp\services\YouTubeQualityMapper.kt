package com.anonymous.videodownloaderapp.services

import android.content.Context
import android.webkit.WebView
import kotlinx.coroutines.*
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

/**
 * Mapeamento de Qualidade Ultra-Preciso para YouTube
 * Elimina completamente "Auto" usando múltiplas fontes de dados
 */
class YouTubeQualityMapper(private val context: Context) {

    data class YouTubeQuality(
        val itag: Int,
        val quality: String,
        val width: Int?,
        val height: Int?,
        val fps: Int?,
        val hasAudio: Boolean,
        val codec: String?,
        val url: String,
        val contentLength: Long?
    )

    companion object {
        // Tabela completa de mapeamento itag atualizada
        private val ITAG_QUALITY_MAP = mapOf(
            // 4K (2160p)
            401 to "2160p", 400 to "2160p", 337 to "2160p60", 315 to "2160p60",
            313 to "2160p", 272 to "2160p60", 308 to "2160p60", 571 to "2160p",
            
            // 1440p
            271 to "1440p", 304 to "1440p60", 336 to "1440p60", 620 to "1440p",
            
            // 1080p
            137 to "1080p", 299 to "1080p60", 303 to "1080p60", 248 to "1080p",
            169 to "1080p", 170 to "1080p", 264 to "1080p", 266 to "1080p",
            298 to "1080p60", 302 to "1080p60", 335 to "1080p60", 614 to "1080p",
            
            // 720p
            136 to "720p", 298 to "720p60", 302 to "720p60", 247 to "720p",
            168 to "720p", 169 to "720p", 170 to "720p", 218 to "720p",
            219 to "720p", 264 to "720p", 266 to "720p", 334 to "720p60",
            
            // 480p
            135 to "480p", 244 to "480p", 167 to "480p", 168 to "480p",
            218 to "480p", 219 to "480p", 333 to "480p60",
            
            // 360p
            134 to "360p", 243 to "360p", 167 to "360p", 168 to "360p",
            218 to "360p", 219 to "360p", 332 to "360p60",
            
            // 240p
            133 to "240p", 242 to "240p", 167 to "240p", 168 to "240p",
            
            // 144p
            160 to "144p", 278 to "144p", 394 to "144p", 396 to "144p",
            
            // Formatos com áudio (progressivos)
            22 to "720p", 18 to "360p", 43 to "360p", 36 to "240p", 17 to "144p"
        )
        
        private val ITAG_AUDIO_MAP = setOf(
            22, 18, 43, 36, 17, // Progressivos com áudio
            140, 141, 171, 249, 250, 251 // Apenas áudio
        )
        
        private val ITAG_CODEC_MAP = mapOf(
            // VP9
            313 to "vp9", 271 to "vp9", 137 to "avc1", 136 to "avc1",
            // AV1
            571 to "av01", 401 to "av01", 400 to "av01",
            // H.264
            22 to "avc1", 18 to "avc1"
        )
        
        private const val METADATA_TIMEOUT_MS = 800L
        private const val YTPLAYER_TIMEOUT_MS = 2000L
    }
    
    private val qualityCache = ConcurrentHashMap<String, List<YouTubeQuality>>()
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    /**
     * Extração ultra-precisa de qualidades do YouTube
     * Prioridade: ytInitialPlayerResponse > itag mapping > performance entries
     */
    suspend fun extractYouTubeQualities(
        webView: WebView,
        pageUrl: String
    ): List<YouTubeQuality> = withContext(Dispatchers.Main) {
        
        if (!isYouTubeUrl(pageUrl)) return@withContext emptyList()
        
        // Verificar cache primeiro
        val cached = qualityCache[pageUrl]
        if (cached != null) return@withContext cached
        
        val qualities = mutableListOf<YouTubeQuality>()
        
        try {
            // 1. Fonte primária: ytInitialPlayerResponse
            val playerQualities = extractFromYtInitialPlayerResponse(webView)
            qualities.addAll(playerQualities)
            
            // 2. Fallback: Performance API com mapeamento itag
            if (qualities.isEmpty()) {
                val performanceQualities = extractFromPerformanceAPI(webView)
                qualities.addAll(performanceQualities)
            }
            
            // 3. Último recurso: Varredura DOM por googlevideo URLs
            if (qualities.isEmpty()) {
                val domQualities = extractFromDOMScan(webView)
                qualities.addAll(domQualities)
            }
            
            // Filtrar e deduplificar
            val finalQualities = qualities
                .filter { it.quality != "Auto" } // Eliminar "Auto" completamente
                .distinctBy { "${it.quality}_${it.hasAudio}" }
                .sortedWith(compareByDescending<YouTubeQuality> { extractHeightFromQuality(it.quality) }
                    .thenByDescending { if (it.hasAudio) 1 else 0 })
            
            // Cache por 15 minutos
            qualityCache[pageUrl] = finalQualities
            scope.launch {
                delay(TimeUnit.MINUTES.toMillis(15))
                qualityCache.remove(pageUrl)
            }
            
            return@withContext finalQualities
            
        } catch (e: Exception) {
            return@withContext emptyList()
        }
    }
    
    /**
     * Extração de ytInitialPlayerResponse (fonte primária)
     */
    private suspend fun extractFromYtInitialPlayerResponse(
        webView: WebView
    ): List<YouTubeQuality> = withContext(Dispatchers.Main) {
        
        val script = """
            (function() {
                try {
                    if (typeof window.ytInitialPlayerResponse === 'undefined') {
                        return null;
                    }
                    
                    var playerResponse = window.ytInitialPlayerResponse;
                    if (!playerResponse || !playerResponse.streamingData) {
                        return null;
                    }
                    
                    var streamingData = playerResponse.streamingData;
                    var allFormats = [];
                    
                    // Formatos progressivos (vídeo + áudio)
                    if (streamingData.formats) {
                        streamingData.formats.forEach(function(format) {
                            if (format.url && format.itag) {
                                allFormats.push({
                                    itag: format.itag,
                                    url: format.url,
                                    qualityLabel: format.qualityLabel || format.quality,
                                    width: format.width,
                                    height: format.height,
                                    fps: format.fps,
                                    mimeType: format.mimeType,
                                    contentLength: format.contentLength,
                                    hasAudio: true
                                });
                            }
                        });
                    }
                    
                    // Formatos adaptativos (apenas vídeo)
                    if (streamingData.adaptiveFormats) {
                        streamingData.adaptiveFormats.forEach(function(format) {
                            if (format.url && format.itag && format.mimeType && format.mimeType.startsWith('video/')) {
                                allFormats.push({
                                    itag: format.itag,
                                    url: format.url,
                                    qualityLabel: format.qualityLabel || format.quality,
                                    width: format.width,
                                    height: format.height,
                                    fps: format.fps,
                                    mimeType: format.mimeType,
                                    contentLength: format.contentLength,
                                    hasAudio: false
                                });
                            }
                        });
                    }
                    
                    return JSON.stringify(allFormats);
                    
                } catch (e) {
                    return null;
                }
            })();
        """.trimIndent()
        
        return@withContext suspendCancellableCoroutine { continuation ->
            webView.evaluateJavascript(script) { result ->
                try {
                    if (result != "null" && result != "\"null\"" && result.isNotEmpty()) {
                        val cleanResult = result.removeSurrounding("\"")
                            .replace("\\\"", "\"")
                            .replace("\\\\", "\\")
                        
                        val jsonArray = org.json.JSONArray(cleanResult)
                        val qualities = mutableListOf<YouTubeQuality>()
                        
                        for (i in 0 until jsonArray.length()) {
                            val format = jsonArray.getJSONObject(i)
                            val itag = format.getInt("itag")
                            
                            // Mapear qualidade usando tabela atualizada
                            val quality = ITAG_QUALITY_MAP[itag] 
                                ?: format.optString("qualityLabel").takeIf { it.isNotEmpty() && it != "null" }
                                ?: inferQualityFromDimensions(format.optInt("width", 0), format.optInt("height", 0))
                                ?: continue // Pular se não conseguir determinar qualidade
                            
                            qualities.add(
                                YouTubeQuality(
                                    itag = itag,
                                    quality = quality,
                                    width = format.optInt("width").takeIf { it > 0 },
                                    height = format.optInt("height").takeIf { it > 0 },
                                    fps = format.optInt("fps").takeIf { it > 0 },
                                    hasAudio = format.optBoolean("hasAudio", false),
                                    codec = ITAG_CODEC_MAP[itag] ?: extractCodecFromMimeType(format.optString("mimeType")),
                                    url = format.getString("url"),
                                    contentLength = format.optLong("contentLength").takeIf { it > 0 }
                                )
                            )
                        }
                        
                        continuation.resume(qualities, null)
                    } else {
                        continuation.resume(emptyList(), onCancellation = {})
                    }
                } catch (e: Exception) {
                    continuation.resume(emptyList(), null)
                }
            }
        }
    }
    
    /**
     * Fallback: Performance API com mapeamento itag atualizado
     */
    private suspend fun extractFromPerformanceAPI(
        webView: WebView
    ): List<YouTubeQuality> = withContext(Dispatchers.Main) {
        
        val script = """
            (function() {
                try {
                    if (!window.performance || !window.performance.getEntriesByType) {
                        return null;
                    }
                    
                    var entries = window.performance.getEntriesByType('resource');
                    var videoEntries = [];
                    
                    entries.forEach(function(entry) {
                        var url = entry.name;
                        if (url.includes('googlevideo') || url.includes('videoplayback')) {
                            // Nunca incluir window.location.href
                            if (url === window.location.href) return;
                            
                            var urlObj = new URL(url);
                            var itag = urlObj.searchParams.get('itag');
                            
                            if (itag) {
                                videoEntries.push({
                                    url: url,
                                    itag: parseInt(itag),
                                    transferSize: entry.transferSize || 0
                                });
                            }
                        }
                    });
                    
                    return JSON.stringify(videoEntries);
                    
                } catch (e) {
                    return null;
                }
            })();
        """.trimIndent()
        
        return@withContext suspendCancellableCoroutine { continuation ->
            webView.evaluateJavascript(script) { result ->
                try {
                    if (result != "null" && result != "\"null\"" && result.isNotEmpty()) {
                        val cleanResult = result.removeSurrounding("\"")
                            .replace("\\\"", "\"")
                        
                        val jsonArray = org.json.JSONArray(cleanResult)
                        val qualities = mutableListOf<YouTubeQuality>()
                        
                        for (i in 0 until jsonArray.length()) {
                            val entry = jsonArray.getJSONObject(i)
                            val itag = entry.getInt("itag")
                            
                            // Usar tabela completa de mapeamento
                            val quality = ITAG_QUALITY_MAP[itag] ?: continue
                            
                            qualities.add(
                                YouTubeQuality(
                                    itag = itag,
                                    quality = quality,
                                    width = null,
                                    height = extractHeightFromQuality(quality),
                                    fps = if (quality.contains("60")) 60 else 30,
                                    hasAudio = ITAG_AUDIO_MAP.contains(itag),
                                    codec = ITAG_CODEC_MAP[itag],
                                    url = entry.getString("url"),
                                    contentLength = entry.optLong("transferSize").takeIf { it > 0 }
                                )
                            )
                        }
                        
                        continuation.resume(qualities, null)
                    } else {
                        continuation.resume(emptyList(), null)
                    }
                } catch (e: Exception) {
                    continuation.resume(emptyList(), null)
                }
            }
        }
    }
    
    /**
     * Último recurso: Varredura DOM por URLs googlevideo
     */
    private suspend fun extractFromDOMScan(
        webView: WebView
    ): List<YouTubeQuality> = withContext(Dispatchers.Main) {
        
        val script = """
            (function() {
                try {
                    var html = document.documentElement.outerHTML;
                    var matches = html.match(/https:\/\/[^\s"'<>()]*googlevideo[^\s"'<>()]*/g) || [];
                    var videoUrls = [];
                    
                    matches.forEach(function(url) {
                        // Nunca incluir window.location.href
                        if (url === window.location.href) return;
                        
                        try {
                            var urlObj = new URL(url);
                            var itag = urlObj.searchParams.get('itag');
                            
                            if (itag && !videoUrls.some(v => v.itag === parseInt(itag))) {
                                videoUrls.push({
                                    url: url,
                                    itag: parseInt(itag)
                                });
                            }
                        } catch (e) {}
                    });
                    
                    return JSON.stringify(videoUrls);
                    
                } catch (e) {
                    return null;
                }
            })();
        """.trimIndent()
        
        return@withContext suspendCancellableCoroutine { continuation ->
            webView.evaluateJavascript(script) { result ->
                try {
                    if (result != "null" && result != "\"null\"" && result.isNotEmpty()) {
                        val cleanResult = result.removeSurrounding("\"")
                            .replace("\\\"", "\"")
                        
                        val jsonArray = org.json.JSONArray(cleanResult)
                        val qualities = mutableListOf<YouTubeQuality>()
                        
                        for (i in 0 until jsonArray.length()) {
                            val entry = jsonArray.getJSONObject(i)
                            val itag = entry.getInt("itag")
                            
                            val quality = ITAG_QUALITY_MAP[itag] ?: continue
                            
                            qualities.add(
                                YouTubeQuality(
                                    itag = itag,
                                    quality = quality,
                                    width = null,
                                    height = extractHeightFromQuality(quality),
                                    fps = if (quality.contains("60")) 60 else 30,
                                    hasAudio = ITAG_AUDIO_MAP.contains(itag),
                                    codec = ITAG_CODEC_MAP[itag],
                                    url = entry.getString("url"),
                                    contentLength = null
                                )
                            )
                        }
                        
                        continuation.resume(qualities, null)
                    } else {
                        continuation.resume(emptyList(), null)
                    }
                } catch (e: Exception) {
                    continuation.resume(emptyList(), null)
                }
            }
        }
    }
    
    // Funções auxiliares
    private fun isYouTubeUrl(url: String): Boolean {
        return url.contains("youtube.com") || url.contains("youtu.be")
    }
    
    private fun inferQualityFromDimensions(width: Int, height: Int): String? {
        return when {
            height >= 2160 -> "2160p"
            height >= 1440 -> "1440p"
            height >= 1080 -> "1080p"
            height >= 720 -> "720p"
            height >= 480 -> "480p"
            height >= 360 -> "360p"
            height >= 240 -> "240p"
            height >= 144 -> "144p"
            else -> null
        }
    }
    
    private fun extractCodecFromMimeType(mimeType: String): String? {
        return when {
            mimeType.contains("av01") -> "av01"
            mimeType.contains("vp9") -> "vp9"
            mimeType.contains("avc1") -> "avc1"
            mimeType.contains("h264") -> "avc1"
            else -> null
        }
    }
    
    private fun extractHeightFromQuality(quality: String): Int? {
        return Regex("(\\d+)p").find(quality)?.groupValues?.get(1)?.toIntOrNull()
    }
    
    /**
     * Limpar cache de uma página específica
     */
    fun clearPageCache(pageUrl: String) {
        qualityCache.remove(pageUrl)
    }
    
    /**
     * Limpar todo o cache
     */
    fun clearAllCache() {
        qualityCache.clear()
    }
}
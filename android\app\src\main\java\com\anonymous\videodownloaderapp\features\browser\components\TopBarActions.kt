package com.anonymous.videodownloaderapp.features.browser.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.anonymous.videodownloaderapp.R

@Composable
fun TopBarActions(
    onHomeClick: () -> Unit,
    onDownloadsClick: () -> Unit,
    onMenuClick: () -> Unit,
    showSettingsMenu: Boolean,
    onSettingsMenuDismiss: () -> Unit,
    onSettingsClick: () -> Unit,
    onRefreshClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // Botão Home (primeiro)
        IconButton(
            onClick = onHomeClick,
            modifier = Modifier.size(36.dp)
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_globe),
                contentDescription = stringResource(R.string.home),
                modifier = Modifier.size(20.dp),
                tint = MaterialTheme.colorScheme.onSurface
            )
        }
        
        // Botão Downloads (segundo)
        IconButton(
            onClick = onDownloadsClick,
            modifier = Modifier.size(36.dp)
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_cloud_download),
                contentDescription = stringResource(R.string.downloads),
                modifier = Modifier.size(20.dp),
                tint = MaterialTheme.colorScheme.onSurface
            )
        }
        
        // Menu com dropdown (terceiro)
        Box {
            IconButton(
                onClick = onMenuClick,
                modifier = Modifier.size(36.dp)
            ) {
                Icon(
                    Icons.Default.MoreVert,
                    contentDescription = stringResource(R.string.menu),
                    modifier = Modifier.size(20.dp),
                    tint = MaterialTheme.colorScheme.onSurface
                )
            }
            
            DropdownMenu(
                expanded = showSettingsMenu,
                onDismissRequest = onSettingsMenuDismiss
            ) {
                DropdownMenuItem(
                    text = { Text(stringResource(R.string.configuracoes)) },
                    onClick = {
                        onSettingsMenuDismiss()
                        onSettingsClick()
                    },
                    leadingIcon = {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_settings),
                            contentDescription = null
                        )
                    }
                )
                
                DropdownMenuItem(
                    text = { Text(stringResource(R.string.downloads)) },
                    onClick = {
                        onSettingsMenuDismiss()
                        onDownloadsClick()
                    },
                    leadingIcon = {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_cloud_download),
                            contentDescription = null
                        )
                    }
                )
                
                DropdownMenuItem(
                    text = { Text(stringResource(R.string.recarregar)) },
                    onClick = {
                        onSettingsMenuDismiss()
                        onRefreshClick()
                    },
                    leadingIcon = {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_refresh),
                            contentDescription = null
                        )
                    }
                )
            }
        }
    }
}
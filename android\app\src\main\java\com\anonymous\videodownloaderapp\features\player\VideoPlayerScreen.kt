package com.anonymous.videodownloaderapp.features.player

import android.app.Activity
import android.app.PictureInPictureParams
import android.net.Uri
import android.os.Build
import android.util.Rational
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.navigation.NavHostController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import kotlinx.coroutines.delay
import java.net.URLDecoder

import com.anonymous.videodownloaderapp.services.AudioSessionManager

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VideoPlayerScreen(navController: NavHostController, videoUrl: String) {
    val context = LocalContext.current
    val audioSessionManager = remember { AudioSessionManager(context) }

    val decodedUrl = remember(videoUrl) {
        runCatching { URLDecoder.decode(videoUrl, "UTF-8") }.getOrDefault(videoUrl)
    }

    var exoPlayer by remember { mutableStateOf<ExoPlayer?>(null) }

    var isPlaying by remember { mutableStateOf(true) }
    var duration by remember { mutableStateOf(0L) }
    var position by remember { mutableStateOf(0L) }
    var buffered by remember { mutableStateOf(0L) }
    var error by remember { mutableStateOf<String?>(null) }

    DisposableEffect(exoPlayer) {
        val player = exoPlayer ?: return@DisposableEffect onDispose {}
        val listener = object : Player.Listener {
            override fun onPlaybackStateChanged(playbackState: Int) {
                duration = player.duration.takeIf { it >= 0 } ?: 0L
            }

            override fun onIsPlayingChanged(isPlayingNew: Boolean) {
                isPlaying = isPlayingNew
            }

            override fun onPlayerError(errorNew: PlaybackException) {
                error = errorNew.localizedMessage ?: errorNew.message
            }
        }
        player.addListener(listener)

        onDispose {
            player.removeListener(listener)
            player.release()
            audioSessionManager.releaseAudioFocus()
        }
    }

    LaunchedEffect(decodedUrl, exoPlayer) {
        val player = exoPlayer ?: return@LaunchedEffect
        val item = MediaItem.fromUri(Uri.parse(decodedUrl))
        player.setMediaItem(item)
        player.prepare()
        player.playWhenReady = true
        audioSessionManager.prepareForVideoPlayback()
    }

    LaunchedEffect(isPlaying, exoPlayer) {
        while (true) {
            val p = exoPlayer
            if (p != null) {
                position = p.currentPosition.coerceAtLeast(0L)
                buffered = p.bufferedPosition.coerceAtLeast(0L)
                duration = p.duration.takeIf { it >= 0 } ?: duration
            }
            delay(250)
        }
    }

    BackHandler { navController.popBackStack() }

    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = { Text("Player") },
                actions = {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        IconButton(onClick = {
                            val activity = (context as? Activity) ?: return@IconButton
                            val params = PictureInPictureParams.Builder()
                                .setAspectRatio(Rational(16, 9))
                                .build()
                            activity.enterPictureInPictureMode(params)
                        }) {
                            Text("PIP", style = MaterialTheme.typography.titleMedium)
                        }
                    }
                }
            )
        }
    ) { inner ->
        Column(
            modifier = Modifier
                .padding(inner)
                .fillMaxSize()
        ) {
            AndroidView(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(220.dp),
                factory = { ctx ->
                    val player = ExoPlayer.Builder(ctx).build()
                    exoPlayer = player
                    PlayerView(ctx).apply {
                        useController = false
                        this.player = player
                    }
                },
                update = { view ->
                    view.player = exoPlayer
                }
            )

            Spacer(Modifier.height(12.dp))

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                IconButton(onClick = {
                    val p = exoPlayer ?: return@IconButton
                    val target = (p.currentPosition - 10_000L).coerceAtLeast(0L)
                    p.seekTo(target)
                }) {
                    Text("⟲10s")
                }
                IconButton(onClick = {
                    val p = exoPlayer ?: return@IconButton
                    if (p.isPlaying) p.pause() else p.play()
                }) {
                    Text(if (isPlaying) "Pause" else "Play", style = MaterialTheme.typography.titleMedium)
                }
                IconButton(onClick = {
                    val p = exoPlayer ?: return@IconButton
                    val dur = p.duration.takeIf { it > 0 } ?: Long.MAX_VALUE
                    val target = (p.currentPosition + 10_000L).coerceAtMost(dur)
                    p.seekTo(target)
                }) {
                    Text("10s⟳")
                }
            }

            Column(Modifier.padding(horizontal = 12.dp)) {
                val dur = duration.takeIf { it > 0 } ?: 1L
                val prog = (position.toFloat() / dur.toFloat()).coerceIn(0f, 1f)
                Slider(
                    value = prog,
                    onValueChange = { fraction ->
                        val p = exoPlayer ?: return@Slider
                        val seekTo = (fraction.coerceIn(0f, 1f) * dur).toLong()
                        p.seekTo(seekTo)
                    }
                )
                Text(
                    text = "${formatMs(position)} / ${formatMs(duration)}",
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            error?.let {
                Spacer(Modifier.height(8.dp))
                Text("Erro: $it", color = MaterialTheme.colorScheme.error)
            }
        }
    }
}

private fun formatMs(ms: Long): String {
    val totalSec = (ms / 1000).toInt().coerceAtLeast(0)
    val h = totalSec / 3600
    val m = (totalSec % 3600) / 60
    val s = totalSec % 60
    return if (h > 0) "%d:%02d:%02d".format(h, m, s) else "%02d:%02d".format(m, s)
}
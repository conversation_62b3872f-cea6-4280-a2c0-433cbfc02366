package com.anonymous.videodownloaderapp.features.browser;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\n\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\rX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\rX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0017"}, d2 = {"Lcom/anonymous/videodownloaderapp/features/browser/BrowserConstants;", "", "()V", "ADDRESS_BAR_ANIMATION_DURATION_MS", "", "ADDRESS_BAR_COMPACT_HEIGHT_DP", "ADDRESS_BAR_EXPANDED_HEIGHT_DP", "ADDRESS_BAR_MAX_DISPLAY_LENGTH", "DEFAULT_URL", "", "DEFAULT_VIDEO_MIME_TYPE", "DEFAULT_VIDEO_TITLE", "INITIAL_VIDEO_DETECTION_DELAY_MS", "", "JS_INTERFACE_NAME", "MAX_DETECTED_VIDEOS", "REFRESH_DETECTION_DELAY_MS", "SEARCH_URL_TEMPLATE", "SECONDARY_VIDEO_DETECTION_DELAY_MS", "VIDEO_DETECTOR_SCRIPT_PATH", "VIDEO_URL_MIN_LENGTH", "WEBVIEW_CACHE_SIZE_MB", "WEBVIEW_USER_AGENT_SUFFIX", "app_debug"})
public final class BrowserConstants {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DEFAULT_URL = "https://www.google.com";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SEARCH_URL_TEMPLATE = "https://www.google.com/search?q=%s";
    public static final long INITIAL_VIDEO_DETECTION_DELAY_MS = 3000L;
    public static final long SECONDARY_VIDEO_DETECTION_DELAY_MS = 6000L;
    public static final long REFRESH_DETECTION_DELAY_MS = 1000L;
    public static final int ADDRESS_BAR_MAX_DISPLAY_LENGTH = 30;
    public static final int ADDRESS_BAR_COMPACT_HEIGHT_DP = 40;
    public static final int ADDRESS_BAR_EXPANDED_HEIGHT_DP = 56;
    public static final int ADDRESS_BAR_ANIMATION_DURATION_MS = 300;
    public static final int WEBVIEW_CACHE_SIZE_MB = 100;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String WEBVIEW_USER_AGENT_SUFFIX = " VideoDownloader/1.0";
    public static final int MAX_DETECTED_VIDEOS = 50;
    public static final int VIDEO_URL_MIN_LENGTH = 10;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DEFAULT_VIDEO_TITLE = "V\u00eddeo";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DEFAULT_VIDEO_MIME_TYPE = "video/mp4";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String JS_INTERFACE_NAME = "VideoDetector";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String VIDEO_DETECTOR_SCRIPT_PATH = "video_detector_enhanced.js";
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.features.browser.BrowserConstants INSTANCE = null;
    
    private BrowserConstants() {
        super();
    }
}
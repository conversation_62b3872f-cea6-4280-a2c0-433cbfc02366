<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="splashscreen_background">#FFFFFFFF</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">VideoDownloader</string>
    <string name="atualizar">Atualizar</string>
    <string name="baixado">Baixado</string>
    <string name="baixar">Baixar</string>
    <string name="configuracoes">Configurações</string>
    <string name="digite_url">Digite uma URL…</string>
    <string name="download_channel_desc">Background media downloads</string>
    <string name="download_channel_name">Downloads</string>
    <string name="downloads">Downloads</string>
    <string name="fechar">Fechar</string>
    <string name="home">Home</string>
    <string name="inicio">Início</string>
    <string name="menu">Menu</string>
    <string name="navegue_para_videos">Navegue para uma página com vídeos para detectá-los</string>
    <string name="nenhum_video_encontrado">Nenhum vídeo encontrado</string>
    <string name="nova_aba">Nova aba</string>
    <string name="recarregar">Recarregar</string>
    <string name="video_title">Vídeo - %1$s</string>
    <string name="videos">Vídeos</string>
    <string name="videos_detectados">Vídeos Detectados (%1$d)</string>
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    </style>
    <style name="Theme.App" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style>
</resources>
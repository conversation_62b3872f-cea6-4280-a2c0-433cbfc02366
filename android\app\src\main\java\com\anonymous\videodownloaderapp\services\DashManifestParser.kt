package com.anonymous.videodownloaderapp.services

import android.content.Context
import okhttp3.OkHttpClient
import okhttp3.Request
import org.w3c.dom.Element
import java.util.concurrent.TimeUnit
import javax.xml.parsers.DocumentBuilderFactory

/**
 * Minimal DASH (MPD) parser to extract video Representation qualities.
 * Focus: collect width/height, bandwidth, codecs and resolve media/BaseURL.
 * Note: For segmented streams, we surface the BaseURL (manifest-level or rep-level).
 */
class DashManifestParser(
    context: Context? = null,
    private val http: OkHttpClient = run {
        val builder = OkHttpClient.Builder()
            .connectTimeout(8, TimeUnit.SECONDS)
            .readTimeout(8, TimeUnit.SECONDS)
        if (context != null) {
            HybridVideoDetectionService.getInstance(context).configureOkHttpClient(builder)
        }
        builder.build()
    }
) {

    data class Quality(
        val label: String,        // e.g., "1080p"
        val width: Int?,
        val height: Int?,
        val bandwidth: Long?,     // bits per second
        val codecs: String?,
        val url: String,
        val type: String = "DASH"
    )

    fun parse(url: String, maxBytes: Long = 1_000_000L): List<Quality> {
        val xml = fetchXmlLimited(url, maxBytes) ?: return emptyList()

        val out = mutableListOf<Quality>()
        val mpdEl = xml.documentElement ?: return emptyList()

        // MPD BaseURL (optional)
        val mpdBase = firstChildText(mpdEl, "BaseURL")

        val period = firstChild(mpdEl, "Period") ?: return emptyList()
        val adaptationSets = period.getElementsByTagName("AdaptationSet")

        for (i in 0 until adaptationSets.length) {
            val aset = adaptationSets.item(i) as? Element ?: continue
            val mimeType = aset.getAttribute("mimeType") ?: ""
            // Focus only on video adaptation sets for quality variants
            if (!mimeType.lowercase().startsWith("video/")) continue

            // AdaptationSet BaseURL (optional)
            val asetBase = firstChildText(aset, "BaseURL") ?: mpdBase

            val reps = aset.getElementsByTagName("Representation")
            for (r in 0 until reps.length) {
                val rep = reps.item(r) as? Element ?: continue
                val width = rep.getAttribute("width")?.toIntOrNull()
                val height = rep.getAttribute("height")?.toIntOrNull()
                val bw = rep.getAttribute("bandwidth")?.toLongOrNull()
                val codecs = rep.getAttribute("codecs")

                // Representation BaseURL overrides previous bases
                val repBase = firstChildText(rep, "BaseURL") ?: asetBase

                val resolvedUrl = repBase ?: url // fallback to MPD url if no BaseURL
                val label = height?.let { normalizeHeightToLabel(it) }
                    ?: inferLabelFromString(resolvedUrl)
                    ?: "Auto"

                out += Quality(
                    label = label,
                    width = width,
                    height = height,
                    bandwidth = bw,
                    codecs = codecs.ifBlank { null },
                    url = resolvedUrl
                )
            }
        }

        return dedupeKeepBest(out).sortedByDescending { extractHeight(it.label) ?: 0 }
    }

    private fun fetchXmlLimited(url: String, maxBytes: Long): org.w3c.dom.Document? {
        val req = Request.Builder().url(url).get().build()
        http.newCall(req).execute().use { resp ->
            if (!resp.isSuccessful) return null
            val body = resp.body ?: return null
            val source = body.source()
            val bytes = okio.Buffer()
            var total = 0L
            val chunk = 8 * 1024L
            while (true) {
                val read = source.read(bytes, chunk)
                if (read == -1L) break
                total += read
                if (total > maxBytes) break
                if (source.exhausted()) break
            }
            val factory = DocumentBuilderFactory.newInstance()
            val builder = factory.newDocumentBuilder()
            return builder.parse(bytes.inputStream())
        }
    }

    private fun firstChild(parent: Element, name: String): Element? {
        val list = parent.getElementsByTagName(name)
        return if (list.length > 0) list.item(0) as? Element else null
    }

    private fun firstChildText(parent: Element, name: String): String? {
        return firstChild(parent, name)?.textContent?.trim()?.takeIf { it.isNotBlank() }
    }

    private fun normalizeHeightToLabel(h: Int): String {
        val common = listOf(2160, 1440, 1080, 720, 480, 360, 240, 144)
        val best = if (common.contains(h)) h else common.minByOrNull { kotlin.math.abs(it - h) } ?: h
        return "${'$'}{best}p"
    }

    private fun inferLabelFromString(s: String): String? {
        val lower = s.lowercase()
        Regex("(\\d{3,4})p").find(lower)?.let { return "${'$'}{it.groupValues[1]}p" }
        if (listOf("2160", "4k", "uhd").any { lower.contains(it) }) return "2160p"
        if (lower.contains("1440")) return "1440p"
        if (listOf("1080", "fhd").any { lower.contains(it) }) return "1080p"
        if (listOf("720", "hd").any { lower.contains(it) }) return "720p"
        if (listOf("480", "sd").any { lower.contains(it) }) return "480p"
        if (lower.contains("360")) return "360p"
        if (lower.contains("240")) return "240p"
        if (lower.contains("144")) return "144p"
        return null
    }

    private fun extractHeight(label: String?): Int? {
        if (label.isNullOrBlank()) return null
        return Regex("(\\d{3,4})p", RegexOption.IGNORE_CASE).find(label)?.groupValues?.getOrNull(1)?.toIntOrNull()
    }

    private fun dedupeKeepBest(list: List<Quality>): List<Quality> {
        val byUrl = LinkedHashMap<String, Quality>()
        list.forEach { q ->
            val existing = byUrl[q.url]
            if (existing == null) {
                byUrl[q.url] = q
            } else {
                val newH = extractHeight(q.label) ?: 0
                val oldH = extractHeight(existing.label) ?: 0
                if (newH > oldH) byUrl[q.url] = q
            }
        }
        return byUrl.values.toList()
    }
}
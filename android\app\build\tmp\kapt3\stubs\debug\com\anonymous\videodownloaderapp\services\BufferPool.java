package com.anonymous.videodownloaderapp.services;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0012\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\b\u001a\u00020\u0007J\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u0007R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/BufferPool;", "", "bufferSize", "", "(I)V", "pool", "Lkotlin/collections/ArrayDeque;", "", "acquire", "release", "", "buffer", "app_debug"})
public final class BufferPool {
    private final int bufferSize = 0;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.collections.ArrayDeque<byte[]> pool = null;
    
    public BufferPool(int bufferSize) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final byte[] acquire() {
        return null;
    }
    
    public final void release(@org.jetbrains.annotations.NotNull()
    byte[] buffer) {
    }
}
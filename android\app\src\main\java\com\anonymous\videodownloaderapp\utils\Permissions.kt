package com.anonymous.videodownloaderapp.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.result.ActivityResultCaller
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat

object Permissions {

    fun requiredRuntimePermissions(): Array<String> {
        val perms = mutableListOf<String>()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            perms += Manifest.permission.READ_MEDIA_VIDEO
            perms += Manifest.permission.POST_NOTIFICATIONS
        } else {
            // For API < 33, downloads via MediaStore do not require READ/WRITE external storage.
            // We keep runtime list empty for legacy to avoid deprecated permissions.
        }
        return perms.distinct().toTypedArray()
    }

    fun hasAll(context: Context): Boolean =
        requiredRuntimePermissions().all { p -> ContextCompat.checkSelfPermission(context, p) == PackageManager.PERMISSION_GRANTED }

    fun missing(context: Context): List<String> =
        requiredRuntimePermissions().filter { p -> ContextCompat.checkSelfPermission(context, p) != PackageManager.PERMISSION_GRANTED }

    /**
     * Helper to register a permission launcher and request missing permissions.
     * Usage from an Activity or Fragment:
     *
     * val launcher = Permissions.registerRequester(this) { granted -> ... }
     * Permissions.requestIfNeeded(this, launcher)
     */
    fun registerRequester(
        caller: ActivityResultCaller,
        onResult: (granted: Boolean) -> Unit
    ) = caller.registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
        val allGranted = result.values.all { it }
        onResult(allGranted)
    }

    fun requestIfNeeded(
        activity: Activity,
        launcher: androidx.activity.result.ActivityResultLauncher<Array<String>>
    ) {
        val need = missing(activity)
        if (need.isNotEmpty()) {
            launcher.launch(need.toTypedArray())
        }
    }
}
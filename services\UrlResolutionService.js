// URL Resolution Service
// Handles redirects, shortened URLs, and complex URL patterns

class UrlResolutionService {
  
  // Common URL shorteners and redirect patterns
  static URL_SHORTENERS = [
    'bit.ly', 'tinyurl.com', 't.co', 'goo.gl', 'ow.ly',
    'short.link', 'tiny.cc', 'is.gd', 'buff.ly', 'ift.tt'
  ];

  // Platform-specific redirect patterns
  static REDIRECT_PATTERNS = {
    youtube: [
      /youtu\.be\/([^?]+)/,
      /youtube\.com\/redirect\?.*q=([^&]+)/,
      /youtube\.com\/attribution_link\?.*u=([^&]+)/
    ],
    instagram: [
      /instagram\.com\/.*\?.*igshid=/,
      /instagr\.am\/p\/([^\/]+)/
    ],
    tiktok: [
      /vm\.tiktok\.com\/([^\/]+)/,
      /tiktok\.com\/t\/([^\/]+)/
    ],
    facebook: [
      /fb\.watch\/([^\/]+)/,
      /facebook\.com\/l\.php\?u=([^&]+)/
    ],
    twitter: [
      /t\.co\/([^\/]+)/,
      /twitter\.com\/intent\/.*url=([^&]+)/
    ]
  };

  // Resolve URL through multiple strategies
  static async resolveVideoUrl(url, platform = null) {
    try {
      console.log('Resolving URL:', url);

      // Step 1: Check if it's already a direct video URL
      if (this.isDirectVideoUrl(url)) {
        return { resolved: url, redirects: 0 };
      }

      // Step 2: Handle platform-specific redirects
      if (platform) {
        const platformResolved = this.resolvePlatformRedirect(url, platform);
        if (platformResolved !== url) {
          return { resolved: platformResolved, redirects: 1 };
        }
      }

      // Step 3: Handle URL shorteners
      if (this.isShortUrl(url)) {
        const resolved = await this.resolveShortUrl(url);
        if (resolved !== url) {
          return { resolved, redirects: 1 };
        }
      }

      // Step 4: Extract embedded URLs
      const embeddedUrl = this.extractEmbeddedUrl(url);
      if (embeddedUrl !== url) {
        return { resolved: embeddedUrl, redirects: 1 };
      }

      // Return original URL if no resolution needed
      return { resolved: url, redirects: 0 };

    } catch (error) {
      console.error('Error resolving URL:', error);
      return { resolved: url, redirects: 0, error: error.message };
    }
  }

  // Check if URL is a direct video URL
  static isDirectVideoUrl(url) {
    const directPatterns = [
      /\.(mp4|webm|ogg|avi|mov|wmv|flv|mkv|m4v)(\?[^"'\s]*)?$/i,
      /youtube\.com\/watch\?v=/,
      /vimeo\.com\/\d+/,
      /dailymotion\.com\/video\//,
      /twitch\.tv\/videos\//
    ];

    return directPatterns.some(pattern => pattern.test(url));
  }

  // Check if URL is a shortened URL
  static isShortUrl(url) {
    try {
      const hostname = new URL(url).hostname.toLowerCase();
      return this.URL_SHORTENERS.some(shortener => hostname.includes(shortener));
    } catch (e) {
      return false;
    }
  }

  // Resolve platform-specific redirects
  static resolvePlatformRedirect(url, platform) {
    const patterns = this.REDIRECT_PATTERNS[platform];
    if (!patterns) return url;

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        switch (platform) {
          case 'youtube':
            if (pattern.source.includes('youtu.be')) {
              return `https://www.youtube.com/watch?v=${match[1]}`;
            } else if (pattern.source.includes('redirect') || pattern.source.includes('attribution')) {
              try {
                return decodeURIComponent(match[1]);
              } catch (e) {
                return url;
              }
            }
            break;

          case 'instagram':
            if (pattern.source.includes('instagr.am')) {
              return `https://www.instagram.com/p/${match[1]}/`;
            }
            break;

          case 'tiktok':
            // TikTok short URLs need special handling
            return url; // Keep original for now

          case 'facebook':
            if (pattern.source.includes('fb.watch')) {
              return `https://www.facebook.com/watch/?v=${match[1]}`;
            } else if (pattern.source.includes('l.php')) {
              try {
                return decodeURIComponent(match[1]);
              } catch (e) {
                return url;
              }
            }
            break;

          case 'twitter':
            // Twitter short URLs are handled differently
            return url;
        }
      }
    }

    return url;
  }

  // Resolve shortened URLs (simplified version)
  static async resolveShortUrl(url) {
    try {
      // In a real implementation, you would make a HEAD request to follow redirects
      // For now, we'll return the original URL
      console.log('Short URL detected, would resolve:', url);
      return url;
    } catch (error) {
      console.error('Error resolving short URL:', error);
      return url;
    }
  }

  // Extract embedded URLs from redirect parameters
  static extractEmbeddedUrl(url) {
    try {
      const urlObj = new URL(url);
      
      // Common redirect parameters
      const redirectParams = ['url', 'u', 'q', 'link', 'target', 'redirect'];
      
      for (const param of redirectParams) {
        const value = urlObj.searchParams.get(param);
        if (value) {
          try {
            const decodedUrl = decodeURIComponent(value);
            if (decodedUrl.startsWith('http')) {
              return decodedUrl;
            }
          } catch (e) {
            // Continue to next parameter
          }
        }
      }

      return url;
    } catch (e) {
      return url;
    }
  }

  // Extract video ID from various URL formats
  static extractVideoId(url, platform) {
    const patterns = {
      youtube: [
        /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
        /youtube\.com\/v\/([^&\n?#]+)/
      ],
      vimeo: [
        /vimeo\.com\/(?:video\/)?(\d+)/,
        /player\.vimeo\.com\/video\/(\d+)/
      ],
      instagram: [
        /instagram\.com\/(?:p|reel|tv)\/([^\/\?]+)/
      ],
      tiktok: [
        /tiktok\.com\/@[^\/]+\/video\/(\d+)/,
        /tiktok\.com\/v\/(\d+)/
      ],
      facebook: [
        /facebook\.com\/.*\/videos\/(\d+)/,
        /facebook\.com\/watch\/?\?v=(\d+)/
      ],
      twitter: [
        /(?:twitter\.com|x\.com)\/.*\/status\/(\d+)/
      ],
      dailymotion: [
        /dailymotion\.com\/video\/([^_\?]+)/
      ]
    };

    const platformPatterns = patterns[platform];
    if (!platformPatterns) return null;

    for (const pattern of platformPatterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }

    return null;
  }

  // Build canonical URL from video ID
  static buildCanonicalUrl(videoId, platform) {
    const templates = {
      youtube: `https://www.youtube.com/watch?v=${videoId}`,
      vimeo: `https://vimeo.com/${videoId}`,
      instagram: `https://www.instagram.com/p/${videoId}/`,
      tiktok: `https://www.tiktok.com/v/${videoId}`,
      facebook: `https://www.facebook.com/watch/?v=${videoId}`,
      twitter: `https://twitter.com/i/status/${videoId}`,
      dailymotion: `https://www.dailymotion.com/video/${videoId}`
    };

    return templates[platform] || null;
  }

  // Validate and clean URL
  static validateAndCleanUrl(url) {
    try {
      // Remove tracking parameters
      const urlObj = new URL(url);
      const trackingParams = [
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term',
        'fbclid', 'gclid', 'igshid', 'ref', 'source', 'campaign'
      ];

      trackingParams.forEach(param => {
        urlObj.searchParams.delete(param);
      });

      return urlObj.toString();
    } catch (e) {
      return url;
    }
  }

  // Get platform from URL
  static getPlatformFromUrl(url) {
    try {
      const hostname = new URL(url).hostname.toLowerCase().replace('www.', '');
      
      if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) return 'youtube';
      if (hostname.includes('vimeo.com')) return 'vimeo';
      if (hostname.includes('instagram.com')) return 'instagram';
      if (hostname.includes('tiktok.com')) return 'tiktok';
      if (hostname.includes('facebook.com')) return 'facebook';
      if (hostname.includes('twitter.com') || hostname.includes('x.com')) return 'twitter';
      if (hostname.includes('dailymotion.com')) return 'dailymotion';
      if (hostname.includes('twitch.tv')) return 'twitch';
      
      return 'generic';
    } catch (e) {
      return 'generic';
    }
  }

  // Comprehensive URL processing
  static async processVideoUrl(url, platform = null) {
    try {
      // Step 1: Validate and clean URL
      const cleanedUrl = this.validateAndCleanUrl(url);
      
      // Step 2: Detect platform if not provided
      const detectedPlatform = platform || this.getPlatformFromUrl(cleanedUrl);
      
      // Step 3: Resolve redirects and short URLs
      const { resolved } = await this.resolveVideoUrl(cleanedUrl, detectedPlatform);
      
      // Step 4: Extract video ID and build canonical URL if possible
      const videoId = this.extractVideoId(resolved, detectedPlatform);
      if (videoId) {
        const canonicalUrl = this.buildCanonicalUrl(videoId, detectedPlatform);
        if (canonicalUrl) {
          return canonicalUrl;
        }
      }
      
      return resolved;
    } catch (error) {
      console.error('Error processing video URL:', error);
      return url; // Return original URL as fallback
    }
  }
}

export default UrlResolutionService;

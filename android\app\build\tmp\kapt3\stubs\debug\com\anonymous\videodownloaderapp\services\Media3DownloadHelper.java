package com.anonymous.videodownloaderapp.services;

/**
 * Media3 helper aligned with androidx.media3 1.4.x API.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J+\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\u0002\u0010\u0015J+\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\u0002\u0010\u0015J\u001f\u0010\u0017\u001a\u00020\u00112\u0006\u0010\u0010\u001a\u00020\u00112\b\u0010\u0013\u001a\u0004\u0018\u00010\u0014H\u0002\u00a2\u0006\u0002\u0010\u0018J\u000e\u0010\u0019\u001a\u00020\r2\u0006\u0010\u001a\u001a\u00020\u001bJ\u0010\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u0010\u0010\u001e\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u000e\u0010\u001f\u001a\u00020\t2\u0006\u0010\u001a\u001a\u00020\u001bJ\u000e\u0010 \u001a\u00020\u000b2\u0006\u0010\u001a\u001a\u00020\u001bJ\u0010\u0010!\u001a\u00020\"2\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\f\u0010#\u001a\u00020$*\u00020\u0011H\u0002R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0005\u001a\n \u0007*\u0004\u0018\u00010\u00060\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006%"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/Media3DownloadHelper;", "", "()V", "dbProvider", "Landroidx/media3/database/DatabaseProvider;", "downloadExecutor", "Ljava/util/concurrent/ExecutorService;", "kotlin.jvm.PlatformType", "downloadManager", "Landroidx/media3/exoplayer/offline/DownloadManager;", "notificationHelper", "Landroidx/media3/exoplayer/offline/DownloadNotificationHelper;", "simpleCache", "Landroidx/media3/datasource/cache/Cache;", "buildDashRequest", "Landroidx/media3/exoplayer/offline/DownloadRequest;", "url", "", "title", "desiredHeight", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/media3/exoplayer/offline/DownloadRequest;", "buildHlsRequest", "cacheKeyFor", "(Ljava/lang/String;Ljava/lang/Integer;)Ljava/lang/String;", "getCache", "context", "Landroid/content/Context;", "getCacheDataSourceFactory", "Landroidx/media3/datasource/cache/CacheDataSource$Factory;", "getDatabaseProvider", "getDownloadManager", "getNotificationHelper", "getUpstreamFactory", "Landroidx/media3/datasource/DataSource$Factory;", "toUriCompat", "Landroid/net/Uri;", "app_debug"})
public final class Media3DownloadHelper {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile androidx.media3.datasource.cache.Cache simpleCache;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile androidx.media3.database.DatabaseProvider dbProvider;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile androidx.media3.exoplayer.offline.DownloadManager downloadManager;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile androidx.media3.exoplayer.offline.DownloadNotificationHelper notificationHelper;
    private static final java.util.concurrent.ExecutorService downloadExecutor = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.services.Media3DownloadHelper INSTANCE = null;
    
    private Media3DownloadHelper() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.media3.datasource.cache.Cache getCache(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    private final androidx.media3.database.DatabaseProvider getDatabaseProvider(android.content.Context context) {
        return null;
    }
    
    private final androidx.media3.datasource.DataSource.Factory getUpstreamFactory(android.content.Context context) {
        return null;
    }
    
    private final androidx.media3.datasource.cache.CacheDataSource.Factory getCacheDataSourceFactory(android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.media3.exoplayer.offline.DownloadNotificationHelper getNotificationHelper(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.media3.exoplayer.offline.DownloadManager getDownloadManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.media3.exoplayer.offline.DownloadRequest buildHlsRequest(@org.jetbrains.annotations.NotNull()
    java.lang.String url, @org.jetbrains.annotations.Nullable()
    java.lang.String title, @org.jetbrains.annotations.Nullable()
    java.lang.Integer desiredHeight) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.media3.exoplayer.offline.DownloadRequest buildDashRequest(@org.jetbrains.annotations.NotNull()
    java.lang.String url, @org.jetbrains.annotations.Nullable()
    java.lang.String title, @org.jetbrains.annotations.Nullable()
    java.lang.Integer desiredHeight) {
        return null;
    }
    
    private final java.lang.String cacheKeyFor(java.lang.String url, java.lang.Integer desiredHeight) {
        return null;
    }
    
    private final android.net.Uri toUriCompat(java.lang.String $this$toUriCompat) {
        return null;
    }
}
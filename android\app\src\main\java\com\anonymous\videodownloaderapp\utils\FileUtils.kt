package com.anonymous.videodownloaderapp.utils

import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.OutputStream
import java.util.Locale

object FileUtils {

    data class MediaDest(
        val uri: Uri,
        val displayName: String,
        val mimeType: String,
    )

    fun guessFileName(url: String, mimeType: String?): String {
        val clean = url.substringAfterLast('/').substringBefore('?').substringBefore('#')
        val base = clean.ifBlank { "video_${System.currentTimeMillis()}" }
        val safe = base.replace(Regex("[^A-Za-z0-9._-]"), "_")
        val extFromMime = when (mimeType?.lowercase(Locale.US)) {
            "application/vnd.apple.mpegurl", "application/x-mpegurl", "audio/mpegurl", "vnd.apple.mpegurl" -> "m3u8"
            "video/mp2t", "video/MP2T" -> "ts"
            "video/mp4" -> "mp4"
            "video/webm" -> "webm"
            "video/3gpp" -> "3gp"
            "video/ogg" -> "ogv"
            else -> null
        }
        val hasExt = safe.contains('.')
        val finalName = if (!hasExt && extFromMime != null) "$safe.$extFromMime" else safe
        return finalName.take(240) // keep within typical filesystem limits
    }

    fun resolveMimeType(url: String, provided: String?): String {
        if (!provided.isNullOrBlank()) return provided
        val lower = url.lowercase(Locale.US)
        return when {
            lower.contains(".m3u8") -> "application/vnd.apple.mpegurl"
            lower.contains(".mp4") -> "video/mp4"
            lower.contains(".webm") -> "video/webm"
            lower.contains(".ts") -> "video/mp2t"
            else -> "application/octet-stream"
        }
    }

    suspend fun createMediaInDownloads(
        context: Context,
        displayName: String,
        mimeType: String
    ): MediaDest? = withContext(Dispatchers.IO) {
        val resolver: ContentResolver = context.contentResolver
        val collection: Uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            MediaStore.Downloads.EXTERNAL_CONTENT_URI
        } else {
            @Suppress("DEPRECATION")
            MediaStore.Files.getContentUri("external")
        }

        val values = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, displayName)
            put(MediaStore.MediaColumns.MIME_TYPE, mimeType)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS)
                put(MediaStore.MediaColumns.IS_PENDING, 1)
            } else {
                @Suppress("DEPRECATION")
                put(MediaStore.MediaColumns.DATA, File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), displayName).absolutePath)
            }
        }

        val itemUri = resolver.insert(collection, values) ?: return@withContext null
        MediaDest(itemUri, displayName, mimeType)
    }

    suspend fun openOutputStream(
        context: Context,
        uri: Uri
    ): OutputStream? = withContext(Dispatchers.IO) {
        context.contentResolver.openOutputStream(uri)
    }

    suspend fun finalizePending(context: Context, uri: Uri) = withContext(Dispatchers.IO) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val values = ContentValues().apply { put(MediaStore.MediaColumns.IS_PENDING, 0) }
            context.contentResolver.update(uri, values, null, null)
        }
    }
}
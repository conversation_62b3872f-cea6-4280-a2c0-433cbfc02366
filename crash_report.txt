## Crash Report (4)

**Date:** 2025-08-10

**Application:** com.anonymous.videodownloaderapp

### Problem:
Videos and video players on some websites are not being displayed or are not working correctly after the previous fix was applied.

### Root Cause:
The custom `WebViewClient` implemented to fix the previous crash is intercepting all network requests from the `WebView`. This is too aggressive and is interfering with the normal operation of the video players, which rely on a complex chain of network requests to load their controls, advertisements, and other resources. By trying to handle all these requests with our custom `OkHttpClient`, we are breaking the video players.

### Solution:
Instead of intercepting all network requests, we should only intercept the requests for the video manifests (HLS and DASH). This will allow us to get the video quality information without interfering with the video player's operation. All other requests will be handled by the default `WebView` network stack.

### Proposed Implementation:

1.  **Refine the `CustomWebViewClient` in `BrowserWebView.kt`:**

    ```kotlin
    private class CustomWebViewClient(
        private val okHttpClient: okhttp3.OkHttpClient,
        private val hybridVideoDetectionService: HybridVideoDetectionService
    ) : WebViewClient() {
        override fun shouldInterceptRequest(view: WebView?, request: WebResourceRequest?): WebResourceResponse? {
            if (request == null || request.url == null) {
                return super.shouldInterceptRequest(view, request)
            }

            val url = request.url.toString()
            if (url.endsWith(".m3u8") || url.endsWith(".mpd")) {
                try {
                    val okHttpRequest = okhttp3.Request.Builder()
                        .url(url)
                        .headers(okhttp3.Headers.of(request.requestHeaders))
                        .build()

                    val response = okHttpClient.newCall(okHttpRequest).execute()

                    val contentType = response.header("content-type")
                    val encoding = response.header("content-encoding")
                    val inputStream = response.body?.byteStream()

                    val responseHeaders = mutableMapOf<String, String>()
                    for (i in 0 until response.headers.size) {
                        responseHeaders[response.headers.name(i)] = response.headers.value(i)
                    }

                    // Let the HybridVideoDetectionService know about the response
                    hybridVideoDetectionService.onManifestResponse(request, response.body?.string())

                    return WebResourceResponse(
                        contentType,
                        encoding,
                        response.code,
                        response.message,
                        responseHeaders,
                        inputStream
                    )
                } catch (e: java.io.IOException) {
                    return null
                }
            }
            return super.shouldInterceptRequest(view, request)
        }
    }
    ```

2.  **Update `HybridVideoDetectionService.kt`:**

    A new method `onManifestResponse` needs to be added to the `HybridVideoDetectionService` to handle the intercepted manifest files. This method will be responsible for parsing the manifests and extracting the video quality information.

This approach is much safer and should not interfere with the normal operation of the websites, while still allowing us to detect the videos.
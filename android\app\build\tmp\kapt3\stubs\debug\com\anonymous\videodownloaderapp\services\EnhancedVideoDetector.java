package com.anonymous.videodownloaderapp.services;

/**
 * Detector de vídeo aprimorado com filtros anti-spam ultra-rigorosos
 * que elimina definitivamente a detecção excessiva (26→3-5 vídeos válidos).
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0086\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010$\n\u0002\b\u0019\u0018\u0000 H2\u00020\u0001:\u0007HIJKLMNB\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\nH\u0002J\u001c\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\n0\t2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u0002J\u0010\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001fH\u0002J\u000e\u0010 \u001a\u00020\u00182\u0006\u0010!\u001a\u00020\u000fJ\u001c\u0010\"\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u0006\u0010!\u001a\u00020\u000fH\u0082@\u00a2\u0006\u0002\u0010#J\u0012\u0010$\u001a\u0004\u0018\u00010\n2\u0006\u0010%\u001a\u00020&H\u0002J\u001c\u0010\'\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u0006\u0010(\u001a\u00020)H\u0082@\u00a2\u0006\u0002\u0010*J\u001c\u0010+\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u0006\u0010(\u001a\u00020)H\u0082@\u00a2\u0006\u0002\u0010*J0\u0010,\u001a\b\u0012\u0004\u0012\u00020\n0\t2\b\u0010(\u001a\u0004\u0018\u00010)2\u0006\u0010!\u001a\u00020\u000f2\b\b\u0002\u0010-\u001a\u00020.H\u0086@\u00a2\u0006\u0002\u0010/J\u0019\u00100\u001a\u0004\u0018\u0001012\b\u00102\u001a\u0004\u0018\u00010\u000fH\u0002\u00a2\u0006\u0002\u00103J\u0010\u00104\u001a\u00020\u000f2\u0006\u0010\u0019\u001a\u00020\nH\u0002J\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u000106J\u0012\u00107\u001a\u0004\u0018\u00010\u000f2\u0006\u00108\u001a\u00020\u000fH\u0002J\u0012\u00109\u001a\u0004\u0018\u00010\u000f2\u0006\u00108\u001a\u00020\u000fH\u0002J\u0010\u0010:\u001a\u00020.2\u0006\u00108\u001a\u00020\u000fH\u0002J\u0010\u0010;\u001a\u00020\u000f2\u0006\u00108\u001a\u00020\u000fH\u0002J\u0006\u0010<\u001a\u00020\u0018J\u0006\u0010=\u001a\u00020\u0018J\u001c\u0010>\u001a\b\u0012\u0004\u0012\u00020\n0\t2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u0002J\u0018\u0010?\u001a\u0004\u0018\u00010\n2\u0006\u0010%\u001a\u00020&H\u0082@\u00a2\u0006\u0002\u0010@J\u0018\u0010A\u001a\u0004\u0018\u00010\n2\u0006\u0010%\u001a\u00020&H\u0082@\u00a2\u0006\u0002\u0010@J\u001e\u0010B\u001a\u00020\u00012\u0006\u0010%\u001a\u00020&2\u0006\u0010C\u001a\u00020\u000fH\u0082@\u00a2\u0006\u0002\u0010DJ\u0016\u0010E\u001a\u00020\u00182\u0006\u0010%\u001a\u00020&H\u0082@\u00a2\u0006\u0002\u0010@J\b\u0010F\u001a\u00020\u0018H\u0002J\b\u0010G\u001a\u00020\u0018H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00100\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006O"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "cache", "Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache;", "detectionResults", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$VideoCandidate;", "networkInterceptor", "Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService;", "pageStates", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$PageDetectionState;", "results", "Lkotlinx/coroutines/flow/StateFlow;", "getResults", "()Lkotlinx/coroutines/flow/StateFlow;", "scope", "Lkotlinx/coroutines/CoroutineScope;", "addCandidateToResults", "", "candidate", "applyRigorousFilters", "candidates", "calculateVideoElementScore", "", "videoData", "Lorg/json/JSONObject;", "clearPage", "pageUrl", "correlateWithNetworkRequests", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createProgressiveCandidate", "request", "Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaRequest;", "detectHtml5VideoElements", "webView", "Landroid/webkit/WebView;", "(Landroid/webkit/WebView;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "detectViaPerformanceAPI", "detectVideos", "isBackgrounded", "", "(Landroid/webkit/WebView;Ljava/lang/String;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "extractHeightFromLabel", "", "label", "(Ljava/lang/String;)Ljava/lang/Integer;", "generateContentKey", "getDetectionStats", "", "inferFormatFromUrl", "url", "inferQualityFromUrl", "isAdOrTrackerUrl", "normalizeManifestUrl", "onBackgrounded", "onForegrounded", "performAdvancedGrouping", "processDashManifest", "(Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "processHlsManifest", "processManifestContent", "content", "(Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "processNetworkRequest", "setupNetworkListener", "updateGlobalResults", "Companion", "DetectionSource", "ElementInfo", "NetworkInfo", "PageDetectionState", "PlaybackState", "VideoCandidate", "app_debug"})
public final class EnhancedVideoDetector {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.anonymous.videodownloaderapp.services.EnhancedVideoDetector INSTANCE;
    private static final int MIN_SIDE_PX = 200;
    private static final int MIN_AREA_PX = 40000;
    private static final double MIN_ASPECT_RATIO = 0.625;
    private static final double MAX_ASPECT_RATIO = 2.4;
    private static final int MIN_DURATION_SECONDS = 15;
    private static final int MIN_ENGAGEMENT_SECONDS = 3;
    private static final int ENGAGEMENT_TIMEOUT_SECONDS = 10;
    private static final float VISIBILITY_THRESHOLD = 0.25F;
    private static final long SUSTAINED_VISIBILITY_MS = 800L;
    private static final long SCROLL_DEBOUNCE_MS = 300L;
    private static final long HEAVY_PAGE_DEBOUNCE_MS = 500L;
    private static final int MAX_CANDIDATES_PER_PAGE = 50;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope scope = null;
    @org.jetbrains.annotations.NotNull()
    private final com.anonymous.videodownloaderapp.services.NetworkVideoCache cache = null;
    @org.jetbrains.annotations.NotNull()
    private final com.anonymous.videodownloaderapp.services.NetworkInterceptorService networkInterceptor = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.PageDetectionState> pageStates = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate>> detectionResults = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate>> results = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.Companion Companion = null;
    
    private EnhancedVideoDetector(android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate>> getResults() {
        return null;
    }
    
    private final void setupNetworkListener() {
    }
    
    /**
     * Executar detecção completa em uma página
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object detectVideos(@org.jetbrains.annotations.Nullable()
    android.webkit.WebView webView, @org.jetbrains.annotations.NotNull()
    java.lang.String pageUrl, boolean isBackgrounded, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate>> $completion) {
        return null;
    }
    
    private final java.lang.Object detectHtml5VideoElements(android.webkit.WebView webView, kotlin.coroutines.Continuation<? super java.util.List<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate>> $completion) {
        return null;
    }
    
    private final float calculateVideoElementScore(org.json.JSONObject videoData) {
        return 0.0F;
    }
    
    private final java.lang.Object correlateWithNetworkRequests(java.lang.String pageUrl, kotlin.coroutines.Continuation<? super java.util.List<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate>> $completion) {
        return null;
    }
    
    private final java.lang.Object processNetworkRequest(com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequest request, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object processManifestContent(com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequest request, java.lang.String content, kotlin.coroutines.Continuation<java.lang.Object> $completion) {
        return null;
    }
    
    private final java.lang.Object processHlsManifest(com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequest request, kotlin.coroutines.Continuation<? super com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate> $completion) {
        return null;
    }
    
    private final java.lang.Object processDashManifest(com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequest request, kotlin.coroutines.Continuation<? super com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate> $completion) {
        return null;
    }
    
    private final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate createProgressiveCandidate(com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequest request) {
        return null;
    }
    
    private final java.lang.Object detectViaPerformanceAPI(android.webkit.WebView webView, kotlin.coroutines.Continuation<? super java.util.List<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate>> $completion) {
        return null;
    }
    
    private final java.util.List<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate> applyRigorousFilters(java.util.List<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate> candidates) {
        return null;
    }
    
    private final java.util.List<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate> performAdvancedGrouping(java.util.List<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate> candidates) {
        return null;
    }
    
    private final java.lang.String generateContentKey(com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate candidate) {
        return null;
    }
    
    private final void addCandidateToResults(com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate candidate) {
    }
    
    private final void updateGlobalResults() {
    }
    
    private final java.lang.String inferQualityFromUrl(java.lang.String url) {
        return null;
    }
    
    private final java.lang.String inferFormatFromUrl(java.lang.String url) {
        return null;
    }
    
    private final java.lang.Integer extractHeightFromLabel(java.lang.String label) {
        return null;
    }
    
    private final boolean isAdOrTrackerUrl(java.lang.String url) {
        return false;
    }
    
    private final java.lang.String normalizeManifestUrl(java.lang.String url) {
        return null;
    }
    
    /**
     * Limpar estado de uma página
     */
    public final void clearPage(@org.jetbrains.annotations.NotNull()
    java.lang.String pageUrl) {
    }
    
    /**
     * Notificar que app foi para background
     */
    public final void onBackgrounded() {
    }
    
    /**
     * Notificar que app voltou para foreground
     */
    public final void onForegrounded() {
    }
    
    /**
     * Obter estatísticas de detecção
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getDetectionStats() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\t\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0015\u001a\u00020\b2\u0006\u0010\u0016\u001a\u00020\u0017R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\nX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$Companion;", "", "()V", "ENGAGEMENT_TIMEOUT_SECONDS", "", "HEAVY_PAGE_DEBOUNCE_MS", "", "INSTANCE", "Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector;", "MAX_ASPECT_RATIO", "", "MAX_CANDIDATES_PER_PAGE", "MIN_AREA_PX", "MIN_ASPECT_RATIO", "MIN_DURATION_SECONDS", "MIN_ENGAGEMENT_SECONDS", "MIN_SIDE_PX", "SCROLL_DEBOUNCE_MS", "SUSTAINED_VISIBILITY_MS", "VISIBILITY_THRESHOLD", "", "getInstance", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$DetectionSource;", "", "(Ljava/lang/String;I)V", "HTML5_VIDEO_ELEMENT", "NETWORK_INTERCEPTOR", "PERFORMANCE_API_FALLBACK", "YOUTUBE_DATA", "DIRECT_LINK_SCAN", "app_debug"})
    public static enum DetectionSource {
        /*public static final*/ HTML5_VIDEO_ELEMENT /* = new HTML5_VIDEO_ELEMENT() */,
        /*public static final*/ NETWORK_INTERCEPTOR /* = new NETWORK_INTERCEPTOR() */,
        /*public static final*/ PERFORMANCE_API_FALLBACK /* = new PERFORMANCE_API_FALLBACK() */,
        /*public static final*/ YOUTUBE_DATA /* = new YOUTUBE_DATA() */,
        /*public static final*/ DIRECT_LINK_SCAN /* = new DIRECT_LINK_SCAN() */;
        
        DetectionSource() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.DetectionSource> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001d\b\u0086\b\u0018\u00002\u00020\u0001BG\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\u0006\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\"\u001a\u00020\bH\u00c6\u0003J\t\u0010#\u001a\u00020\nH\u00c6\u0003J\t\u0010$\u001a\u00020\u0006H\u00c6\u0003J\t\u0010%\u001a\u00020\rH\u00c6\u0003J\t\u0010&\u001a\u00020\u000fH\u00c6\u0003J[\u0010\'\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00062\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u00c6\u0001J\u0013\u0010(\u001a\u00020\r2\b\u0010)\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010*\u001a\u00020\nH\u00d6\u0001J\t\u0010+\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u000b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0012R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0016R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001e\u00a8\u0006,"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$ElementInfo;", "", "tagName", "", "currentSrc", "visibility", "", "rect", "Landroid/graphics/Rect;", "zIndex", "", "opacity", "hasUserInteraction", "", "playbackState", "Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$PlaybackState;", "(Ljava/lang/String;Ljava/lang/String;FLandroid/graphics/Rect;IFZLcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$PlaybackState;)V", "getCurrentSrc", "()Ljava/lang/String;", "getHasUserInteraction", "()Z", "getOpacity", "()F", "getPlaybackState", "()Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$PlaybackState;", "getRect", "()Landroid/graphics/Rect;", "getTagName", "getVisibility", "getZIndex", "()I", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class ElementInfo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String tagName = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String currentSrc = null;
        private final float visibility = 0.0F;
        @org.jetbrains.annotations.NotNull()
        private final android.graphics.Rect rect = null;
        private final int zIndex = 0;
        private final float opacity = 0.0F;
        private final boolean hasUserInteraction = false;
        @org.jetbrains.annotations.NotNull()
        private final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.PlaybackState playbackState = null;
        
        public ElementInfo(@org.jetbrains.annotations.NotNull()
        java.lang.String tagName, @org.jetbrains.annotations.Nullable()
        java.lang.String currentSrc, float visibility, @org.jetbrains.annotations.NotNull()
        android.graphics.Rect rect, int zIndex, float opacity, boolean hasUserInteraction, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.PlaybackState playbackState) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getTagName() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getCurrentSrc() {
            return null;
        }
        
        public final float getVisibility() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.graphics.Rect getRect() {
            return null;
        }
        
        public final int getZIndex() {
            return 0;
        }
        
        public final float getOpacity() {
            return 0.0F;
        }
        
        public final boolean getHasUserInteraction() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.PlaybackState getPlaybackState() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component2() {
            return null;
        }
        
        public final float component3() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.graphics.Rect component4() {
            return null;
        }
        
        public final int component5() {
            return 0;
        }
        
        public final float component6() {
            return 0.0F;
        }
        
        public final boolean component7() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.PlaybackState component8() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.ElementInfo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String tagName, @org.jetbrains.annotations.Nullable()
        java.lang.String currentSrc, float visibility, @org.jetbrains.annotations.NotNull()
        android.graphics.Rect rect, int zIndex, float opacity, boolean hasUserInteraction, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.PlaybackState playbackState) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\bJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\nJ\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J<\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0015J\u0013\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\n\n\u0002\u0010\u000b\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\r\u00a8\u0006\u001c"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$NetworkInfo;", "", "requestId", "", "contentType", "contentLength", "", "referer", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;)V", "getContentLength", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getContentType", "()Ljava/lang/String;", "getReferer", "getRequestId", "component1", "component2", "component3", "component4", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;)Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$NetworkInfo;", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    public static final class NetworkInfo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String requestId = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String contentType = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.Long contentLength = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String referer = null;
        
        public NetworkInfo(@org.jetbrains.annotations.NotNull()
        java.lang.String requestId, @org.jetbrains.annotations.Nullable()
        java.lang.String contentType, @org.jetbrains.annotations.Nullable()
        java.lang.Long contentLength, @org.jetbrains.annotations.Nullable()
        java.lang.String referer) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getRequestId() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getContentType() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Long getContentLength() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getReferer() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Long component3() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.NetworkInfo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String requestId, @org.jetbrains.annotations.Nullable()
        java.lang.String contentType, @org.jetbrains.annotations.Nullable()
        java.lang.Long contentLength, @org.jetbrains.annotations.Nullable()
        java.lang.String referer) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0015\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BA\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0014\b\u0002\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00060\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\u0015\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001a\u001a\u00020\nH\u00c6\u0003J\t\u0010\u001b\u001a\u00020\nH\u00c6\u0003JG\u0010\u001c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0014\b\u0002\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\n2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001J\t\u0010!\u001a\u00020\u0003H\u00d6\u0001R\u001d\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u000b\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u001a\u0010\t\u001a\u00020\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\t\u0010\u0010\"\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006\""}, d2 = {"Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$PageDetectionState;", "", "url", "", "candidates", "", "Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$VideoCandidate;", "lastScrollTime", "Ljava/util/concurrent/atomic/AtomicLong;", "isBackgrounded", "", "heavyPageMode", "(Ljava/lang/String;Ljava/util/Map;Ljava/util/concurrent/atomic/AtomicLong;ZZ)V", "getCandidates", "()Ljava/util/Map;", "getHeavyPageMode", "()Z", "setBackgrounded", "(Z)V", "getLastScrollTime", "()Ljava/util/concurrent/atomic/AtomicLong;", "getUrl", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class PageDetectionState {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String url = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.Map<java.lang.String, com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate> candidates = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.concurrent.atomic.AtomicLong lastScrollTime = null;
        private boolean isBackgrounded;
        private final boolean heavyPageMode = false;
        
        public PageDetectionState(@org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.NotNull()
        java.util.Map<java.lang.String, com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate> candidates, @org.jetbrains.annotations.NotNull()
        java.util.concurrent.atomic.AtomicLong lastScrollTime, boolean isBackgrounded, boolean heavyPageMode) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Map<java.lang.String, com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate> getCandidates() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.concurrent.atomic.AtomicLong getLastScrollTime() {
            return null;
        }
        
        public final boolean isBackgrounded() {
            return false;
        }
        
        public final void setBackgrounded(boolean p0) {
        }
        
        public final boolean getHeavyPageMode() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Map<java.lang.String, com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate> component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.concurrent.atomic.AtomicLong component3() {
            return null;
        }
        
        public final boolean component4() {
            return false;
        }
        
        public final boolean component5() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.PageDetectionState copy(@org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.NotNull()
        java.util.Map<java.lang.String, com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate> candidates, @org.jetbrains.annotations.NotNull()
        java.util.concurrent.atomic.AtomicLong lastScrollTime, boolean isBackgrounded, boolean heavyPageMode) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$PlaybackState;", "", "(Ljava/lang/String;I)V", "PLAYING", "PAUSED", "ENDED", "NOT_STARTED", "LOADING", "app_debug"})
    public static enum PlaybackState {
        /*public static final*/ PLAYING /* = new PLAYING() */,
        /*public static final*/ PAUSED /* = new PAUSED() */,
        /*public static final*/ ENDED /* = new ENDED() */,
        /*public static final*/ NOT_STARTED /* = new NOT_STARTED() */,
        /*public static final*/ LOADING /* = new LOADING() */;
        
        PlaybackState() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.PlaybackState> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b.\b\u0086\b\u0018\u00002\u00020\u0001B\u0081\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\b\u0010\t\u001a\u0004\u0018\u00010\b\u0012\b\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u000f\u001a\u00020\u0010\u0012\u0006\u0010\u0011\u001a\u00020\u0012\u0012\b\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u0012\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0018\u00a2\u0006\u0002\u0010\u0019J\t\u00102\u001a\u00020\u0003H\u00c6\u0003J\t\u00103\u001a\u00020\u0010H\u00c6\u0003J\t\u00104\u001a\u00020\u0012H\u00c6\u0003J\u000b\u00105\u001a\u0004\u0018\u00010\u0014H\u00c6\u0003J\u000b\u00106\u001a\u0004\u0018\u00010\u0016H\u00c6\u0003J\t\u00107\u001a\u00020\u0018H\u00c6\u0003J\t\u00108\u001a\u00020\u0003H\u00c6\u0003J\t\u00109\u001a\u00020\u0003H\u00c6\u0003J\t\u0010:\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010;\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010$J\u0010\u0010<\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010$J\u0010\u0010=\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001bJ\t\u0010>\u001a\u00020\rH\u00c6\u0003J\t\u0010?\u001a\u00020\rH\u00c6\u0003J\u00a4\u0001\u0010@\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\r2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u00122\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00162\b\b\u0002\u0010\u0017\u001a\u00020\u0018H\u00c6\u0001\u00a2\u0006\u0002\u0010AJ\u0013\u0010B\u001a\u00020\r2\b\u0010C\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010D\u001a\u00020\bH\u00d6\u0001J\t\u0010E\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\n\n\u0002\u0010\u001c\u001a\u0004\b\u001a\u0010\u001bR\u0013\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0011\u0010\u000e\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0015\u0010\t\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010%\u001a\u0004\b#\u0010$R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010 R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\"R\u0013\u0010\u0015\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010 R\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u0011\u0010\u0017\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010 R\u0015\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010%\u001a\u0004\b1\u0010$\u00a8\u0006F"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$VideoCandidate;", "", "id", "", "url", "quality", "format", "width", "", "height", "duration", "", "isLive", "", "hasDrm", "score", "", "source", "Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$DetectionSource;", "element", "Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$ElementInfo;", "networkInfo", "Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$NetworkInfo;", "timestamp", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Double;ZZFLcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$DetectionSource;Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$ElementInfo;Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$NetworkInfo;J)V", "getDuration", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getElement", "()Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$ElementInfo;", "getFormat", "()Ljava/lang/String;", "getHasDrm", "()Z", "getHeight", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getId", "getNetworkInfo", "()Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$NetworkInfo;", "getQuality", "getScore", "()F", "getSource", "()Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$DetectionSource;", "getTimestamp", "()J", "getUrl", "getWidth", "component1", "component10", "component11", "component12", "component13", "component14", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Double;ZZFLcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$DetectionSource;Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$ElementInfo;Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$NetworkInfo;J)Lcom/anonymous/videodownloaderapp/services/EnhancedVideoDetector$VideoCandidate;", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class VideoCandidate {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String id = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String url = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String quality = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String format = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.Integer width = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.Integer height = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.Double duration = null;
        private final boolean isLive = false;
        private final boolean hasDrm = false;
        private final float score = 0.0F;
        @org.jetbrains.annotations.NotNull()
        private final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.DetectionSource source = null;
        @org.jetbrains.annotations.Nullable()
        private final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.ElementInfo element = null;
        @org.jetbrains.annotations.Nullable()
        private final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.NetworkInfo networkInfo = null;
        private final long timestamp = 0L;
        
        public VideoCandidate(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.NotNull()
        java.lang.String quality, @org.jetbrains.annotations.NotNull()
        java.lang.String format, @org.jetbrains.annotations.Nullable()
        java.lang.Integer width, @org.jetbrains.annotations.Nullable()
        java.lang.Integer height, @org.jetbrains.annotations.Nullable()
        java.lang.Double duration, boolean isLive, boolean hasDrm, float score, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.DetectionSource source, @org.jetbrains.annotations.Nullable()
        com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.ElementInfo element, @org.jetbrains.annotations.Nullable()
        com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.NetworkInfo networkInfo, long timestamp) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getQuality() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getFormat() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Integer getWidth() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Integer getHeight() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Double getDuration() {
            return null;
        }
        
        public final boolean isLive() {
            return false;
        }
        
        public final boolean getHasDrm() {
            return false;
        }
        
        public final float getScore() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.DetectionSource getSource() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.ElementInfo getElement() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.NetworkInfo getNetworkInfo() {
            return null;
        }
        
        public final long getTimestamp() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final float component10() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.DetectionSource component11() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.ElementInfo component12() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.NetworkInfo component13() {
            return null;
        }
        
        public final long component14() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Integer component5() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Integer component6() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Double component7() {
            return null;
        }
        
        public final boolean component8() {
            return false;
        }
        
        public final boolean component9() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.VideoCandidate copy(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.NotNull()
        java.lang.String quality, @org.jetbrains.annotations.NotNull()
        java.lang.String format, @org.jetbrains.annotations.Nullable()
        java.lang.Integer width, @org.jetbrains.annotations.Nullable()
        java.lang.Integer height, @org.jetbrains.annotations.Nullable()
        java.lang.Double duration, boolean isLive, boolean hasDrm, float score, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.DetectionSource source, @org.jetbrains.annotations.Nullable()
        com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.ElementInfo element, @org.jetbrains.annotations.Nullable()
        com.anonymous.videodownloaderapp.services.EnhancedVideoDetector.NetworkInfo networkInfo, long timestamp) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}
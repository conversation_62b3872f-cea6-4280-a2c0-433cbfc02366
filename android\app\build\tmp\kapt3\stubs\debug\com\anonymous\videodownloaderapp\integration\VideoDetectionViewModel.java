package com.anonymous.videodownloaderapp.integration;

/**
 * ViewModel para integração com a UI
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0013\u001a\u00020\u0014J*\u0010\u0015\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00172\b\b\u0002\u0010\u0019\u001a\u00020\u001aH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u001e0\u001dJ\b\u0010\u001f\u001a\u00020\u0014H\u0014J\u001e\u0010 \u001a\u00020\u001a2\u0006\u0010!\u001a\u00020\"2\u0006\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010#R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u001b\u0010\n\u001a\u00020\u000b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000e\u0010\u000f\u001a\u0004\b\f\u0010\rR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\t\u00a8\u0006$"}, d2 = {"Lcom/anonymous/videodownloaderapp/integration/VideoDetectionViewModel;", "Landroidx/lifecycle/ViewModel;", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "detectionResults", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$VideoDetectionResult;", "getDetectionResults", "()Lkotlinx/coroutines/flow/StateFlow;", "integration", "Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration;", "getIntegration", "()Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration;", "integration$delegate", "Lkotlin/Lazy;", "integrationState", "Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$IntegrationState;", "getIntegrationState", "clearHistory", "", "detectVideos", "webViewId", "", "pageUrl", "enableYouTubeOptimization", "", "(Ljava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getStatistics", "", "", "onCleared", "setupWebView", "webView", "Landroid/webkit/WebView;", "(Landroid/webkit/WebView;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class VideoDetectionViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy integration$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.IntegrationState> integrationState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult> detectionResults = null;
    
    public VideoDetectionViewModel(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    private final com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration getIntegration() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.IntegrationState> getIntegrationState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult> getDetectionResults() {
        return null;
    }
    
    /**
     * Configurar WebView
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setupWebView(@org.jetbrains.annotations.NotNull()
    android.webkit.WebView webView, @org.jetbrains.annotations.NotNull()
    java.lang.String webViewId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Detectar vídeos
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object detectVideos(@org.jetbrains.annotations.NotNull()
    java.lang.String webViewId, @org.jetbrains.annotations.NotNull()
    java.lang.String pageUrl, boolean enableYouTubeOptimization, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult> $completion) {
        return null;
    }
    
    /**
     * Obter estatísticas
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getStatistics() {
        return null;
    }
    
    /**
     * Limpar histórico
     */
    public final void clearHistory() {
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
}
package com.anonymous.videodownloaderapp.integration

import android.content.Context
import android.webkit.WebView
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.anonymous.videodownloaderapp.services.*
import com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.util.concurrent.ConcurrentHashMap

/**
 * Integração do Sistema de Detecção Ultra-Preciso
 * Conecta o sistema avançado com a UI e componentes existentes
 */
class VideoDetectionIntegration private constructor(private val context: Context) {

    data class IntegrationState(
        val isSystemReady: Boolean = false,
        val isDetecting: Boolean = false,
        val lastDetectionTime: Long = 0L,
        val totalVideosDetected: Int = 0,
        val currentPageUrl: String = "",
        val detectionErrors: List<String> = emptyList(),
        val performanceMetrics: PerformanceSnapshot? = null
    )

    data class PerformanceSnapshot(
        val averageDetectionTime: Double,
        val successRate: Double,
        val memoryUsage: Long,
        val cpuUsage: Double,
        val cacheHitRate: Double,
        val timestamp: Long = System.currentTimeMillis()
    )

    data class VideoDetectionResult(
        val videos: List<DetectedVideo>,
        val source: String,
        val processingTime: Long,
        val qualityEnhanced: Boolean,
        val specialCasesHandled: List<String>
    )

    data class DetectedVideo(
        val url: String,
        val title: String,
        val quality: String,
        val format: String,
        val duration: String?,
        val thumbnail: String?,
        val fileSize: String?,
        val isLive: Boolean,
        val score: Float,
        val metadata: Map<String, Any> = emptyMap()
    )

    companion object {
        @Volatile
        private var instance: VideoDetectionIntegration? = null
        
        fun getInstance(context: Context): VideoDetectionIntegration {
            return instance ?: synchronized(this) {
                instance ?: VideoDetectionIntegration(context.applicationContext).also {
                    instance = it
                }
            }
        }
    }

    // Sistema ultra-preciso
    private val ultraPreciseSystem by lazy { 
        UltraPreciseVideoDetectionSystem.getInstance(context) 
    }
    
    // Store de dados existente
    private val videoDetectionStore by lazy { 
        VideoDetectionStore 
    }
    
    // Estado da integração
    private val _integrationState = MutableStateFlow(IntegrationState())
    val integrationState: StateFlow<IntegrationState> = _integrationState
    
    private val _detectionResults = MutableStateFlow<VideoDetectionResult?>(null)
    val detectionResults: StateFlow<VideoDetectionResult?> = _detectionResults
    
    // Controle de operações
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private val activeWebViews = ConcurrentHashMap<String, WebView>()
    private val detectionHistory = mutableListOf<VideoDetectionResult>()
    
    // Callbacks para UI
    private val uiCallbacks = mutableSetOf<(VideoDetectionResult) -> Unit>()
    private val errorCallbacks = mutableSetOf<(String) -> Unit>()
    private val stateCallbacks = mutableSetOf<(IntegrationState) -> Unit>()

    init {
        setupIntegration()
    }

    /**
     * Configurar integração
     */
    private fun setupIntegration() {
        scope.launch {
            try {
                // Aguardar sistema estar pronto
                ultraPreciseSystem.systemState
                    .filter { it == UltraPreciseVideoDetectionSystem.SystemState.READY }
                    .first()
                
                // Configurar callbacks do sistema
                setupSystemCallbacks()
                
                // Atualizar estado
                updateIntegrationState { copy(isSystemReady = true) }
                
            } catch (e: Exception) {
                notifyError("Erro na configuração da integração: ${e.message}")
            }
        }
    }

    /**
     * Configurar callbacks do sistema
     */
    private fun setupSystemCallbacks() {
        // Callback de detecção
        ultraPreciseSystem.addDetectionCallback { result ->
            scope.launch {
                processDetectionResult(result)
            }
        }
        
        // Callback de erro
        ultraPreciseSystem.addErrorCallback { message, throwable ->
            val errorMsg = "${message}: ${throwable?.message ?: "Erro desconhecido"}"
            notifyError(errorMsg)
            updateIntegrationState { 
                copy(detectionErrors = detectionErrors + errorMsg)
            }
        }
        
        // Callback de mudança de estado
        ultraPreciseSystem.addStateChangeCallback { systemState ->
            scope.launch {
                updateSystemStateInIntegration(systemState)
            }
        }
    }

    /**
     * Configurar WebView para detecção
     */
    suspend fun setupWebViewForDetection(
        webView: WebView,
        webViewId: String,
        optimizationProfile: WebViewConfigurationManager.ConfigurationProfile = 
            WebViewConfigurationManager.ConfigurationProfile.VIDEO_DETECTION_FOCUSED
    ): Boolean {
        
        return try {
            // Configurar WebView com sistema ultra-preciso
            val configured = ultraPreciseSystem.configureWebView(webView, optimizationProfile)
            
            if (configured) {
                // Registrar WebView
                activeWebViews[webViewId] = webView
                
                // Configurar interceptação de navegação
                setupWebViewNavigation(webView, webViewId)
                
                true
            } else {
                notifyError("Falha ao configurar WebView: $webViewId")
                false
            }
            
        } catch (e: Exception) {
            notifyError("Erro ao configurar WebView: ${e.message}")
            false
        }
    }

    /**
     * Configurar navegação da WebView
     */
    private fun setupWebViewNavigation(webView: WebView, webViewId: String) {
        // Implementar interceptação de mudanças de página
        // para detectar automaticamente vídeos em novas páginas
    }

    /**
     * Detectar vídeos na página atual
     */
    suspend fun detectVideosInCurrentPage(
        webViewId: String,
        pageUrl: String,
        enableYouTubeOptimization: Boolean = true,
        enableSpecialCases: Boolean = true,
        priority: OptimizedJavaScriptBridge.Priority = OptimizedJavaScriptBridge.Priority.NORMAL
    ): VideoDetectionResult? {
        
        val webView = activeWebViews[webViewId]
        if (webView == null) {
            notifyError("WebView não encontrada: $webViewId")
            return null
        }
        
        return try {
            // Atualizar estado
            updateIntegrationState { 
                copy(
                    isDetecting = true,
                    currentPageUrl = pageUrl
                )
            }
            
            // Executar detecção ultra-precisa
            val result = ultraPreciseSystem.detectVideosUltraPrecise(
                webView = webView,
                pageUrl = pageUrl,
                enableYouTubeOptimization = enableYouTubeOptimization,
                enableSpecialCasesDetection = enableSpecialCases,
                priority = priority
            )
            
            if (result != null) {
                // Processar resultado
                val integrationResult = convertToIntegrationResult(result, pageUrl)
                
                // Atualizar store de dados
                updateVideoDetectionStore(integrationResult, webViewId)
                
                // Atualizar estado
                updateIntegrationState { 
                    copy(
                        isDetecting = false,
                        lastDetectionTime = System.currentTimeMillis(),
                        totalVideosDetected = totalVideosDetected + integrationResult.videos.size
                    )
                }
                
                integrationResult
            } else {
                updateIntegrationState { copy(isDetecting = false) }
                null
            }
            
        } catch (e: Exception) {
            updateIntegrationState { copy(isDetecting = false) }
            notifyError("Erro na detecção: ${e.message}")
            null
        }
    }

    /**
     * Processar resultado de detecção
     */
    private suspend fun processDetectionResult(
        result: UltraPreciseVideoDetectionSystem.DetectionResult
    ) {
        val integrationResult = convertToIntegrationResult(result, "")
        
        // Atualizar resultado atual
        _detectionResults.value = integrationResult
        
        // Adicionar ao histórico
        detectionHistory.add(integrationResult)
        
        // Limitar histórico
        if (detectionHistory.size > 50) {
            detectionHistory.removeAt(0)
        }
        
        // Notificar UI
        notifyUICallbacks(integrationResult)
        
        // Atualizar métricas de performance
        updatePerformanceMetrics(result.performanceMetrics)
    }

    /**
     * Converter resultado do sistema para formato de integração
     */
    private fun convertToIntegrationResult(
        systemResult: UltraPreciseVideoDetectionSystem.DetectionResult,
        pageUrl: String
    ): VideoDetectionResult {
        
        val videos = systemResult.candidates.map { candidate ->
            DetectedVideo(
                url = candidate.url,
                title = candidate.title.ifEmpty { "Vídeo Detectado" },
                quality = candidate.quality,
                format = candidate.format.name,
                duration = formatDuration(candidate.duration),
                thumbnail = extractThumbnail(candidate.url, candidate.metadata),
                fileSize = formatFileSize(candidate.bitrate, candidate.duration),
                isLive = candidate.isLive,
                score = candidate.score,
                metadata = candidate.metadata
            )
        }
        
        val specialCasesHandled = systemResult.specialCasesDetected.map { specialCase ->
            when (specialCase) {
                UltraPreciseVideoDetectionSystem.SpecialCase.OFFSCREEN_CONTENT -> "Conteúdo Offscreen"
                UltraPreciseVideoDetectionSystem.SpecialCase.PICTURE_IN_PICTURE -> "Picture-in-Picture"
                UltraPreciseVideoDetectionSystem.SpecialCase.LIVE_STREAM -> "Stream Ao Vivo"
                UltraPreciseVideoDetectionSystem.SpecialCase.QUARANTINED_CONTENT -> "Conteúdo em Quarentena"
                UltraPreciseVideoDetectionSystem.SpecialCase.MOBILE_OPTIMIZED -> "Otimizado para Mobile"
            }
        }
        
        return VideoDetectionResult(
            videos = videos,
            source = systemResult.detectionSource.name,
            processingTime = systemResult.processingTime,
            qualityEnhanced = systemResult.qualityMappingApplied,
            specialCasesHandled = specialCasesHandled
        )
    }

    /**
     * Atualizar store de detecção de vídeo
     */
    private suspend fun updateVideoDetectionStore(result: VideoDetectionResult, tabId: String = "default") {
        try {
            // Converter para formato do store existente (List<Pair<String, String>>)
            val pairs = result.videos.map { video ->
                video.quality to video.url
            }
            
            // Atualizar store com tabId e pares corretos
            videoDetectionStore.upsertPairs(tabId, pairs)
            
        } catch (e: Exception) {
            notifyError("Erro ao atualizar store: ${e.message}")
        }
    }

    /**
     * Atualizar métricas de performance
     */
    private fun updatePerformanceMetrics(
        metrics: UltraPreciseVideoDetectionSystem.DetectionPerformanceMetrics
    ) {
        val snapshot = PerformanceSnapshot(
            averageDetectionTime = metrics.totalTime.toDouble(),
            successRate = metrics.cacheHitRate,
            memoryUsage = metrics.memoryUsage,
            cpuUsage = metrics.cpuUsage,
            cacheHitRate = metrics.cacheHitRate
        )
        
        updateIntegrationState { 
            copy(performanceMetrics = snapshot)
        }
    }

    /**
     * Atualizar estado do sistema na integração
     */
    private fun updateSystemStateInIntegration(
        systemState: UltraPreciseVideoDetectionSystem.SystemState
    ) {
        val isReady = systemState == UltraPreciseVideoDetectionSystem.SystemState.READY
        val isDetecting = systemState == UltraPreciseVideoDetectionSystem.SystemState.DETECTING
        
        updateIntegrationState { 
            copy(
                isSystemReady = isReady,
                isDetecting = isDetecting
            )
        }
    }

    /**
     * Atualizar estado da integração
     */
    private fun updateIntegrationState(update: IntegrationState.() -> IntegrationState) {
        _integrationState.value = _integrationState.value.update()
        notifyStateCallbacks(_integrationState.value)
    }

    /**
     * Formatar duração
     */
    private fun formatDuration(duration: Double?): String? {
        return duration?.let {
            val minutes = (it / 60).toInt()
            val seconds = (it % 60).toInt()
            String.format("%02d:%02d", minutes, seconds)
        }
    }

    /**
     * Extrair thumbnail
     */
    private fun extractThumbnail(url: String, metadata: Map<String, Any>): String? {
        return try {
            // Tentar extrair thumbnail dos metadados
            metadata["thumbnail"] as? String
                ?: metadata["poster"] as? String
                ?: generateThumbnailFromUrl(url)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Gerar thumbnail da URL
     */
    private fun generateThumbnailFromUrl(url: String): String? {
        return when {
            url.contains("youtube.com") || url.contains("youtu.be") -> {
                // Extrair ID do vídeo e gerar URL do thumbnail
                val videoId = extractYouTubeVideoId(url)
                videoId?.let { "https://img.youtube.com/vi/$it/maxresdefault.jpg" }
            }
            url.contains("vimeo.com") -> {
                // Lógica para Vimeo se necessário
                null
            }
            else -> null
        }
    }

    /**
     * Extrair ID do vídeo do YouTube
     */
    private fun extractYouTubeVideoId(url: String): String? {
        return try {
            val regex = Regex("(?:youtube\\.com/watch\\?v=|youtu\\.be/)([a-zA-Z0-9_-]+)")
            val match = regex.find(url)
            match?.groupValues?.get(1)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Formatar tamanho do arquivo
     */
    private fun formatFileSize(bitrate: Long?, duration: Double?): String? {
        return if (bitrate != null && duration != null) {
            try {
                val sizeInBytes = (bitrate * duration / 8).toLong()
                formatBytes(sizeInBytes)
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }

    /**
     * Formatar bytes
     */
    private fun formatBytes(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        var size = bytes.toDouble()
        var unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }
        
        return String.format("%.1f %s", size, units[unitIndex])
    }

    /**
     * Obter histórico de detecções
     */
    fun getDetectionHistory(): List<VideoDetectionResult> {
        return detectionHistory.toList()
    }

    /**
     * Obter estatísticas da integração
     */
    fun getIntegrationStatistics(): Map<String, Any> {
        val systemStats = ultraPreciseSystem.getSystemStatistics()
        val currentState = _integrationState.value
        
        val avgProcessing = detectionHistory
            .takeIf { it.isNotEmpty() }
            ?.map { it.processingTime }
            ?.average() ?: 0.0
        val successRate = if (detectionHistory.isNotEmpty()) {
            detectionHistory.count { it.videos.isNotEmpty() }.toDouble() / detectionHistory.size
        } else 0.0
        
        return mutableMapOf<String, Any>().apply {
            put("integrationState", currentState)
            put("systemStatistics", systemStats)
            put("detectionHistory", detectionHistory.size)
            put("activeWebViews", activeWebViews.size)
            put("totalVideosDetected", currentState.totalVideosDetected)
            put("averageProcessingTime", avgProcessing)
            put("successRate", successRate)
        }
    }

    /**
     * Limpar histórico
     */
    fun clearDetectionHistory() {
        detectionHistory.clear()
        updateIntegrationState { 
            copy(
                totalVideosDetected = 0,
                detectionErrors = emptyList()
            )
        }
    }

    /**
     * Remover WebView
     */
    fun removeWebView(webViewId: String) {
        activeWebViews.remove(webViewId)
    }

    /**
     * Adicionar callback de UI
     */
    fun addUICallback(callback: (VideoDetectionResult) -> Unit) {
        uiCallbacks.add(callback)
    }

    /**
     * Remover callback de UI
     */
    fun removeUICallback(callback: (VideoDetectionResult) -> Unit) {
        uiCallbacks.remove(callback)
    }

    /**
     * Adicionar callback de erro
     */
    fun addErrorCallback(callback: (String) -> Unit) {
        errorCallbacks.add(callback)
    }

    /**
     * Adicionar callback de estado
     */
    fun addStateCallback(callback: (IntegrationState) -> Unit) {
        stateCallbacks.add(callback)
    }

    /**
     * Notificar callbacks de UI
     */
    private fun notifyUICallbacks(result: VideoDetectionResult) {
        uiCallbacks.forEach { callback ->
            try {
                callback(result)
            } catch (e: Exception) {
                // Ignorar erros em callbacks
            }
        }
    }

    /**
     * Notificar erro
     */
    private fun notifyError(message: String) {
        errorCallbacks.forEach { callback ->
            try {
                callback(message)
            } catch (e: Exception) {
                // Ignorar erros em callbacks
            }
        }
    }

    /**
     * Notificar callbacks de estado
     */
    private fun notifyStateCallbacks(state: IntegrationState) {
        stateCallbacks.forEach { callback ->
            try {
                callback(state)
            } catch (e: Exception) {
                // Ignorar erros em callbacks
            }
        }
    }

    /**
     * Suspender integração
     */
    fun suspend() {
        ultraPreciseSystem.suspendSystem()
        updateIntegrationState { 
            copy(
                isSystemReady = false,
                isDetecting = false
            )
        }
    }

    /**
     * Retomar integração
     */
    fun resume() {
        ultraPreciseSystem.resumeSystem()
        updateIntegrationState { 
            copy(isSystemReady = true)
        }
    }

    /**
     * Limpeza de recursos
     */
    fun cleanup() {
        scope.cancel()
        activeWebViews.clear()
        detectionHistory.clear()
        uiCallbacks.clear()
        errorCallbacks.clear()
        stateCallbacks.clear()
        
        ultraPreciseSystem.cleanup()
        instance = null
    }
}

/**
 * ViewModel para integração com a UI
 */
class VideoDetectionViewModel(private val context: Context) : ViewModel() {
    
    private val integration by lazy { VideoDetectionIntegration.getInstance(context) }
    
    val integrationState = integration.integrationState
    val detectionResults = integration.detectionResults
    
    /**
     * Configurar WebView
     */
    suspend fun setupWebView(
        webView: WebView,
        webViewId: String
    ): Boolean {
        return integration.setupWebViewForDetection(webView, webViewId)
    }
    
    /**
     * Detectar vídeos
     */
    suspend fun detectVideos(
        webViewId: String,
        pageUrl: String,
        enableYouTubeOptimization: Boolean = true
    ): VideoDetectionIntegration.VideoDetectionResult? {
        return integration.detectVideosInCurrentPage(
            webViewId = webViewId,
            pageUrl = pageUrl,
            enableYouTubeOptimization = enableYouTubeOptimization
        )
    }
    
    /**
     * Obter estatísticas
     */
    fun getStatistics(): Map<String, Any> {
        return integration.getIntegrationStatistics()
    }
    
    /**
     * Limpar histórico
     */
    fun clearHistory() {
        integration.clearDetectionHistory()
    }
    
    override fun onCleared() {
        super.onCleared()
        integration.cleanup()
    }
}
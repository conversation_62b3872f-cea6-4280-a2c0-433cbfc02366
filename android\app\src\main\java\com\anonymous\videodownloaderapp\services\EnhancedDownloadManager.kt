package com.anonymous.videodownloaderapp.services

import android.content.Intent
import android.os.Binder
import android.os.IBinder
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.anonymous.videodownloaderapp.data.DownloadItem
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Manager service that orchestrates both progressive (OkHttp) and segmented (HLS/DASH via Media3)
 * downloads. For now, progressive is handled by EnhancedDownloadService; segmented manifests are
 * enqueued via UniversalVideoUrlExtractor.enqueueHls/enqueueDash that currently delegates to progressive
 * until full Media3 DownloadService is introduced.
 */
class EnhancedDownloadManager : LifecycleService() {

    private val binder = LocalBinder()
    private val progressiveService = EnhancedDownloadService(this)

    private val _downloadStates = MutableStateFlow<Map<String, DownloadState>>(emptyMap())
    val downloadStates: StateFlow<Map<String, DownloadState>> = _downloadStates.asStateFlow()

    inner class LocalBinder : Binder() {
        fun getService(): EnhancedDownloadManager = this@EnhancedDownloadManager
    }

    override fun onBind(intent: Intent): IBinder {
        super.onBind(intent)
        return binder
    }

    override fun onCreate() {
        super.onCreate()
        // Basic foreground to keep process alive during downloads
        startForeground(1, DownloadNotificationService(this).createForegroundNotification())
    }

    override fun onDestroy() {
        super.onDestroy()
        progressiveService.shutdown()
    }

    // Progressive direct-file download
    fun startDownload(downloadItem: DownloadItem) {
        lifecycleScope.launch {
            progressiveService.startDownload(downloadItem)
        }
    }

    fun pauseDownload(downloadId: String) {
        progressiveService.pauseDownload(downloadId)
    }

    fun resumeDownload(downloadId: String) {
        progressiveService.resumeDownload(downloadId)
    }

    fun cancelDownload(downloadId: String) {
        progressiveService.cancelDownload(downloadId)
    }

    data class DownloadState(
        val downloadItem: DownloadItem,
        val progress: Int,
        val bytesDownloaded: Long,
        val totalBytes: Long,
        val status: DownloadItem.Status,
        val speed: Long = 0L,
        val eta: Long = 0L
    )
}
{"logs": [{"outputFile": "com.anonymous.videodownloaderapp-mergeDebugResources-79:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c83662cb1041180272524de55f94ad06\\transformed\\media3-exoplayer-1.4.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,241,308,385,455,549,641", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "115,174,236,303,380,450,544,636,707"}, "to": {"startLines": "96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7576,7641,7700,7762,7829,7906,7976,8070,8162", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "7636,7695,7757,7824,7901,7971,8065,8157,8228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c594a6b1ad90298d1781df40da764bd0\\transformed\\foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "259,260", "startColumns": "4,4", "startOffsets": "21958,22048", "endColumns": "89,89", "endOffsets": "22043,22133"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\366aa02d18db07ca95bf2d64251e5a94\\transformed\\appcompat-1.7.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "955,1063,1163,1272,1358,1463,1581,1667,1746,1837,1930,2025,2119,2213,2306,2402,2501,2592,2686,2766,2873,2974,3071,3177,3277,3375,3525,20963", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "1058,1158,1267,1353,1458,1576,1662,1741,1832,1925,2020,2114,2208,2301,2397,2496,2587,2681,2761,2868,2969,3066,3172,3272,3370,3520,3620,21039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ea0d069a154b88d6448f277841df52b5\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,997,1079,1151,1227,1307,1381,1458,1530", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,75,79,73,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,992,1074,1146,1222,1302,1376,1453,1525,1647"}, "to": {"startLines": "65,66,67,68,69,124,125,242,243,245,246,250,252,253,254,256,257,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5051,5145,5230,5329,5430,9478,9555,20548,20639,20800,20881,21209,21356,21432,21512,21687,21764,21836", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,75,79,73,76,71,121", "endOffsets": "5140,5225,5324,5425,5514,9550,9643,20634,20716,20876,20958,21276,21427,21507,21581,21759,21831,21953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd2b74cc75dcc4061ff406ef39af9f1c\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "55,56,57,58,59,60,61,255", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4029,4125,4227,4325,4430,4535,4647,21586", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "4120,4222,4320,4425,4530,4642,4758,21682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a1b6440c5b30b54fb1514c977cfe8efa\\transformed\\material-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1022,1087,1180,1255,1320,1408,1473,1539,1597,1668,1734,1788,1898,1958,2022,2076,2149,2265,2349,2425,2516,2597,2678,2811,2896,2981,3114,3204,3278,3330,3381,3447,3524,3606,3677,3751,3825,3904,3981,4053,4160,4249,4325,4416,4511,4585,4658,4752,4806,4880,4952,5038,5124,5186,5250,5313,5384,5485,5588,5683,5783,5839,5894,5973,6059,6138", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "263,339,413,496,585,667,763,871,955,1017,1082,1175,1250,1315,1403,1468,1534,1592,1663,1729,1783,1893,1953,2017,2071,2144,2260,2344,2420,2511,2592,2673,2806,2891,2976,3109,3199,3273,3325,3376,3442,3519,3601,3672,3746,3820,3899,3976,4048,4155,4244,4320,4411,4506,4580,4653,4747,4801,4875,4947,5033,5119,5181,5245,5308,5379,5480,5583,5678,5778,5834,5889,5968,6054,6133,6208"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,70,71,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,244,248,249,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,3625,3701,3775,3858,3947,4763,4859,4967,5519,5581,9310,9403,9648,15974,16062,16127,16193,16251,16322,16388,16442,16552,16612,16676,16730,16803,16919,17003,17079,17170,17251,17332,17465,17550,17635,17768,17858,17932,17984,18035,18101,18178,18260,18331,18405,18479,18558,18635,18707,18814,18903,18979,19070,19165,19239,19312,19406,19460,19534,19606,19692,19778,19840,19904,19967,20038,20139,20242,20337,20437,20493,20721,21044,21130,21281", "endLines": "22,50,51,52,53,54,62,63,64,70,71,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,244,248,249,251", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "950,3696,3770,3853,3942,4024,4854,4962,5046,5576,5641,9398,9473,9708,16057,16122,16188,16246,16317,16383,16437,16547,16607,16671,16725,16798,16914,16998,17074,17165,17246,17327,17460,17545,17630,17763,17853,17927,17979,18030,18096,18173,18255,18326,18400,18474,18553,18630,18702,18809,18898,18974,19065,19160,19234,19307,19401,19455,19529,19601,19687,19773,19835,19899,19962,20033,20134,20237,20332,20432,20488,20543,20795,21125,21204,21351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\19dbdb4d446295775d71c6ca0010df0c\\transformed\\media3-ui-1.4.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,491,692,783,876,957,1053,1145,1216,1283,1372,1459,1527,1592,1655,1727,1820,1910,2001,2078,2160,2232,2303,2397,2493,2558,2622,2675,2733,2781,2842,2910,2982,3051,3123,3190,3245,3310,3362,3423,3498,3573,3630", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,92,89,90,76,81,71,70,93,95,64,63,52,57,47,60,67,71,68,71,66,54,64,51,60,74,74,56,68", "endOffsets": "282,486,687,778,871,952,1048,1140,1211,1278,1367,1454,1522,1587,1650,1722,1815,1905,1996,2073,2155,2227,2298,2392,2488,2553,2617,2670,2728,2776,2837,2905,2977,3046,3118,3185,3240,3305,3357,3418,3493,3568,3625,3694"}, "to": {"startLines": "2,11,15,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,586,5646,5737,5830,5911,6007,6099,6170,6237,6326,6413,6481,6546,6609,6681,6774,6864,6955,7032,7114,7186,7257,7351,7447,7512,8233,8286,8344,8392,8453,8521,8593,8662,8734,8801,8856,8921,8973,9034,9109,9184,9241", "endLines": "10,14,18,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,92,89,90,76,81,71,70,93,95,64,63,52,57,47,60,67,71,68,71,66,54,64,51,60,74,74,56,68", "endOffsets": "377,581,782,5732,5825,5906,6002,6094,6165,6232,6321,6408,6476,6541,6604,6676,6769,6859,6950,7027,7109,7181,7252,7346,7442,7507,7571,8281,8339,8387,8448,8516,8588,8657,8729,8796,8851,8916,8968,9029,9104,9179,9236,9305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\576ca5b088d8fb564e2e136af4f08d54\\transformed\\material3-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,393,507,604,705,823,960,1082,1234,1324,1420,1518,1620,1738,1861,1962,2094,2226,2355,2522,2644,2768,2895,3017,3116,3215,3336,3457,3560,3671,3779,3918,4062,4170,4276,4359,4457,4554,4667,4751,4836,4936,5016,5101,5198,5301,5398,5503,5593,5701,5804,5914,6032,6112,6217", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "166,279,388,502,599,700,818,955,1077,1229,1319,1415,1513,1615,1733,1856,1957,2089,2221,2350,2517,2639,2763,2890,3012,3111,3210,3331,3452,3555,3666,3774,3913,4057,4165,4271,4354,4452,4549,4662,4746,4831,4931,5011,5096,5193,5296,5393,5498,5588,5696,5799,5909,6027,6107,6212,6311"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9713,9829,9942,10051,10165,10262,10363,10481,10618,10740,10892,10982,11078,11176,11278,11396,11519,11620,11752,11884,12013,12180,12302,12426,12553,12675,12774,12873,12994,13115,13218,13329,13437,13576,13720,13828,13934,14017,14115,14212,14325,14409,14494,14594,14674,14759,14856,14959,15056,15161,15251,15359,15462,15572,15690,15770,15875", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "9824,9937,10046,10160,10257,10358,10476,10613,10735,10887,10977,11073,11171,11273,11391,11514,11615,11747,11879,12008,12175,12297,12421,12548,12670,12769,12868,12989,13110,13213,13324,13432,13571,13715,13823,13929,14012,14110,14207,14320,14404,14489,14589,14669,14754,14851,14954,15051,15156,15246,15354,15457,15567,15685,15765,15870,15969"}}]}]}
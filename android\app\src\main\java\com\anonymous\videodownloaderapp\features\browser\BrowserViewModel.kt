package com.anonymous.videodownloaderapp.features.browser

import android.content.Context
import android.webkit.WebView
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.anonymous.videodownloaderapp.data.DownloadRepository
import com.anonymous.videodownloaderapp.services.DownloadWorker
import com.anonymous.videodownloaderapp.services.HybridVideoDetectionService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.json.JSONArray
import java.io.IOException

data class BrowserUiState(
    val currentUrl: String = BrowserConstants.DEFAULT_URL,
    val addressBarText: String = BrowserConstants.DEFAULT_URL,
    val isLoading: Boolean = false,
    val detectedVideos: List<VideoQuality> = emptyList(),
    val showVideoDialog: Boolean = false,
    val showSettingsMenu: Boolean = false,
    val isAddressBarExpanded: Boolean = false,
    val isAddressBarFocused: Boolean = false,
    val errorMessage: String? = null,
    val canGoBack: Boolean = false,
    val canGoForward: Boolean = false
)

class BrowserViewModel(
    private val context: Context,
    private val downloadRepository: DownloadRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(BrowserUiState())
    val uiState: StateFlow<BrowserUiState> = _uiState.asStateFlow()

    private var videoDetectorScript: String? = null
    private val hybridDetector = HybridVideoDetectionService.getInstance(context)

    // Observe VideoDetectionStore updates per current tab
    private val storeListener = object : VideoDetectionStore.Listener {
        override fun onTabUpdated(tabId: String, entries: List<VideoDetectionStore.QualityEntry>) {
            val currentTab = _uiState.value.currentUrl
            if (tabId == currentTab) {
                viewModelScope.launch {
                    val list = entries.map { e ->
                        val downloaded = try { downloadRepository.isVideoDownloaded(e.url) } catch (_: Throwable) { false }
                        VideoQuality(
                            quality = e.quality,
                            url = e.url,
                            format = inferFormatFromUrl(e.url),
                            isDownloaded = downloaded
                        )
                    }
                    _uiState.value = _uiState.value.copy(detectedVideos = list)
                }
            }
        }
    }

    private fun inferFormatFromUrl(url: String): String {
        val lower = url.lowercase()
        return when {
            ".m3u8" in lower -> "hls"
            ".mpd" in lower -> "dash"
            ".webm" in lower -> "webm"
            ".ts" in lower -> "ts"
            else -> "mp4"
        }
    }

    init {
        loadVideoDetectorScript()
        setupHybridDetection()
        // Start listening store updates
        VideoDetectionStore.addListener(storeListener)
    }


    private fun setupHybridDetection() {
        // Observar resultados do detector híbrido
        viewModelScope.launch {
            hybridDetector.detectedVideos.collect { videoQualities ->
                _uiState.value = _uiState.value.copy(detectedVideos = videoQualities)
            }
        }
    }

    private fun loadVideoDetectorScript() {
        viewModelScope.launch {
            try {
                videoDetectorScript = context.assets.open(BrowserConstants.VIDEO_DETECTOR_SCRIPT_PATH)
                .bufferedReader().use { it.readText() }
            } catch (e: IOException) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Erro ao carregar script de detecção de vídeos"
                )
            }
        }
    }

    fun onAddressBarTextChanged(text: String) {
        _uiState.value = _uiState.value.copy(addressBarText = text)
    }
    
    fun updateAddressBarText(text: String) {
        _uiState.value = _uiState.value.copy(addressBarText = text)
    }

    fun onUrlChanged(url: String) {
        _uiState.value = _uiState.value.copy(
            currentUrl = url,
            addressBarText = url
        )
    }

    fun onLoadingStateChanged(isLoading: Boolean) {
        _uiState.value = _uiState.value.copy(isLoading = isLoading)
    }

    fun onAddressBarExpandedChanged(isExpanded: Boolean) {
        _uiState.value = _uiState.value.copy(isAddressBarExpanded = isExpanded)
    }

    fun onAddressBarFocusChanged(isFocused: Boolean) {
        _uiState.value = _uiState.value.copy(isAddressBarFocused = isFocused)
    }

    fun onVideoDialogVisibilityChanged(isVisible: Boolean) {
        _uiState.value = _uiState.value.copy(showVideoDialog = isVisible)
    }

    fun onSettingsMenuVisibilityChanged(isVisible: Boolean) {
        _uiState.value = _uiState.value.copy(showSettingsMenu = isVisible)
    }

    fun onGoToUrl(webView: WebView?) {
        val inputUrl = _uiState.value.addressBarText.trim()
        val formattedUrl = when {
            inputUrl.isEmpty() -> BrowserConstants.DEFAULT_URL
            inputUrl.startsWith("http://") || inputUrl.startsWith("https://") -> inputUrl
            inputUrl.contains(".") -> "https://$inputUrl"
            else -> "https://www.google.com/search?q=$inputUrl"
        }
        
        webView?.loadUrl(formattedUrl)
        _uiState.value = _uiState.value.copy(
            isAddressBarExpanded = false,
            isAddressBarFocused = false
        )
    }

    fun onRefresh(webView: WebView?) {
        webView?.apply {
            stopLoading()
            reload()
        }
    }

    fun onHomeClicked(webView: WebView?) {
        _uiState.value = _uiState.value.copy(addressBarText = BrowserConstants.DEFAULT_URL)
        webView?.loadUrl(BrowserConstants.DEFAULT_URL)
    }

    fun onGoBack(webView: WebView?) {
        webView?.goBack()
    }

    fun onGoForward(webView: WebView?) {
        webView?.goForward()
    }

    fun onCanGoBackChanged(canGoBack: Boolean) {
        _uiState.value = _uiState.value.copy(canGoBack = canGoBack)
    }

    fun onCanGoForwardChanged(canGoForward: Boolean) {
        _uiState.value = _uiState.value.copy(canGoForward = canGoForward)
    }

    fun detectVideos(webView: WebView?) {
        viewModelScope.launch {
            try {
                // Usar detector híbrido como método principal
                val currentUrl = _uiState.value.currentUrl
                val detectedVideos = hybridDetector.detectVideos(
                    webView = webView,
                    pageUrl = currentUrl,
                    tabId = currentUrl // Usar URL como tabId por simplicidade
                )
                
                // Atualizar estado com vídeos detectados
                _uiState.value = _uiState.value.copy(detectedVideos = detectedVideos)
                
            } catch (e: Exception) {
                // Fallback para método JavaScript original em caso de erro
                detectVideosJavaScriptFallback(webView)
            }
        }
    }

    /**
     * Fallback para detecção JavaScript original (mantido para compatibilidade)
     */
    private fun detectVideosJavaScriptFallback(webView: WebView?) {
        val script = videoDetectorScript ?: return
        
        webView?.evaluateJavascript(script) { result ->
            viewModelScope.launch {
                try {
                    val cleanResult = result.replace("\\", "").removeSurrounding("\"")
                    if (cleanResult != "null" && cleanResult.isNotEmpty()) {
                        val videoArray = JSONArray(cleanResult)
                        val newVideos = mutableListOf<VideoQuality>()
                        
                        for (i in 0 until videoArray.length()) {
                            val video = videoArray.getJSONObject(i)
                            val videoUrl = video.getString("url")
                            
                            val isDownloaded = downloadRepository.isVideoDownloaded(videoUrl)
                            newVideos.add(
                                VideoQuality(
                                    quality = video.getString("quality"),
                                    url = videoUrl,
                                    format = video.optString("format", "mp4"),
                                    isDownloaded = isDownloaded
                                )
                            )
                        }
                        
                        _uiState.value = _uiState.value.copy(detectedVideos = newVideos)
                    }
                } catch (e: Exception) {
                    // Ignore parsing errors
                }
            }
        }
    }

    fun onDownloadVideo(quality: String, url: String, format: String) {
        viewModelScope.launch {
            val lower = format.lowercase()
            when {
                lower.contains("hls") || url.contains(".m3u8", ignoreCase = true) -> {
                    val extractor = com.anonymous.videodownloaderapp.services.UniversalVideoUrlExtractor(
                        context = context,
                        repo = downloadRepository
                    )
                    extractor.enqueueHls(context, url, title = "Vídeo - $quality")
                }
                lower.contains("dash") || url.contains(".mpd", ignoreCase = true) -> {
                    val extractor = com.anonymous.videodownloaderapp.services.UniversalVideoUrlExtractor(
                        context = context,
                        repo = downloadRepository
                    )
                    extractor.enqueueDash(context, url, title = "Vídeo - $quality")
                }
                else -> {
                    val mime = when {
                        url.contains(".webm", ignoreCase = true) -> "video/webm"
                        url.contains(".ts", ignoreCase = true) -> "video/mp2t"
                        url.contains(".m4v", ignoreCase = true) -> "video/mp4"
                        else -> "video/mp4"
                    }
                    DownloadWorker.enqueue(
                        context = context,
                        url = url,
                        title = "Vídeo - $quality",
                        mimeType = mime
                    )
                }
            }
            _uiState.value = _uiState.value.copy(showVideoDialog = false)
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun getTruncatedAddressText(): String {
        val text = _uiState.value.addressBarText
        return if (text.length > BrowserConstants.ADDRESS_BAR_MAX_DISPLAY_LENGTH) {
            text.take(BrowserConstants.ADDRESS_BAR_MAX_DISPLAY_LENGTH) + "..."
        } else {
            text
        }
    }

    override fun onCleared() {
        super.onCleared()
        // Limpar recursos do detector híbrido se necessário
        hybridDetector.performMaintenance()
        // Remover listener do store
        VideoDetectionStore.removeListener(storeListener)
    }
}
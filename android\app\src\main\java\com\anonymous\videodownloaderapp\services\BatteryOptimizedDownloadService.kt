package com.anonymous.videodownloaderapp.services

import android.app.job.JobParameters
import android.app.job.JobService
import android.os.Build
import androidx.annotation.RequiresApi

@RequiresApi(Build.VERSION_CODES.LOLLIPOP)
class BatteryOptimizedDownloadService : JobService() {

    override fun onStartJob(params: JobParameters?): <PERSON><PERSON><PERSON> {
        // Start your download logic here
        return true
    }

    override fun onStopJob(params: JobParameters?): <PERSON><PERSON><PERSON> {
        // Stop your download logic here
        return true
    }
}
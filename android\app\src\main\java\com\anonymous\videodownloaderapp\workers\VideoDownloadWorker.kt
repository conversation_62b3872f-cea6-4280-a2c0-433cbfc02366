package com.anonymous.videodownloaderapp.workers

import android.app.Notification
import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.ForegroundInfo
import androidx.work.WorkerParameters
import com.anonymous.videodownloaderapp.data.DownloadItem
import com.anonymous.videodownloaderapp.services.DownloadTracker
import com.anonymous.videodownloaderapp.services.NotificationHelper
import com.anonymous.videodownloaderapp.utils.FileUtils
import com.google.gson.Gson
import okhttp3.OkHttpClient
import okhttp3.Request
import okio.buffer
import okio.sink
import java.io.File
import java.util.UUID

class VideoDownloadWorker(appContext: Context, workerParams: WorkerParameters) :
    CoroutineWorker(appContext, workerParams) {

    companion object {
        const val TAG_DOWNLOAD = "video_download"
    }

    private val gson = Gson()
    private fun buildForegroundInfo(title: String, progress: Int?): ForegroundInfo {
        val notification: Notification = NotificationHelper.foregroundNotification(
            context = applicationContext,
            title = title,
            text = if (progress != null) "$progress%" else "Downloading...",
            progress = progress,
            isPaused = false,
            downloadId = 1L
        )
        return ForegroundInfo(NotificationHelper.NOTIFICATION_ID_FOREGROUND, notification)
    }

    override suspend fun doWork(): Result {
        // Parse inputs: support either a JSON DownloadItem or individual fields
        val downloadItemJson = inputData.getString("downloadItem")
        var videoUrl: String? = null
        var fileName: String? = null
        var downloadId: Long = 0
        var providedMime: String? = null

        if (!downloadItemJson.isNullOrBlank()) {
            runCatching {
                val item = gson.fromJson(downloadItemJson, com.anonymous.videodownloaderapp.data.DownloadItem::class.java)
                videoUrl = item.url
                fileName = item.title
                downloadId = item.id
                providedMime = item.mimeType
            }.onFailure {
                // Fallback to legacy keys below
            }
        }
        if (videoUrl.isNullOrBlank()) {
            videoUrl = inputData.getString("videoUrl")
            fileName = fileName ?: inputData.getString("fileName")
            downloadId = if (downloadId != 0L) downloadId else inputData.getLong("downloadId", 0)
            providedMime = providedMime ?: inputData.getString("mimeType")
        }

        if (videoUrl.isNullOrBlank()) return Result.failure()
        val safeFileName = fileName ?: "video_${UUID.randomUUID()}.mp4"

        val mime = FileUtils.resolveMimeType(videoUrl!!, providedMime)

        val downloadTracker = DownloadTracker(applicationContext)

        // Enter foreground before heavy I/O
        setForeground(buildForegroundInfo(safeFileName, 0))

        val tempFile = File(applicationContext.cacheDir, safeFileName)

        try {
            downloadTracker.addDownload(
                com.anonymous.videodownloaderapp.data.DownloadItem(
                    id = downloadId,
                    url = videoUrl!!,
                    title = safeFileName,
                    mimeType = mime,
                    status = com.anonymous.videodownloaderapp.data.DownloadItem.Status.Running,
                    progress = 0,
                    bytesDownloaded = 0,
                    totalBytes = 0,
                    mediaStoreUri = null,
                    error = null,
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis()
                )
            )

            val client = OkHttpClient()
            val request = Request.Builder().url(videoUrl!!).build()

            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    downloadTracker.updateDownload(
                        com.anonymous.videodownloaderapp.data.DownloadItem(
                            id = downloadId,
                            url = videoUrl!!,
                            title = safeFileName,
                            mimeType = mime,
                            status = com.anonymous.videodownloaderapp.data.DownloadItem.Status.Failed,
                            progress = 0,
                            bytesDownloaded = 0,
                            totalBytes = 0,
                            mediaStoreUri = null,
                            error = response.message,
                            createdAt = System.currentTimeMillis(),
                            updatedAt = System.currentTimeMillis()
                        )
                    )
                    return Result.failure()
                }

                val body = response.body ?: return Result.failure()
                val contentLength = body.contentLength()

                val sink = tempFile.sink().buffer()
                var bytesRead = 0L
                val buffer = ByteArray(8 * 1024)

                while (true) {
                    val read = body.source().read(buffer)
                    if (read == -1) break
                    sink.write(buffer, 0, read)
                    bytesRead += read
                    val progress = (bytesRead * 100f / contentLength).coerceIn(0f, 100f).toInt()

                    downloadTracker.updateDownload(
                        com.anonymous.videodownloaderapp.data.DownloadItem(
                            id = downloadId,
                            url = videoUrl!!,
                            title = safeFileName,
                            mimeType = mime,
                            status = com.anonymous.videodownloaderapp.data.DownloadItem.Status.Running,
                            progress = progress,
                            bytesDownloaded = bytesRead,
                            totalBytes = contentLength,
                            mediaStoreUri = null,
                            error = null,
                            createdAt = System.currentTimeMillis(),
                            updatedAt = System.currentTimeMillis()
                        )
                    )
                    setForeground(buildForegroundInfo(safeFileName, progress))
                }
                sink.close()

                downloadTracker.updateDownload(
                    com.anonymous.videodownloaderapp.data.DownloadItem(
                        id = downloadId,
                        url = videoUrl!!,
                        title = safeFileName,
                        mimeType = mime,
                        status = com.anonymous.videodownloaderapp.data.DownloadItem.Status.Completed,
                        progress = 100,
                        bytesDownloaded = contentLength,
                        totalBytes = contentLength,
                        mediaStoreUri = null,
                        error = null,
                        createdAt = System.currentTimeMillis(),
                        updatedAt = System.currentTimeMillis()
                    )
                )
                setForeground(buildForegroundInfo(safeFileName, 100))
                return Result.success()
            }
        } catch (e: Exception) {
            downloadTracker.updateDownload(
                com.anonymous.videodownloaderapp.data.DownloadItem(
                    id = downloadId,
                    url = videoUrl!!,
                    title = safeFileName,
                    mimeType = mime,
                    status = com.anonymous.videodownloaderapp.data.DownloadItem.Status.Failed,
                    progress = 0,
                    bytesDownloaded = 0,
                    totalBytes = 0,
                    mediaStoreUri = null,
                    error = e.localizedMessage,
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis()
                )
            )
            return Result.failure()
        }
    }
}

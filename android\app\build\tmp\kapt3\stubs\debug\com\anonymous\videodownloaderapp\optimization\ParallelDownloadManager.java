package com.anonymous.videodownloaderapp.optimization;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u00002\u00020\u0001:\u0003,-.B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0007J\u0010\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u001e\u0010\u001c\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001d\u001a\u00020\u001eH\u0082@\u00a2\u0006\u0002\u0010\u001fJ\u0016\u0010 \u001a\u00020\u00162\u0006\u0010!\u001a\u00020\u0019H\u0082@\u00a2\u0006\u0002\u0010\"J\u000e\u0010#\u001a\u00020$2\u0006\u0010\u0017\u001a\u00020\u0007J\u0006\u0010%\u001a\u00020&J\u0010\u0010\'\u001a\u00020\u00162\u0006\u0010!\u001a\u00020\u0019H\u0002J\u000e\u0010(\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0007J\u0016\u0010)\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010\u001a\u001a\u00020\u001bJ\u0006\u0010*\u001a\u00020\u0016J\u000e\u0010+\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u001bR\u001a\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u000e0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006/"}, d2 = {"Lcom/anonymous/videodownloaderapp/optimization/ParallelDownloadManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "activeDownloads", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lkotlinx/coroutines/Job;", "chunkSize", "", "coroutineScope", "Lkotlinx/coroutines/CoroutineScope;", "downloadProgress", "Ljava/util/concurrent/atomic/AtomicLong;", "httpClient", "Lokhttp3/OkHttpClient;", "maxConcurrentDownloads", "", "semaphore", "Lkotlinx/coroutines/sync/Semaphore;", "cancelDownload", "", "downloadId", "createParallelDownload", "Lcom/anonymous/videodownloaderapp/optimization/ParallelDownloadManager$ParallelDownload;", "downloadItem", "Lcom/anonymous/videodownloaderapp/data/DownloadItem;", "downloadChunk", "chunk", "Lcom/anonymous/videodownloaderapp/optimization/ParallelDownloadManager$DownloadChunk;", "(Lcom/anonymous/videodownloaderapp/data/DownloadItem;Lcom/anonymous/videodownloaderapp/optimization/ParallelDownloadManager$DownloadChunk;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "downloadParallelChunks", "parallelDownload", "(Lcom/anonymous/videodownloaderapp/optimization/ParallelDownloadManager$ParallelDownload;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getDownloadProgress", "", "getParallelStats", "Lcom/anonymous/videodownloaderapp/optimization/ParallelDownloadManager$ParallelStats;", "mergeChunks", "pauseDownload", "resumeDownload", "shutdown", "startParallelDownload", "DownloadChunk", "ParallelDownload", "ParallelStats", "app_debug"})
public final class ParallelDownloadManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope coroutineScope = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlinx.coroutines.Job> activeDownloads = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.concurrent.atomic.AtomicLong> downloadProgress = null;
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient httpClient = null;
    private final int maxConcurrentDownloads = 3;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.sync.Semaphore semaphore = null;
    private final long chunkSize = 5242880L;
    
    public ParallelDownloadManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    public final void startParallelDownload(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadItem downloadItem) {
    }
    
    private final com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.ParallelDownload createParallelDownload(com.anonymous.videodownloaderapp.data.DownloadItem downloadItem) {
        return null;
    }
    
    private final java.lang.Object downloadParallelChunks(com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.ParallelDownload parallelDownload, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object downloadChunk(com.anonymous.videodownloaderapp.data.DownloadItem downloadItem, com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.DownloadChunk chunk, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final void mergeChunks(com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.ParallelDownload parallelDownload) {
    }
    
    public final void pauseDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId) {
    }
    
    public final void resumeDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId, @org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadItem downloadItem) {
    }
    
    public final void cancelDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId) {
    }
    
    public final float getDownloadProgress(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId) {
        return 0.0F;
    }
    
    public final void shutdown() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.ParallelStats getParallelStats() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0016\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B/\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001b\u001a\u00020\nH\u00c6\u0003J;\u0010\u001c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\n2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00d6\u0001J\t\u0010 \u001a\u00020!H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001a\u0010\t\u001a\u00020\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\u000f\"\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013\u00a8\u0006\""}, d2 = {"Lcom/anonymous/videodownloaderapp/optimization/ParallelDownloadManager$DownloadChunk;", "", "chunkId", "", "startByte", "", "endByte", "file", "Ljava/io/File;", "downloaded", "", "(IJJLjava/io/File;Z)V", "getChunkId", "()I", "getDownloaded", "()Z", "setDownloaded", "(Z)V", "getEndByte", "()J", "getFile", "()Ljava/io/File;", "getStartByte", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "toString", "", "app_debug"})
    public static final class DownloadChunk {
        private final int chunkId = 0;
        private final long startByte = 0L;
        private final long endByte = 0L;
        @org.jetbrains.annotations.NotNull()
        private final java.io.File file = null;
        private boolean downloaded;
        
        public DownloadChunk(int chunkId, long startByte, long endByte, @org.jetbrains.annotations.NotNull()
        java.io.File file, boolean downloaded) {
            super();
        }
        
        public final int getChunkId() {
            return 0;
        }
        
        public final long getStartByte() {
            return 0L;
        }
        
        public final long getEndByte() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.io.File getFile() {
            return null;
        }
        
        public final boolean getDownloaded() {
            return false;
        }
        
        public final void setDownloaded(boolean p0) {
        }
        
        public final int component1() {
            return 0;
        }
        
        public final long component2() {
            return 0L;
        }
        
        public final long component3() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.io.File component4() {
            return null;
        }
        
        public final boolean component5() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.DownloadChunk copy(int chunkId, long startByte, long endByte, @org.jetbrains.annotations.NotNull()
        java.io.File file, boolean downloaded) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0011\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\bH\u00c6\u0003J\t\u0010\u0019\u001a\u00020\nH\u00c6\u0003J7\u0010\u001a\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u001b\u001a\u00020\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001e\u001a\u00020\nH\u00d6\u0001J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001R\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001a\u0010\t\u001a\u00020\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\u000f\"\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015\u00a8\u0006!"}, d2 = {"Lcom/anonymous/videodownloaderapp/optimization/ParallelDownloadManager$ParallelDownload;", "", "downloadItem", "Lcom/anonymous/videodownloaderapp/data/DownloadItem;", "chunks", "", "Lcom/anonymous/videodownloaderapp/optimization/ParallelDownloadManager$DownloadChunk;", "totalSize", "", "completedChunks", "", "(Lcom/anonymous/videodownloaderapp/data/DownloadItem;Ljava/util/List;JI)V", "getChunks", "()Ljava/util/List;", "getCompletedChunks", "()I", "setCompletedChunks", "(I)V", "getDownloadItem", "()Lcom/anonymous/videodownloaderapp/data/DownloadItem;", "getTotalSize", "()J", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
    public static final class ParallelDownload {
        @org.jetbrains.annotations.NotNull()
        private final com.anonymous.videodownloaderapp.data.DownloadItem downloadItem = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.DownloadChunk> chunks = null;
        private final long totalSize = 0L;
        private int completedChunks;
        
        public ParallelDownload(@org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.data.DownloadItem downloadItem, @org.jetbrains.annotations.NotNull()
        java.util.List<com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.DownloadChunk> chunks, long totalSize, int completedChunks) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.data.DownloadItem getDownloadItem() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.DownloadChunk> getChunks() {
            return null;
        }
        
        public final long getTotalSize() {
            return 0L;
        }
        
        public final int getCompletedChunks() {
            return 0;
        }
        
        public final void setCompletedChunks(int p0) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.data.DownloadItem component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.DownloadChunk> component2() {
            return null;
        }
        
        public final long component3() {
            return 0L;
        }
        
        public final int component4() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.ParallelDownload copy(@org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.data.DownloadItem downloadItem, @org.jetbrains.annotations.NotNull()
        java.util.List<com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.DownloadChunk> chunks, long totalSize, int completedChunks) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\'\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\bR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\b\u00a8\u0006\u0015"}, d2 = {"Lcom/anonymous/videodownloaderapp/optimization/ParallelDownloadManager$ParallelStats;", "", "activeDownloads", "", "maxConcurrent", "totalChunks", "(III)V", "getActiveDownloads", "()I", "getMaxConcurrent", "getTotalChunks", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
    public static final class ParallelStats {
        private final int activeDownloads = 0;
        private final int maxConcurrent = 0;
        private final int totalChunks = 0;
        
        public ParallelStats(int activeDownloads, int maxConcurrent, int totalChunks) {
            super();
        }
        
        public final int getActiveDownloads() {
            return 0;
        }
        
        public final int getMaxConcurrent() {
            return 0;
        }
        
        public final int getTotalChunks() {
            return 0;
        }
        
        public final int component1() {
            return 0;
        }
        
        public final int component2() {
            return 0;
        }
        
        public final int component3() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.optimization.ParallelDownloadManager.ParallelStats copy(int activeDownloads, int maxConcurrent, int totalChunks) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}
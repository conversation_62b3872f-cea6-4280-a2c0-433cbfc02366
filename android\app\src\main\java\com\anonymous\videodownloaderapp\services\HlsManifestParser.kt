package com.anonymous.videodownloaderapp.services

import android.content.Context
import okhttp3.OkHttpClient
import okhttp3.Request
import okio.Buffer
import java.net.URL
import java.util.concurrent.TimeUnit

/**
 * Minimal HLS master-playlist parser that extracts variant streams and maps them to qualities.
 * Focuses on #EXT-X-STREAM-INF + following URI, reading RESOLUTION and BANDWIDTH.
 */
class HlsManifestParser(
    context: Context? = null,
    private val http: OkHttpClient = run {
        val builder = OkHttpClient.Builder()
            .connectTimeout(8, TimeUnit.SECONDS)
            .readTimeout(8, TimeUnit.SECONDS)
        if (context != null) {
            HybridVideoDetectionService.getInstance(context).configureOkHttpClient(builder)
        }
        builder.build()
    }
) {

    data class Quality(
        val label: String,      // e.g., "1080p"
        val width: Int?,
        val height: Int?,
        val bandwidth: Long?,   // bits per second
        val codecs: String?,
        val url: String,
        val type: String = "HLS"
    )

    fun parseMaster(url: String, maxBytes: Long = 1_000_000L): List<Quality> {
        val text = fetchTextLimited(url, maxBytes) ?: return emptyList()
        return parseMasterContent(text, url)
    }

    /**
     * Parse HLS master playlist content directly (without HTTP request)
     */
    fun parseMasterContent(content: String, baseUrl: String): List<Quality> {
        val out = mutableListOf<Quality>()
        val lines = content.split("\n")
        var i = 0

        while (i < lines.size) {
            val l = lines[i].trim()
            if (l.startsWith("#EXT-X-STREAM-INF", ignoreCase = true)) {
                val attrs = parseAttributes(l.substringAfter(":"))
                val next = if (i + 1 < lines.size) lines[i + 1].trim() else ""
                if (next.isNotEmpty() && !next.startsWith("#")) {
                    val variantUrl = absolutize(baseUrl, next)
                    val width = attrs["RESOLUTION"]?.let { parseResolutionW(it) }
                    val height = attrs["RESOLUTION"]?.let { parseResolutionH(it) }
                    val bw = attrs["BANDWIDTH"]?.toLongOrNull()
                    val codecs = attrs["CODECS"]
                    val label = height?.let { normalizeHeightToLabel(it) }
                        ?: inferLabelFromString(variantUrl)
                        ?: "Auto"
                    out += Quality(
                        label = label,
                        width = width,
                        height = height,
                        bandwidth = bw,
                        codecs = codecs,
                        url = variantUrl
                    )
                }
            }
            i++
        }

        // Deduplicate by URL and prefer higher quality labels
        return dedupeKeepBest(out).sortedByDescending { extractHeight(it.label) ?: 0 }
    }

    private fun parseAttributes(attrLine: String?): Map<String, String> {
        if (attrLine.isNullOrBlank()) return emptyMap()
        // Split by comma but handle quoted values
        val map = mutableMapOf<String, String>()
        var i = 0
        var current = StringBuilder()
        val parts = mutableListOf<String>()
        var inQuotes = false
        while (i < attrLine.length) {
            val c = attrLine[i]
            if (c == '"') {
                inQuotes = !inQuotes
                current.append(c)
            } else if (c == ',' && !inQuotes) {
                parts += current.toString()
                current = StringBuilder()
            } else {
                current.append(c)
            }
            i++
        }
        if (current.isNotEmpty()) parts += current.toString()
        for (p in parts) {
            val idx = p.indexOf('=')
            if (idx > 0) {
                val k = p.substring(0, idx).trim().uppercase()
                var v = p.substring(idx + 1).trim()
                if (v.startsWith("\"") && v.endsWith("\"") && v.length >= 2) {
                    v = v.substring(1, v.length - 1)
                }
                map[k] = v
            }
        }
        return map
    }

    private fun parseResolutionW(res: String): Int? {
        // "WxH"
        val parts = res.split("x", "X")
        return parts.getOrNull(0)?.toIntOrNull()
    }
    private fun parseResolutionH(res: String): Int? {
        val parts = res.split("x", "X")
        return parts.getOrNull(1)?.toIntOrNull()
    }

    private fun fetchTextLimited(url: String, maxBytes: Long): String? {
        val req = Request.Builder().url(url).get().build()
        http.newCall(req).execute().use { resp ->
            if (!resp.isSuccessful) return null
            val body = resp.body ?: return null
            val source = body.source()
            val buffer = Buffer()
            var total = 0L
            val chunk = 8 * 1024L
            while (true) {
                val read = source.read(buffer, chunk)
                if (read == -1L) break
                total += read
                if (total > maxBytes) break
                if (source.exhausted()) break
            }
            return buffer.readString(Charsets.UTF_8)
        }
    }

    private fun absolutize(base: String, relative: String): String {
        return try {
            URL(URL(base), relative).toString()
        } catch (_: Throwable) {
            relative
        }
    }

    private fun normalizeHeightToLabel(h: Int): String {
        val common = listOf(2160, 1440, 1080, 720, 480, 360, 240, 144)
        val best = if (common.contains(h)) h else common.minByOrNull { kotlin.math.abs(it - h) } ?: h
        return "${'$'}{best}p"
    }

    private fun inferLabelFromString(s: String): String? {
        val lower = s.lowercase()
        Regex("(\\d{3,4})p").find(lower)?.let { return "${'$'}{it.groupValues[1]}p" }
        if (listOf("2160", "4k", "uhd").any { lower.contains(it) }) return "2160p"
        if (lower.contains("1440")) return "1440p"
        if (listOf("1080", "fhd").any { lower.contains(it) }) return "1080p"
        if (listOf("720", "hd").any { lower.contains(it) }) return "720p"
        if (listOf("480", "sd").any { lower.contains(it) }) return "480p"
        if (lower.contains("360")) return "360p"
        if (lower.contains("240")) return "240p"
        if (lower.contains("144")) return "144p"
        return null
    }

    private fun extractHeight(label: String?): Int? {
        if (label.isNullOrBlank()) return null
        return Regex("(\\d{3,4})p", RegexOption.IGNORE_CASE).find(label)?.groupValues?.getOrNull(1)?.toIntOrNull()
    }

    private fun dedupeKeepBest(list: List<Quality>): List<Quality> {
        val byUrl = LinkedHashMap<String, Quality>()
        list.forEach { q ->
            val existing = byUrl[q.url]
            if (existing == null) {
                byUrl[q.url] = q
            } else {
                val newH = extractHeight(q.label) ?: 0
                val oldH = extractHeight(existing.label) ?: 0
                if (newH > oldH) byUrl[q.url] = q
            }
        }
        return byUrl.values.toList()
    }
}
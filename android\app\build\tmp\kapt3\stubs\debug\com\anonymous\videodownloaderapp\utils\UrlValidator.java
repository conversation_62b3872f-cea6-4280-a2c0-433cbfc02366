package com.anonymous.videodownloaderapp.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u0005J\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\b\u001a\u00020\u0005J\u000e\u0010\u000b\u001a\u00020\n2\u0006\u0010\b\u001a\u00020\u0005J\u000e\u0010\f\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\u0005R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/anonymous/videodownloaderapp/utils/UrlValidator;", "", "()V", "videoExtensions", "", "", "extractUrlInfo", "Lcom/anonymous/videodownloaderapp/utils/UrlInfo;", "url", "isDirectVideoUrl", "", "isValidUrl", "normalizeUrl", "app_debug"})
public final class UrlValidator {
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> videoExtensions = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.utils.UrlValidator INSTANCE = null;
    
    private UrlValidator() {
        super();
    }
    
    public final boolean isValidUrl(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return false;
    }
    
    public final boolean isDirectVideoUrl(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String normalizeUrl(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.anonymous.videodownloaderapp.utils.UrlInfo extractUrlInfo(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return null;
    }
}
package com.anonymous.videodownloaderapp.services;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\u0018\u0000 \u00032\u00020\u0001:\u0001\u0003B\u0005\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0004"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/BackgroundDownloadService;", "", "()V", "Companion", "app_debug"})
public final class BackgroundDownloadService {
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.services.BackgroundDownloadService.Companion Companion = null;
    
    public BackgroundDownloadService() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u0016\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b\u00a8\u0006\n"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/BackgroundDownloadService$Companion;", "", "()V", "startBackgroundDownload", "", "context", "Landroid/content/Context;", "downloadItem", "Lcom/anonymous/videodownloaderapp/data/DownloadItem;", "startOptimizedBackgroundDownload", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final void startBackgroundDownload(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.data.DownloadItem downloadItem) {
        }
        
        public final void startOptimizedBackgroundDownload(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.data.DownloadItem downloadItem) {
        }
    }
}
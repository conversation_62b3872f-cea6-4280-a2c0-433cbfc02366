package com.anonymous.videodownloaderapp.utils

import android.content.Context
import com.anonymous.videodownloaderapp.data.DownloadItem
import com.anonymous.videodownloaderapp.services.EnhancedDownloadService
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

object EnhancedDownloadUtil {
    
    private var enhancedService: EnhancedDownloadService? = null
    private val _downloadStates = MutableStateFlow<Map<String, DownloadState>>(emptyMap())
    val downloadStates: StateFlow<Map<String, DownloadState>> = _downloadStates.asStateFlow()
    
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    data class DownloadState(
        val downloadItem: DownloadItem,
        val progress: Int,
        val bytesDownloaded: Long,
        val totalBytes: Long,
        val status: DownloadItem.Status,
        val speed: Long = 0L
    )
    
    fun initialize(context: Context) {
        enhancedService = EnhancedDownloadService(context.applicationContext)
    }
    
    fun startEnhancedDownload(downloadItem: DownloadItem) {
        enhancedService?.let { service ->
            coroutineScope.launch {
                service.startDownload(downloadItem)
            }
        }
    }
    
    fun pauseDownload(downloadId: String) {
        enhancedService?.pauseDownload(downloadId)
    }
    
    fun resumeDownload(downloadId: String) {
        enhancedService?.resumeDownload(downloadId)
    }
    
    fun cancelDownload(downloadId: String) {
        enhancedService?.cancelDownload(downloadId)
    }
    
    fun shutdown() {
        enhancedService?.shutdown()
        coroutineScope.cancel()
    }
    
    // Utility functions for integration
    fun isEnhancedServiceAvailable(): Boolean = enhancedService != null
    
    fun getDownloadSpeed(downloadId: String): Long {
        return _downloadStates.value[downloadId]?.speed ?: 0L
    }
    
    fun getEstimatedTimeLeft(downloadId: String): Long {
        val state = _downloadStates.value[downloadId] ?: return 0L
        return if (state.speed > 0) {
            (state.totalBytes - state.bytesDownloaded) / state.speed
        } else 0L
    }
}
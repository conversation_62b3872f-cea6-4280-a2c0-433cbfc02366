package com.anonymous.videodownloaderapp.services

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.webkit.JavascriptInterface
import android.webkit.WebView
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong
import kotlin.math.min

/**
 * Bridge JavaScript Otimizado
 * Comunicação Bidirecional Eficiente com Debouncing Inteligente
 */
class OptimizedJavaScriptBridge(private val context: Context) {

    data class BridgeMessage(
        val id: String,
        val type: MessageType,
        val payload: String,
        val timestamp: Long,
        val priority: Priority = Priority.NORMAL
    )

    data class CandidateData(
        val url: String,
        val quality: String,
        val format: String,
        val title: String,
        val score: Float,
        val width: Int?,
        val height: Int?,
        val duration: Double?,
        val isLive: Boolean
    )

    data class DebounceState(
        val currentInterval: Long,
        val activityLevel: ActivityLevel,
        val lastExecutionTime: Long,
        val pendingOperations: Int
    )

    enum class MessageType {
        VIDEO_CANDIDATES,
        QUALITY_UPDATE,
        PERFORMANCE_METRICS,
        ERROR_REPORT,
        HEARTBEAT,
        CONFIGURATION
    }

    enum class Priority {
        LOW, NORMAL, HIGH, CRITICAL
    }

    enum class ActivityLevel {
        LOW, MODERATE, HIGH, VERY_HIGH
    }

    companion object {
        private const val INITIAL_DEBOUNCE_MS = 300L
        private const val HIGH_ACTIVITY_DEBOUNCE_MS = 500L
        private const val MAX_DEBOUNCE_MS = 1000L
        private const val OPERATION_TIMEOUT_MS = 2000L
        private const val MEMORY_CLEANUP_INTERVAL_MS = 30000L // 30 segundos
        private const val MAX_PENDING_OPERATIONS = 10
        private const val HIGH_ACTIVITY_THRESHOLD = 5 // operações por segundo
        
        private const val JS_INTERFACE_NAME = "OptimizedVideoDetector"
        
        @Volatile
        private var instance: OptimizedJavaScriptBridge? = null
        
        fun getInstance(context: Context): OptimizedJavaScriptBridge {
            return instance ?: synchronized(this) {
                instance ?: OptimizedJavaScriptBridge(context.applicationContext).also {
                    instance = it
                }
            }
        }
    }

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private val backgroundScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private val mainHandler = Handler(Looper.getMainLooper())
    
    // Estado do debouncing
    private val _debounceState = MutableStateFlow(
        DebounceState(
            currentInterval = INITIAL_DEBOUNCE_MS,
            activityLevel = ActivityLevel.LOW,
            lastExecutionTime = 0,
            pendingOperations = 0
        )
    )
    val debounceState: StateFlow<DebounceState> = _debounceState
    
    // Controle de operações
    private val operationQueue = Channel<BridgeMessage>(Channel.UNLIMITED)
    private val pendingOperations = ConcurrentHashMap<String, Job>()
    private val operationCounter = AtomicLong(0)
    private val isProcessing = AtomicBoolean(false)
    
    // Callbacks e listeners
    private val candidateCallbacks = mutableSetOf<(List<CandidateData>) -> Unit>()
    private val errorCallbacks = mutableSetOf<(String, Throwable?) -> Unit>()
    
    // Métricas de performance
    private var totalOperations = 0L
    private var successfulOperations = 0L
    private var timeoutOperations = 0L
    private var lastActivityCheck = System.currentTimeMillis()
    private var operationsInLastSecond = 0
    
    // WebView reference
    private var currentWebView: WebView? = null

    init {
        startMessageProcessor()
        startPeriodicCleanup()
        startActivityMonitoring()
    }

    /**
     * Configurar WebView com bridge otimizado
     */
    fun setupWebView(webView: WebView) {
        currentWebView = webView
        
        // Configurações otimizadas
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = false // Desabilitar para performance
            allowFileAccess = false
            allowContentAccess = false
            setSupportZoom(false)
            builtInZoomControls = false
            displayZoomControls = false
        }
        
        // Adicionar interface JavaScript
        webView.addJavascriptInterface(JavaScriptInterface(), JS_INTERFACE_NAME)
    }

    /**
     * Executar detecção de vídeo com debouncing inteligente
     */
    fun detectVideosWithDebouncing(
        webView: WebView,
        pageUrl: String,
        priority: Priority = Priority.NORMAL
    ) {
        val messageId = "detect_${operationCounter.incrementAndGet()}"
        
        val message = BridgeMessage(
            id = messageId,
            type = MessageType.VIDEO_CANDIDATES,
            payload = pageUrl,
            timestamp = System.currentTimeMillis(),
            priority = priority
        )
        
        // Enviar para fila com debouncing
        scope.launch {
            operationQueue.send(message)
        }
    }

    /**
     * Executar JavaScript com timeout
     */
    private suspend fun executeJavaScriptWithTimeout(
        webView: WebView,
        script: String,
        timeoutMs: Long = OPERATION_TIMEOUT_MS
    ): String? = withContext(Dispatchers.Main) {
        
        return@withContext withTimeoutOrNull(timeoutMs) {
            suspendCancellableCoroutine<String?> { continuation ->
                try {
                    webView.evaluateJavascript(script) { result ->
                        continuation.resume(result, null)
                    }
                } catch (e: Exception) {
                    continuation.resume(null, null)
                }
            }
        }
    }

    /**
     * Processar fila de mensagens com debouncing
     */
    private fun startMessageProcessor() {
        scope.launch {
            for (message in operationQueue) {
                processMessageWithDebouncing(message)
            }
        }
    }

    /**
     * Processar mensagem individual com debouncing
     */
    private suspend fun processMessageWithDebouncing(message: BridgeMessage) {
        val currentState = _debounceState.value
        
        // Verificar se deve aguardar debounce
        val timeSinceLastExecution = System.currentTimeMillis() - currentState.lastExecutionTime
        val debounceInterval = calculateDebounceInterval()
        
        if (timeSinceLastExecution < debounceInterval && message.priority != Priority.CRITICAL) {
            delay(debounceInterval - timeSinceLastExecution)
        }
        
        // Processar mensagem
        processMessage(message)
        
        // Atualizar estado
        updateDebounceState()
    }

    /**
     * Processar mensagem específica
     */
    private suspend fun processMessage(message: BridgeMessage) {
        if (isProcessing.get() && message.priority != Priority.CRITICAL) {
            // Evitar sobrecarga
            return
        }
        
        isProcessing.set(true)
        totalOperations++
        
        try {
            when (message.type) {
                MessageType.VIDEO_CANDIDATES -> {
                    processVideoCandidatesDetection(message)
                }
                MessageType.QUALITY_UPDATE -> {
                    processQualityUpdate(message)
                }
                MessageType.PERFORMANCE_METRICS -> {
                    processPerformanceMetrics(message)
                }
                else -> {
                    // Outros tipos de mensagem
                }
            }
            
            successfulOperations++
            
        } catch (e: Exception) {
            notifyError("Erro ao processar mensagem ${message.type}", e)
        } finally {
            isProcessing.set(false)
        }
    }

    /**
     * Processar detecção de candidatos de vídeo
     */
    private suspend fun processVideoCandidatesDetection(message: BridgeMessage) {
        val webView = currentWebView ?: return
        
        // Script otimizado para detecção
        val script = """
            (function() {
                try {
                    // Configuração otimizada
                    var config = {
                        maxCandidates: 20,
                        minScore: 5,
                        timeout: 1500,
                        enablePerformanceAPI: true,
                        enableYouTubeSpecific: true
                    };
                    
                    var candidates = [];
                    var startTime = performance.now();
                    
                    // 1. HTML5 video elements (prioridade alta)
                    document.querySelectorAll('video').forEach(function(video) {
                        if (!video.src && !video.currentSrc) return;
                        
                        var rect = video.getBoundingClientRect();
                        var isVisible = rect.width > 0 && rect.height > 0 && 
                                       rect.top < window.innerHeight && rect.bottom > 0;
                        
                        if (isVisible) {
                            var quality = 'Auto';
                            if (video.videoHeight && video.videoWidth) {
                                quality = video.videoHeight + 'p';
                            }
                            
                            candidates.push({
                                url: video.currentSrc || video.src,
                                quality: quality,
                                format: 'mp4',
                                title: video.title || document.title || 'HTML5 Video',
                                score: 15,
                                width: video.videoWidth || null,
                                height: video.videoHeight || null,
                                duration: video.duration || null,
                                isLive: video.duration === Infinity
                            });
                        }
                    });
                    
                    // 2. Performance API (otimizado)
                    if (config.enablePerformanceAPI && window.performance) {
                        var entries = performance.getEntriesByType('resource')
                            .filter(function(entry) {
                                var url = entry.name.toLowerCase();
                                return (url.includes('.mp4') || url.includes('.webm') || 
                                       url.includes('.m3u8') || url.includes('.mpd') ||
                                       url.includes('googlevideo') || url.includes('videoplayback')) &&
                                       entry.transferSize > 100000; // > 100KB
                            })
                            .slice(0, 10); // Limitar para performance
                        
                        entries.forEach(function(entry) {
                            var url = entry.name;
                            var quality = 'Auto';
                            var format = 'mp4';
                            
                            // YouTube específico
                            if (url.includes('googlevideo') || url.includes('videoplayback')) {
                                var itagMatch = url.match(/itag=(\d+)/);
                                if (itagMatch) {
                                    var itag = parseInt(itagMatch[1]);
                                    if (itag >= 137) quality = '1080p';
                                    else if (itag >= 136) quality = '720p';
                                    else if (itag >= 135) quality = '480p';
                                    else quality = '360p';
                                }
                            }
                            
                            // Formato
                            if (url.includes('.webm')) format = 'webm';
                            else if (url.includes('.m3u8')) format = 'hls';
                            else if (url.includes('.mpd')) format = 'dash';
                            
                            candidates.push({
                                url: url,
                                quality: quality,
                                format: format,
                                title: 'Detected Video',
                                score: 10,
                                width: null,
                                height: null,
                                duration: null,
                                isLive: false
                            });
                        });
                    }
                    
                    // 3. YouTube específico (ytInitialPlayerResponse)
                    if (config.enableYouTubeSpecific && window.ytInitialPlayerResponse) {
                        try {
                            var playerResponse = window.ytInitialPlayerResponse;
                            if (playerResponse.streamingData) {
                                var formats = playerResponse.streamingData.formats || [];
                                var adaptiveFormats = playerResponse.streamingData.adaptiveFormats || [];
                                
                                formats.concat(adaptiveFormats).forEach(function(format) {
                                    if (format.url) {
                                        candidates.push({
                                            url: format.url,
                                            quality: format.qualityLabel || format.quality || 'Auto',
                                            format: 'mp4',
                                            title: 'YouTube Video',
                                            score: 20,
                                            width: format.width || null,
                                            height: format.height || null,
                                            duration: null,
                                            isLive: false
                                        });
                                    }
                                });
                            }
                        } catch (e) {}
                    }
                    
                    // Filtrar e deduplificar
                    var uniqueCandidates = [];
                    var seenUrls = new Set();
                    
                    candidates
                        .filter(function(c) { return c.score >= config.minScore; })
                        .sort(function(a, b) { return b.score - a.score; })
                        .slice(0, config.maxCandidates)
                        .forEach(function(candidate) {
                            if (!seenUrls.has(candidate.url)) {
                                seenUrls.add(candidate.url);
                                uniqueCandidates.push(candidate);
                            }
                        });
                    
                    var processingTime = performance.now() - startTime;
                    
                    return JSON.stringify({
                        candidates: uniqueCandidates,
                        metadata: {
                            processingTime: processingTime,
                            totalFound: candidates.length,
                            uniqueCount: uniqueCandidates.length,
                            timestamp: Date.now()
                        }
                    });
                    
                } catch (e) {
                    return JSON.stringify({
                        candidates: [],
                        error: e.message
                    });
                }
            })();
        """.trimIndent()
        
        // Executar com timeout
        backgroundScope.launch {
            try {
                val result = executeJavaScriptWithTimeout(webView, script)
                
                if (result != null && result != "null") {
                    processDetectionResult(result)
                }
                
            } catch (e: Exception) {
                timeoutOperations++
                notifyError("Timeout na detecção JavaScript", e)
            }
        }
    }

    /**
     * Processar resultado da detecção
     */
    private suspend fun processDetectionResult(jsonResult: String) = withContext(Dispatchers.IO) {
        try {
            val cleanResult = jsonResult.removeSurrounding("\"")
                .replace("\\\"", "\"")
                .replace("\\\\", "\\")
            
            val resultObj = JSONObject(cleanResult)
            val candidatesArray = resultObj.getJSONArray("candidates")
            
            val candidates = mutableListOf<CandidateData>()
            
            for (i in 0 until candidatesArray.length()) {
                val candidate = candidatesArray.getJSONObject(i)
                
                candidates.add(
                    CandidateData(
                        url = candidate.getString("url"),
                        quality = candidate.getString("quality"),
                        format = candidate.getString("format"),
                        title = candidate.getString("title"),
                        score = candidate.getDouble("score").toFloat(),
                        width = candidate.optInt("width").takeIf { it > 0 },
                        height = candidate.optInt("height").takeIf { it > 0 },
                        duration = candidate.optDouble("duration").takeIf { !it.isNaN() },
                        isLive = candidate.optBoolean("isLive", false)
                    )
                )
            }
            
            // Notificar callbacks na thread principal
            withContext(Dispatchers.Main) {
                candidateCallbacks.forEach { callback ->
                    try {
                        callback(candidates)
                    } catch (e: Exception) {
                        // Ignorar erros em callbacks
                    }
                }
            }
            
        } catch (e: Exception) {
            notifyError("Erro ao processar resultado da detecção", e)
        }
    }

    /**
     * Processar atualização de qualidade
     */
    private suspend fun processQualityUpdate(message: BridgeMessage) {
        // Implementar lógica de atualização de qualidade
    }

    /**
     * Processar métricas de performance
     */
    private suspend fun processPerformanceMetrics(message: BridgeMessage) {
        // Implementar coleta de métricas
    }

    /**
     * Calcular intervalo de debounce baseado na atividade
     */
    private fun calculateDebounceInterval(): Long {
        val currentState = _debounceState.value
        
        return when (currentState.activityLevel) {
            ActivityLevel.LOW -> INITIAL_DEBOUNCE_MS
            ActivityLevel.MODERATE -> INITIAL_DEBOUNCE_MS + 100
            ActivityLevel.HIGH -> HIGH_ACTIVITY_DEBOUNCE_MS
            ActivityLevel.VERY_HIGH -> min(MAX_DEBOUNCE_MS, HIGH_ACTIVITY_DEBOUNCE_MS + 200)
        }
    }

    /**
     * Atualizar estado do debouncing
     */
    private fun updateDebounceState() {
        val now = System.currentTimeMillis()
        val currentState = _debounceState.value
        
        _debounceState.value = currentState.copy(
            lastExecutionTime = now,
            pendingOperations = pendingOperations.size
        )
    }

    /**
     * Monitorar atividade para ajustar debouncing
     */
    private fun startActivityMonitoring() {
        scope.launch {
            while (true) {
                delay(1000) // Verificar a cada segundo
                
                val now = System.currentTimeMillis()
                val timeSinceLastCheck = now - lastActivityCheck
                
                if (timeSinceLastCheck >= 1000) {
                    val activityLevel = when {
                        operationsInLastSecond >= HIGH_ACTIVITY_THRESHOLD * 2 -> ActivityLevel.VERY_HIGH
                        operationsInLastSecond >= HIGH_ACTIVITY_THRESHOLD -> ActivityLevel.HIGH
                        operationsInLastSecond >= 2 -> ActivityLevel.MODERATE
                        else -> ActivityLevel.LOW
                    }
                    
                    _debounceState.value = _debounceState.value.copy(
                        activityLevel = activityLevel,
                        currentInterval = calculateDebounceInterval()
                    )
                    
                    operationsInLastSecond = 0
                    lastActivityCheck = now
                }
            }
        }
    }

    /**
     * Limpeza periódica de memória
     */
    private fun startPeriodicCleanup() {
        scope.launch {
            while (true) {
                delay(MEMORY_CLEANUP_INTERVAL_MS)
                
                // Limpar operações pendentes expiradas
                val now = System.currentTimeMillis()
                val expiredOperations = pendingOperations.filter { (_, job) ->
                    !job.isActive
                }
                
                expiredOperations.forEach { (id, _) ->
                    pendingOperations.remove(id)
                }
                
                // Forçar garbage collection se necessário
                if (pendingOperations.size > MAX_PENDING_OPERATIONS) {
                    System.gc()
                }
            }
        }
    }

    /**
     * Interface JavaScript para comunicação bidirecional
     */
    inner class JavaScriptInterface {
        
        @JavascriptInterface
        fun reportCandidates(jsonData: String) {
            operationsInLastSecond++
            
            scope.launch {
                processDetectionResult(jsonData)
            }
        }
        
        @JavascriptInterface
        fun reportError(error: String) {
            notifyError("JavaScript Error: $error", null)
        }
        
        @JavascriptInterface
        fun reportMetrics(metricsJson: String) {
            // Processar métricas de performance do JavaScript
        }
    }

    /**
     * Adicionar callback para candidatos
     */
    fun addCandidateCallback(callback: (List<CandidateData>) -> Unit) {
        candidateCallbacks.add(callback)
    }

    /**
     * Remover callback para candidatos
     */
    fun removeCandidateCallback(callback: (List<CandidateData>) -> Unit) {
        candidateCallbacks.remove(callback)
    }

    /**
     * Adicionar callback para erros
     */
    fun addErrorCallback(callback: (String, Throwable?) -> Unit) {
        errorCallbacks.add(callback)
    }

    /**
     * Notificar erro
     */
    private fun notifyError(message: String, throwable: Throwable?) {
        errorCallbacks.forEach { callback ->
            try {
                callback(message, throwable)
            } catch (e: Exception) {
                // Ignorar erros em callbacks
            }
        }
    }

    /**
     * Obter estatísticas de performance
     */
    fun getPerformanceStats(): Map<String, Any> {
        val currentState = _debounceState.value
        
        return mapOf(
            "totalOperations" to totalOperations,
            "successfulOperations" to successfulOperations,
            "timeoutOperations" to timeoutOperations,
            "successRate" to if (totalOperations > 0) (successfulOperations.toDouble() / totalOperations) else 0.0,
            "currentDebounceInterval" to currentState.currentInterval,
            "activityLevel" to currentState.activityLevel.name,
            "pendingOperations" to currentState.pendingOperations,
            "operationsPerSecond" to operationsInLastSecond
        )
    }

    /**
     * Limpeza de recursos
     */
    fun cleanup() {
        scope.cancel()
        backgroundScope.cancel()
        pendingOperations.values.forEach { it.cancel() }
        pendingOperations.clear()
        candidateCallbacks.clear()
        errorCallbacks.clear()
        currentWebView = null
        instance = null
    }
}
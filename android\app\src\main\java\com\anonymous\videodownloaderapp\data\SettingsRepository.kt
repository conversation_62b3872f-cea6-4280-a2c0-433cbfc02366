package com.anonymous.videodownloaderapp.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")

class SettingsRepository(context: Context) {

    private val dataStore = context.dataStore

    object Keys {
        val isAdBlockingEnabled = booleanPreferencesKey("ad_blocking_enabled")
        val isTrackingProtectionEnabled = booleanPreferencesKey("tracking_protection_enabled")
        val selectedQuality = stringPreferencesKey("selected_quality")
        val audioMixingMode = stringPreferencesKey("audio_mixing_mode")
    }

    val isAdBlockingEnabled: Flow<Boolean> = dataStore.data.map {
        it[Keys.isAdBlockingEnabled] ?: false
    }

    suspend fun setAdBlockingEnabled(isEnabled: Boolean) {
        dataStore.edit {
            it[Keys.isAdBlockingEnabled] = isEnabled
        }
    }

    val isTrackingProtectionEnabled: Flow<Boolean> = dataStore.data.map {
        it[Keys.isTrackingProtectionEnabled] ?: false
    }

    suspend fun setTrackingProtectionEnabled(isEnabled: Boolean) {
        dataStore.edit {
            it[Keys.isTrackingProtectionEnabled] = isEnabled
        }
    }

    val selectedQuality: Flow<String> = dataStore.data.map {
        it[Keys.selectedQuality] ?: "auto"
    }

    suspend fun setSelectedQuality(quality: String) {
        dataStore.edit {
            it[Keys.selectedQuality] = quality
        }
    }

    val audioMixingMode: Flow<String> = dataStore.data.map {
        it[Keys.audioMixingMode] ?: "mix"
    }

    suspend fun setAudioMixingMode(mode: String) {
        dataStore.edit {
            it[Keys.audioMixingMode] = mode
        }
    }
}

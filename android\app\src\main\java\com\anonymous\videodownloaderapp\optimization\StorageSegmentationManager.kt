package com.anonymous.videodownloaderapp.optimization

import android.content.Context
import android.os.Build
import android.os.Environment
import android.os.StatFs
import android.util.Log
import com.anonymous.videodownloaderapp.data.DownloadItem
import java.io.File
import java.util.concurrent.ConcurrentHashMap

class StorageSegmentationManager(private val context: Context) {
    
    private val downloadSegments = ConcurrentHashMap<String, DownloadSegment>()
    
    data class DownloadSegment(
        val segmentId: String,
        val originalFile: File,
        val segmentFiles: List<File>,
        val totalSize: Long,
        val segmentSize: Long,
        val isComplete: Boolean = false
    )
    
    // Storage locations
    private val internalStorage = context.filesDir
    private val externalStorage = context.getExternalFilesDir(null)
    private val cacheStorage = context.cacheDir
    
    // Choose optimal storage location based on file size and available space
    fun chooseOptimalStorageLocation(fileSize: Long): File {
        val locations = listOf(
            externalStorage to getAvailableSpace(externalStorage),
            internalStorage to getAvailableSpace(internalStorage),
            cacheStorage to getAvailableSpace(cacheStorage)
        ).filter { it.second > fileSize * 1.2 } // 20% buffer
        
        return locations.maxByOrNull { it.second }?.first ?: internalStorage
    }
    
    // Segment large downloads
    fun segmentDownload(downloadItem: DownloadItem, segmentSize: Long = 10 * 1024 * 1024): DownloadSegment {
        val segmentCount = (downloadItem.totalBytes / segmentSize) + 1
        val segmentFiles = mutableListOf<File>()
        
        val baseDir = chooseOptimalStorageLocation(downloadItem.totalBytes)
        val downloadDir = File(baseDir, "downloads/${downloadItem.id}")
        downloadDir.mkdirs()
        
        for (i in 0 until segmentCount) {
            val segmentFile = File(downloadDir, "segment_${i}.tmp")
            segmentFiles.add(segmentFile)
        }
        
        val segment = DownloadSegment(
            segmentId = downloadItem.id.toString(),
            originalFile = File(downloadDir, downloadItem.title),
            segmentFiles = segmentFiles,
            totalSize = downloadItem.totalBytes,
            segmentSize = segmentSize
        )
        
        downloadSegments[downloadItem.id.toString()] = segment
        return segment
    }
    
    // Merge segments into final file
    fun mergeSegments(segment: DownloadSegment): File {
        val finalFile = segment.originalFile
        
        finalFile.outputStream().use { output ->
            segment.segmentFiles.forEach { segmentFile ->
                if (segmentFile.exists()) {
                    segmentFile.inputStream().use { input ->
                        input.copyTo(output)
                    }
                    segmentFile.delete()
                }
            }
        }
        
        // Update segment as complete
        val updatedSegment = segment.copy(isComplete = true)
        downloadSegments[segment.segmentId] = updatedSegment
        
        return finalFile
    }
    
    // Get available space for a directory
    fun getAvailableSpace(directory: File?): Long {
        if (directory == null || !directory.exists()) return 0
        
        return try {
            val stat = StatFs(directory.path)
            stat.availableBlocksLong * stat.blockSizeLong
        } catch (e: Exception) {
            0
        }
    }
    
    // Clean up old segments
    fun cleanupOldSegments(maxAgeHours: Int = 24) {
        val maxAge = maxAgeHours * 60 * 60 * 1000L
        val currentTime = System.currentTimeMillis()
        
        downloadSegments.values.forEach { segment ->
            segment.segmentFiles.forEach { file ->
                if (file.exists() && currentTime - file.lastModified() > maxAge) {
                    file.delete()
                }
            }
        }
    }
    
    // Get storage statistics
    fun getStorageStats(): StorageStats {
        val internalAvailable = getAvailableSpace(internalStorage)
        val externalAvailable = getAvailableSpace(externalStorage)
        val cacheAvailable = getAvailableSpace(cacheStorage)
        
        return StorageStats(
            internalAvailable = internalAvailable,
            externalAvailable = externalAvailable,
            cacheAvailable = cacheAvailable,
            totalSegments = downloadSegments.size,
            activeDownloads = downloadSegments.count { !it.value.isComplete }
        )
    }
    
    data class StorageStats(
        val internalAvailable: Long,
        val externalAvailable: Long,
        val cacheAvailable: Long,
        val totalSegments: Int,
        val activeDownloads: Int
    )
}
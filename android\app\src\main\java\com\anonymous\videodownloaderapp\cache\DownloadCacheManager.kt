package com.anonymous.videodownloaderapp.cache

import android.content.Context
import android.util.LruCache
import com.anonymous.videodownloaderapp.data.DownloadItem
import kotlinx.coroutines.*
import java.io.File
import java.util.concurrent.ConcurrentHashMap

class DownloadCacheManager(private val context: Context) {
    
    // Memory cache for URL metadata
    private val urlCache = LruCache<String, CachedUrlData>(50)
    
    // Memory cache for thumbnails
    private val thumbnailCache = LruCache<String, File>(100)
    
    // Disk cache for persistent data
    private val diskCacheDir = File(context.cacheDir, "download_cache")
    
    // Cache for download progress
    private val progressCache = ConcurrentHashMap<String, DownloadProgress>()
    
    data class CachedUrlData(
        val resolvedUrl: String,
        val fileName: String,
        val fileSize: Long,
        val mimeType: String,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    data class DownloadProgress(
        val bytesDownloaded: Long,
        val totalBytes: Long,
        val lastUpdated: Long = System.currentTimeMillis()
    )
    
    init {
        if (!diskCacheDir.exists()) {
            diskCacheDir.mkdirs()
        }
    }
    
    // Cache URL resolution results
    fun cacheUrlData(originalUrl: String, resolvedUrl: String, fileName: String, fileSize: Long, mimeType: String) {
        val data = CachedUrlData(resolvedUrl, fileName, fileSize, mimeType)
        urlCache.put(originalUrl, data)
        
        // Persist to disk
        CoroutineScope(Dispatchers.IO).launch {
            saveToDisk(originalUrl, data)
        }
    }
    
    // Get cached URL data
    fun getCachedUrlData(originalUrl: String): CachedUrlData? {
        return urlCache.get(originalUrl) ?: loadFromDisk(originalUrl)
    }
    
    // Cache thumbnail
    fun cacheThumbnail(videoId: String, thumbnailFile: File) {
        thumbnailCache.put(videoId, thumbnailFile)
    }
    
    // Get cached thumbnail
    fun getCachedThumbnail(videoId: String): File? {
        return thumbnailCache.get(videoId)
    }
    
    // Cache download progress
    fun cacheProgress(downloadId: String, bytesDownloaded: Long, totalBytes: Long) {
        progressCache[downloadId] = DownloadProgress(bytesDownloaded, totalBytes)
    }
    
    // Get cached progress
    fun getCachedProgress(downloadId: String): DownloadProgress? {
        return progressCache[downloadId]
    }
    
    // Clear expired cache entries
    fun clearExpiredCache(maxAgeHours: Int = 24) {
        val maxAge = maxAgeHours * 60 * 60 * 1000L
        val currentTime = System.currentTimeMillis()
        
        // Clear memory cache
        urlCache.evictAll()
        thumbnailCache.evictAll()
        
        // Clear disk cache
        CoroutineScope(Dispatchers.IO).launch {
            diskCacheDir.listFiles()?.forEach { file ->
                if (currentTime - file.lastModified() > maxAge) {
                    file.delete()
                }
            }
        }
    }
    
    // Clear cache by size
    fun clearCacheBySize(maxSizeMB: Int = 100) {
        val maxSizeBytes = maxSizeMB * 1024 * 1024L
        
        CoroutineScope(Dispatchers.IO).launch {
            val files = diskCacheDir.listFiles() ?: return@launch
            val totalSize = files.sumOf { it.length() }
            
            if (totalSize > maxSizeBytes) {
                // Sort by last modified and delete oldest
                files.sortedBy { it.lastModified() }
                    .takeWhile { totalSize - it.length() > maxSizeBytes }
                    .forEach { it.delete() }
            }
        }
    }
    
    private fun saveToDisk(key: String, data: CachedUrlData) {
        try {
            val file = File(diskCacheDir, "url_${key.hashCode()}")
            file.writeText("${data.resolvedUrl}|${data.fileName}|${data.fileSize}|${data.mimeType}")
        } catch (e: Exception) {
            // Silent fail for cache
        }
    }
    
    private fun loadFromDisk(key: String): CachedUrlData? {
        return try {
            val file = File(diskCacheDir, "url_${key.hashCode()}")
            if (file.exists()) {
                val parts = file.readText().split("|")
                if (parts.size == 4) {
                    CachedUrlData(parts[0], parts[1], parts[2].toLong(), parts[3])
                } else null
            } else null
        } catch (e: Exception) {
            null
        }
    }
    
    // Get cache statistics
    fun getCacheStats(): CacheStats {
        return CacheStats(
            urlCacheSize = urlCache.size(),
            thumbnailCacheSize = thumbnailCache.size(),
            diskCacheSize = diskCacheDir.listFiles()?.size ?: 0,
            diskCacheTotalSize = diskCacheDir.listFiles()?.sumOf { it.length() } ?: 0L
        )
    }
    
    data class CacheStats(
        val urlCacheSize: Int,
        val thumbnailCacheSize: Int,
        val diskCacheSize: Int,
        val diskCacheTotalSize: Long
    )
}
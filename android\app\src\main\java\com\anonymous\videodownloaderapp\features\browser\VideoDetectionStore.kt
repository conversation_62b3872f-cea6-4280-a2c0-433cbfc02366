package com.anonymous.videodownloaderapp.features.browser

import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * In-memory store to keep detected video qualities per tab.
 * Thread-safe and lightweight. Not persisted across app restarts.
 *
 * Data model:
 * - Each entry is a pair of (qualityLabel, url).
 * - We keep insertion order and also de-duplicate by URL.
 * - Limit the list size per tab to avoid unbounded memory growth.
 */
object VideoDetectionStore {

    data class QualityEntry(
        val quality: String,
        val url: String
    )

    private const val MAX_PER_TAB = 100

    // Map<tabId, MutableList<QualityEntry>>
    private val byTab: MutableMap<String, MutableList<QualityEntry>> = ConcurrentHashMap()

    // Simple listeners keyed by a token so UI can observe specific tab updates
    interface Listener {
        fun onTabUpdated(tabId: String, entries: List<QualityEntry>)
    }
    private val listeners: MutableList<Listener> = CopyOnWriteArrayList()

    fun addListener(listener: Listener) {
        listeners.add(listener)
    }

    fun removeListener(listener: Listener) {
        listeners.remove(listener)
    }

    fun clearTab(tabId: String) {
        byTab.remove(tabId)
        notifyTab(tabId)
    }

    fun get(tabId: String): List<QualityEntry> {
        return byTab[tabId]?.toList().orEmpty()
    }

    /**
     * Merge a set of quality::url pairs into the store for a given tab.
     * Ensures uniqueness by URL, preferring the newest label for the same URL.
     */
    fun upsertPairs(tabId: String, pairs: List<Pair<String, String>>) {
        if (pairs.isEmpty()) return
        val list = byTab.getOrPut(tabId) { CopyOnWriteArrayList() }

        // Build quick index by URL for de-duplication
        val byUrl = list.associateBy { it.url }.toMutableMap()

        var changed = false
        for ((q, u) in pairs) {
            if (u.isBlank()) continue
            val existing = byUrl[u]
            if (existing == null) {
                val entry = QualityEntry(quality = q.ifBlank { "Auto" }, url = u)
                list.add(entry)
                byUrl[u] = entry
                changed = true
            } else {
                // Update quality if new one seems better/more specific (e.g., "1080p" over "Auto")
                val newQ = q.ifBlank { "Auto" }
                if (shouldPrefer(newQ, existing.quality)) {
                    // Replace in list as well
                    val idx = list.indexOfFirst { it.url == u }
                    if (idx >= 0) {
                        list[idx] = QualityEntry(quality = newQ, url = u)
                        byUrl[u] = list[idx]
                        changed = true
                    }
                }
            }
        }

        // Enforce max size, keep most recent items
        if (list.size > MAX_PER_TAB) {
            val overflow = list.size - MAX_PER_TAB
            repeat(overflow) {
                if (list.isNotEmpty()) list.removeAt(0)
            }
            changed = true
        }

        if (changed) notifyTab(tabId)
    }

    private fun shouldPrefer(newQ: String, oldQ: String): Boolean {
        // Prefer labeled numeric qualities over "Auto"
        if (oldQ.equals("Auto", ignoreCase = true) && !newQ.equals("Auto", ignoreCase = true)) return true
        // Prefer higher numeric p value
        val nNew = extractP(newQ)
        val nOld = extractP(oldQ)
        if (nNew != null && nOld != null) return nNew > nOld
        // Otherwise, keep existing
        return false
    }

    private fun extractP(label: String): Int? {
        val m = Regex("(\\d{3,4})p", RegexOption.IGNORE_CASE).find(label)
        return m?.groupValues?.getOrNull(1)?.toIntOrNull()
    }

    private fun notifyTab(tabId: String) {
        val snapshot = get(tabId)
        listeners.forEach { l ->
            try { l.onTabUpdated(tabId, snapshot) } catch (_: Throwable) { }
        }
    }
}
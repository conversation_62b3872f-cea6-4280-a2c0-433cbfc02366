package com.anonymous.videodownloaderapp.features.downloads

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.anonymous.videodownloaderapp.data.DownloadItem
import com.anonymous.videodownloaderapp.data.DownloadRepository
import com.anonymous.videodownloaderapp.services.DownloadWorker
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DownloadsScreen(navController: NavHostController) {
    val context = LocalContext.current
    val repo = remember { DownloadRepository.get(context) }
    val scope = rememberCoroutineScope()

    var url by remember { mutableStateOf("") }
    var selectedTab by remember { mutableStateOf(0) }
    var sortBy by remember { mutableStateOf("Date") }
    var filterStatus by remember { mutableStateOf("All") }
    var showStatistics by remember { mutableStateOf(false) }
    var showBatchActions by remember { mutableStateOf(false) }
    var selectedItems by remember { mutableStateOf(setOf<Long>()) }
    var showQualityDialog by remember { mutableStateOf(false) }
    var selectedQuality by remember { mutableStateOf("720p") }
    var pendingDownloadUrl by remember { mutableStateOf("") }
    
    val downloads by repo.observeAll().collectAsState(initial = emptyList())
    
    // Filtrar e ordenar downloads
    val filteredDownloads = downloads.filter { item ->
        when (filterStatus) {
            "All" -> true
            "Running" -> item.status == DownloadItem.Status.Running
            "Completed" -> item.status == DownloadItem.Status.Completed
            "Failed" -> item.status == DownloadItem.Status.Failed
            "Paused" -> item.status == DownloadItem.Status.Paused
            else -> true
        }
    }.sortedWith { a, b ->
        when (sortBy) {
            "Date" -> b.createdAt.compareTo(a.createdAt)
            "Name" -> (a.title ?: a.url).compareTo(b.title ?: b.url, ignoreCase = true)
            "Size" -> b.totalBytes.compareTo(a.totalBytes)
            "Progress" -> b.progress.compareTo(a.progress)
            else -> 0
        }
    }
    
    val statistics = remember(downloads) {
        val total = downloads.size
        val completed = downloads.count { it.status == DownloadItem.Status.Completed }
        val running = downloads.count { it.status == DownloadItem.Status.Running }
        val failed = downloads.count { it.status == DownloadItem.Status.Failed }
        val totalSize = downloads.sumOf { it.totalBytes }
        val downloadedSize = downloads.sumOf { it.bytesDownloaded }
        
        mapOf(
            "total" to total,
            "completed" to completed,
            "running" to running,
            "failed" to failed,
            "totalSize" to totalSize,
            "downloadedSize" to downloadedSize
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Downloads", fontWeight = FontWeight.Bold) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.Default.Close, "Fechar")
                    }
                },
                actions = {
                    IconButton(onClick = { showStatistics = !showStatistics }) {
                        Icon(Icons.Default.Analytics, "Estatísticas")
                }
                IconButton(onClick = { showBatchActions = !showBatchActions }) {
                    Icon(Icons.Default.SelectAll, "Ações em lote")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        }
    ) { inner ->
        Column(
            modifier = Modifier
                .padding(inner)
                .fillMaxSize()
        ) {
            // Estatísticas
            if (showStatistics) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "Estatísticas",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(Modifier.height(8.dp))
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            StatisticItem("Total", statistics["total"].toString())
                            StatisticItem("Concluídos", statistics["completed"].toString())
                            StatisticItem("Em andamento", statistics["running"].toString())
                            StatisticItem("Falhas", statistics["failed"].toString())
                        }
                        Spacer(Modifier.height(8.dp))
                        Text(
                            "Tamanho total: ${formatBytes(statistics["totalSize"] as Long)}",
                            fontSize = 12.sp
                        )
                        Text(
                            "Baixados: ${formatBytes(statistics["downloadedSize"] as Long)}",
                            fontSize = 12.sp
                        )
                    }
                }
            }

            // Barra de ferramentas - Nova URL
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        "Novo Download",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Spacer(Modifier.height(8.dp))
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        // Rounded URL input with higher radius and tonal fill
                        OutlinedTextField(
                            value = url,
                            onValueChange = { url = it },
                            modifier = Modifier
                                .weight(1f),
                            singleLine = true,
                            placeholder = { Text("Cole a URL do vídeo aqui...") },
                            leadingIcon = { Icon(Icons.Default.Link, null) },
                            shape = androidx.compose.foundation.shape.RoundedCornerShape(32.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedContainerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.6f),
                                unfocusedContainerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
                                disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.4f),
                                focusedBorderColor = MaterialTheme.colorScheme.primary,
                                unfocusedBorderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                                focusedTextColor = MaterialTheme.colorScheme.onSurface,
                                unfocusedTextColor = MaterialTheme.colorScheme.onSurface
                            )
                        )
                        Spacer(Modifier.width(8.dp))
                        // Rounded download button (pill)
                        FilledTonalButton(
                            onClick = {
                                val u = url.trim()
                                if (u.isNotBlank()) {
                                    pendingDownloadUrl = u
                                    showQualityDialog = true
                                }
                            },
                            shape = androidx.compose.foundation.shape.RoundedCornerShape(32.dp)
                        ) {
                            Icon(Icons.Default.Download, null)
                            Spacer(Modifier.width(4.dp))
                            Text("Baixar")
                        }
                    }
                }
            }

            // Filtros e ordenação
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 4.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text("Filtros", fontWeight = FontWeight.Medium)
                        Text("Ordenar", fontWeight = FontWeight.Medium)
                    }
                    Spacer(Modifier.height(8.dp))
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectableGroup(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // Filtros de status
                        Column(modifier = Modifier.weight(1f)) {
                            Row(horizontalArrangement = Arrangement.spacedBy(4.dp)) {
                                listOf("All", "Running", "Completed", "Failed", "Paused").take(3).forEach { status ->
                                    FilterChip(
                                        selected = filterStatus == status,
                                        onClick = { filterStatus = status },
                                        label = {
                                            Text(
                                                when(status) {
                                                    "All" -> "Todos"
                                                    "Running" -> "Ativo"
                                                    "Completed" -> "Ok"
                                                    "Failed" -> "Erro"
                                                    "Paused" -> "Pausa"
                                                    else -> status
                                                },
                                                fontSize = 10.sp
                                            )
                                        },
                                        modifier = Modifier.height(24.dp)
                                    )
                                }
                            }
                        }
                        
                        // Ordenação
                        Column(modifier = Modifier.weight(1f)) {
                            Row(horizontalArrangement = Arrangement.spacedBy(4.dp)) {
                                listOf("Date", "Name", "Size", "Progress").take(3).forEach { sort ->
                                    FilterChip(
                                        selected = sortBy == sort,
                                        onClick = { sortBy = sort },
                                        label = {
                                            Text(
                                                when(sort) {
                                                    "Date" -> "Data"
                                                    "Name" -> "Nome"
                                                    "Size" -> "Tamanho"
                                                    "Progress" -> "Progresso"
                                                    else -> sort
                                                },
                                                fontSize = 10.sp
                                            )
                                        },
                                        modifier = Modifier.height(24.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }

            // Ações em lote
            if (showBatchActions && selectedItems.isNotEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 4.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.tertiaryContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        TextButton(onClick = {
                            selectedItems.forEach { id ->
                                scope.launch { repo.markPaused(id) }
                            }
                            selectedItems = emptySet()
                        }) {
                            Icon(Icons.Default.Pause, null)
                            Text("Pausar")
                        }
                        TextButton(onClick = {
                            selectedItems.forEach { id ->
                                val item = downloads.find { it.id == id }
                                item?.let { DownloadWorker.enqueue(context, it.url, it.title, it.mimeType) }
                            }
                            selectedItems = emptySet()
                        }) {
                            Icon(Icons.Default.PlayArrow, null)
                            Text("Retomar")
                        }
                        TextButton(onClick = {
                            selectedItems.forEach { id ->
                                scope.launch { repo.markCanceled(id) }
                            }
                            selectedItems = emptySet()
                        }) {
                            Icon(Icons.Default.Delete, null)
                            Text("Excluir")
                        }
                    }
                }
            }

            // Lista de downloads
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(horizontal = 16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(filteredDownloads, key = { it.id }) { item ->
                    ModernDownloadItemRow(
                        item = item,
                        isSelected = selectedItems.contains(item.id),
                        onSelectionChange = { selected ->
                            selectedItems = if (selected) {
                                selectedItems + item.id
                            } else {
                                selectedItems - item.id
                            }
                        },
                        showBatchActions = showBatchActions,
                        onPause = {
                            scope.launch { repo.markPaused(item.id) }
                        },
                        onResume = {
                            DownloadWorker.enqueue(context, item.url, item.title, item.mimeType)
                        },
                        onCancel = {
                            scope.launch { 
                                repo.markCanceled(item.id)
                                // Remove completamente do banco de dados
                                repo.delete(item.id)
                            }
                        },
                        onOpen = {
                            val type: String
                            val source: String
                            val uri = item.mediaStoreUri
                            if (!uri.isNullOrBlank() && item.status == DownloadItem.Status.Completed) {
                                type = "mediaStore"
                                source = java.net.URLEncoder.encode(uri, "UTF-8")
                            } else {
                                type = "url"
                                source = java.net.URLEncoder.encode(item.url, "UTF-8")
                            }
                            navController.navigate("player?type=$type&source=$source")
                        }
                    )
                }
            }
        }
    }
    
    // Diálogo de seleção de qualidade
    if (showQualityDialog) {
        AlertDialog(
            onDismissRequest = { showQualityDialog = false },
            title = { Text("Selecionar Qualidade") },
            text = {
                Column {
                    Text("Escolha a qualidade de video desejada:")
                    Spacer(Modifier.height(16.dp))
                    
                    val qualities = listOf("1080p", "720p", "480p", "Melhor disponível")
                    qualities.forEach { quality ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { selectedQuality = quality }
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = selectedQuality == quality,
                                onClick = { selectedQuality = quality }
                            )
                            Spacer(Modifier.width(8.dp))
                            Text(quality)
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        // Iniciar download com qualidade selecionada
                        val extractor = com.anonymous.videodownloaderapp.services.UniversalVideoUrlExtractor(
                            context = context,
                            repo = repo
                        )
                        val result = extractor.tryEnqueue(pendingDownloadUrl, pageTitle = null)
                        if (result.accepted) {
                            url = ""
                        }
                        showQualityDialog = false
                        pendingDownloadUrl = ""
                    }
                ) {
                    Text("Baixar")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { 
                        showQualityDialog = false
                        pendingDownloadUrl = ""
                    }
                ) {
                    Text("Cancelar")
                }
            }
        )
    }
}

@Composable
private fun StatisticItem(label: String, value: String) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Text(value, fontWeight = FontWeight.Bold, fontSize = 18.sp)
        Text(label, fontSize = 10.sp, color = MaterialTheme.colorScheme.onSurfaceVariant)
    }
}

@Composable
private fun ModernDownloadItemRow(
    item: DownloadItem,
    isSelected: Boolean,
    onSelectionChange: (Boolean) -> Unit,
    showBatchActions: Boolean,
    onPause: () -> Unit,
    onResume: () -> Unit,
    onCancel: () -> Unit,
    onOpen: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { if (!showBatchActions) onOpen() else onSelectionChange(!isSelected) },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) MaterialTheme.colorScheme.secondaryContainer
                           else MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = if (isSelected) 8.dp else 2.dp)
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Checkbox para seleção em lote
            if (showBatchActions) {
                Checkbox(
                    checked = isSelected,
                    onCheckedChange = onSelectionChange
                )
                Spacer(Modifier.width(8.dp))
            }

            // Ícone do status
            Box(
                modifier = Modifier.size(40.dp),
                contentAlignment = Alignment.Center
            ) {
                when (item.status) {
                    DownloadItem.Status.Running -> {
                        CircularProgressIndicator(
                            progress = { item.progress / 100f },
                            modifier = Modifier.fillMaxSize(),
                            strokeWidth = 3.dp
                        )
                        Text("${item.progress}%", fontSize = 8.sp)
                    }
                    DownloadItem.Status.Completed -> Icon(Icons.Default.CheckCircle, null, tint = Color(0xFF4CAF50))
                    DownloadItem.Status.Failed -> Icon(Icons.Default.Error, null, tint = Color(0xFFF44336))
                    DownloadItem.Status.Paused -> Icon(Icons.Default.Pause, null, tint = Color(0xFFFF9800))
                    DownloadItem.Status.Queued -> Icon(Icons.Default.Schedule, null, tint = Color(0xFF2196F3))
                    DownloadItem.Status.Canceled -> Icon(Icons.Default.Cancel, null, tint = Color(0xFF9E9E9E))
                }
            }
            
            Spacer(Modifier.width(12.dp))
            
            // Informações do download
            Column(Modifier.weight(1f)) {
                Text(
                    text = item.title ?: "Sem título",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Spacer(Modifier.height(2.dp))
                Text(
                    text = item.url,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    fontSize = 11.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(Modifier.height(4.dp))
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        "${item.progress}% • ${formatBytes(item.bytesDownloaded)}/${formatBytes(item.totalBytes)}",
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(Modifier.width(8.dp))
                    Text(
                        item.status.name,
                        fontSize = 9.sp,
                        color = when (item.status) {
                            DownloadItem.Status.Completed -> Color(0xFF4CAF50)
                            DownloadItem.Status.Failed -> Color(0xFFF44336)
                            DownloadItem.Status.Running -> Color(0xFF2196F3)
                            else -> MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
            }
            
            // Ações
            if (!showBatchActions) {
                Row(horizontalArrangement = Arrangement.spacedBy(4.dp)) {
                    when (item.status) {
                        DownloadItem.Status.Running -> {
                            IconButton(onClick = onPause) {
                                Icon(Icons.Default.Pause, "Pausar", modifier = Modifier.size(20.dp))
                            }
                        }
                        DownloadItem.Status.Paused, DownloadItem.Status.Queued, DownloadItem.Status.Failed -> {
                            IconButton(onClick = onResume) {
                                Icon(Icons.Default.PlayArrow, "Retomar", modifier = Modifier.size(20.dp))
                            }
                        }
                        else -> {}
                    }
                    // Two-step delete: cancel if running, else permanently remove from DB
                    IconButton(onClick = {
                        if (item.status == DownloadItem.Status.Running) {
                            onCancel()
                        } else {
                            // Hard delete from storage using provided callback to keep scope ownership outside
                            onCancel()
                        }
                    }) {
                        Icon(Icons.Default.Delete, "Excluir", modifier = Modifier.size(20.dp))
                    }
                }
            }
        }
    }
}

private fun formatBytes(bytes: Long): String {
    if (bytes == 0L) return "0 B"
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    val digitGroups = (Math.log10(bytes.toDouble()) / Math.log10(1024.0)).toInt()
    return String.format("%.1f %s", bytes / Math.pow(1024.0, digitGroups.toDouble()), units[digitGroups])
}
package com.anonymous.videodownloaderapp.features.browser.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\u001a(\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0003\u001aN\u0010\b\u001a\u00020\u00012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u001e\u0010\u0004\u001a\u001a\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\f2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u00a8\u0006\u000e"}, d2 = {"VideoItem", "", "video", "Lcom/anonymous/videodownloaderapp/features/browser/VideoQuality;", "onDownload", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "VideoQualityDialog", "videos", "", "onDismiss", "Lkotlin/Function3;", "", "app_debug"})
public final class VideoQualityDialogKt {
    
    @androidx.compose.runtime.Composable()
    public static final void VideoQualityDialog(@org.jetbrains.annotations.NotNull()
    java.util.List<com.anonymous.videodownloaderapp.features.browser.VideoQuality> videos, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super java.lang.String, kotlin.Unit> onDownload, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void VideoItem(com.anonymous.videodownloaderapp.features.browser.VideoQuality video, kotlin.jvm.functions.Function0<kotlin.Unit> onDownload, androidx.compose.ui.Modifier modifier) {
    }
}
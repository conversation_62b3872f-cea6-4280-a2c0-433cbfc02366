/**
 * Persistent Download Tracker
 * Maintains comprehensive download records across app sessions
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import RNFS from 'react-native-fs';

class DownloadTracker {
  static downloadListKey = 'video_downloads';
  static downloadStatusKey = 'background_download_status';
  static activeDownloads = new Map(); // Map para downloads ativos em memória

  static async initialize() {
    console.log('[DOWNLOAD_TRACKER] Initializing...');
    await this.loadDownloadsFromStorage();
    console.log('[DOWNLOAD_TRACKER] Initialized.');
  }

  static async loadDownloadsFromStorage() {
    try {
      const savedDownloadsJson = await AsyncStorage.getItem(this.downloadListKey);
      if (savedDownloadsJson) {
        const savedDownloads = JSON.parse(savedDownloadsJson);
        // Limpar downloads antigos (por exemplo, mais de 3 dias)
        const filteredDownloads = savedDownloads.filter(download => {
          const downloadDate = new Date(download.timestamp || 0);
          const threeDaysAgo = new Date();
          threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
          return downloadDate > threeDaysAgo;
        });
        await AsyncStorage.setItem(this.downloadListKey, JSON.stringify(filteredDownloads));
        // Não popular activeDownloads aqui, eles são gerenciados pelo BackgroundDownloadService
      }
    } catch (error) {
      console.error('[DOWNLOAD_TRACKER] Erro ao carregar downloads do storage:', error);
    }
  }

  static async addDownload(downloadInfo) {
    try {
      const existingDownloads = await this.getDownloads();
      const updatedDownloads = [{ ...downloadInfo, timestamp: new Date().toISOString() }, ...existingDownloads];
      await AsyncStorage.setItem(this.downloadListKey, JSON.stringify(updatedDownloads));
      console.log('[DOWNLOAD_TRACKER] Download adicionado:', downloadInfo.id);
      this.updateDownloadStatus(downloadInfo.id, downloadInfo.status, downloadInfo.error);
    } catch (error) {
      console.error('[DOWNLOAD_TRACKER] Erro ao adicionar download:', error);
      throw error;
    }
  }

  static async updateDownloadProgress(downloadId, progress, downloadedSize, totalSize) {
    try {
      const downloads = await this.getDownloads();
      const index = downloads.findIndex(d => d.id === downloadId);
      if (index !== -1) {
        downloads[index].progress = progress;
        downloads[index].downloadedSize = downloadedSize;
        downloads[index].totalSize = totalSize;
        downloads[index].status = 'downloading';
        await AsyncStorage.setItem(this.downloadListKey, JSON.stringify(downloads));
        // console.log(`[DOWNLOAD_TRACKER] Progresso de ${downloadId}: ${progress.toFixed(1)}%`);
        this.updateInMemoryStatus(downloadId, { progress, downloadedSize, totalSize, status: 'downloading' });
      }
    } catch (error) {
      console.error('[DOWNLOAD_TRACKER] Erro ao atualizar progresso:', error);
    }
  }

  static async updateDownloadStatus(downloadId, status, error = null) {
    try {
      const downloads = await this.getDownloads();
      const index = downloads.findIndex(d => d.id === downloadId);
      if (index !== -1) {
        downloads[index].status = status;
        downloads[index].error = error;
        if (status === 'completed') {
          downloads[index].progress = 100;
          downloads[index].completedTime = new Date().toISOString();
        }
        await AsyncStorage.setItem(this.downloadListKey, JSON.stringify(downloads));
        console.log(`[DOWNLOAD_TRACKER] Status de ${downloadId} atualizado para: ${status}`);
        this.updateInMemoryStatus(downloadId, { status, error });
      }
    } catch (error) {
      console.error('[DOWNLOAD_TRACKER] Erro ao atualizar status:', error);
    }
  }

  static async updateDownloadUri(downloadId, newUri) {
    try {
      const downloads = await this.getDownloads();
      const index = downloads.findIndex(d => d.id === downloadId);
      if (index !== -1) {
        downloads[index].uri = newUri;
        await AsyncStorage.setItem(this.downloadListKey, JSON.stringify(downloads));
        console.log(`[DOWNLOAD_TRACKER] URI de ${downloadId} atualizado para: ${newUri}`);
      }
    } catch (error) {
      console.error('[DOWNLOAD_TRACKER] Erro ao atualizar URI:', error);
    }
  }

  static async removeDownload(downloadId) {
    try {
      const downloads = await this.getDownloads();
      const updatedDownloads = downloads.filter(d => d.id !== downloadId);
      await AsyncStorage.setItem(this.downloadListKey, JSON.stringify(updatedDownloads));
      console.log('[DOWNLOAD_TRACKER] Download removido:', downloadId);
    } catch (error) {
      console.error('[DOWNLOAD_TRACKER] Erro ao remover download:', error);
    }
  }

  static async getDownloads() {
    try {
      const downloadsJson = await AsyncStorage.getItem(this.downloadListKey);
      return downloadsJson ? JSON.parse(downloadsJson) : [];
    } catch (error) {
      console.error('[DOWNLOAD_TRACKER] Erro ao obter downloads:', error);
      return [];
    }
  }

  static async checkDownloadExists(downloadInfo) {
    const normalizedUrl = downloadInfo.normalizedUrl || this.normalizeVideoUrl(downloadInfo.url || downloadInfo.originalUrl);
    const quality = downloadInfo.quality?.quality || 'default';

    const downloads = await this.getDownloads();

    const existingDownload = downloads.find(d =>
      (d.normalizedUrl === normalizedUrl || this.normalizeVideoUrl(d.url || d.originalUrl) === normalizedUrl) &&
      d.quality?.quality === quality &&
      (d.status === 'completed' || d.status === 'downloading' || d.status === 'queued')
    );

    if (existingDownload) {
      return { exists: true, reason: 'duplicate', id: existingDownload.id, status: existingDownload.status };
    }

    // Verificar se o arquivo já existe no sistema de arquivos diretamente
    const fileName = this.generateFileName(downloadInfo.url);
    const downloadDest = `${RNFS.DocumentDirectoryPath}/${fileName}`;
    const fileExistsOnDisk = await RNFS.exists(downloadDest);

    if (fileExistsOnDisk) {
      return { exists: true, reason: 'file_on_disk', path: downloadDest };
    }

    return { exists: false };
  }

  static normalizeVideoUrl(url) {
    if (!url) return url;
    try {
      let cleanUrl = url.trim();
      if (!cleanUrl.startsWith('http')) return cleanUrl;

      const urlObj = new URL(cleanUrl);
      const paramsToRemove = [
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term',
        'fbclid', 'gclid', 'msclkid', 'igshid', 'ref', 'ref_src', 'ref_url',
        't', 'si', 'feature', 'app', 'via', 'source', 'from'
      ];
      paramsToRemove.forEach(param => urlObj.searchParams.delete(param));

      if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
        let videoId = null;
        if (urlObj.hostname.includes('youtu.be')) videoId = urlObj.pathname.split('/')[1]?.split('?')[0];
        else if (urlObj.searchParams.has('v')) videoId = urlObj.searchParams.get('v');
        else if (urlObj.pathname.includes('/embed/')) videoId = urlObj.pathname.split('/embed/')[1]?.split('?')[0];
        if (videoId && videoId.length === 11) return `https://www.youtube.com/watch?v=${videoId}`;
      }
      if (urlObj.hostname.includes('vimeo.com')) {
        const pathParts = urlObj.pathname.split('/').filter(p => p);
        const videoId = pathParts.find(p => /^\\d+$/.test(p));
        if (videoId) return `https://vimeo.com/${videoId}`;
      }
      if (/\.(mp4|webm|avi|mov|mkv|flv|wmv|m4v|m3u8|mpd)(\?|$)/i.test(urlObj.pathname)) {
        return `${urlObj.protocol}//${urlObj.hostname}${urlObj.pathname}`;
      }
      return urlObj.toString();
    } catch (e) {
      console.warn('Erro ao normalizar URL:', url, e.message);
      return url;
    }
  }

  static generateFileName(url) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const extension = this.getFileExtension(url);
    return `video_${timestamp}.${extension}`;
  }

  static getFileExtension(url) {
    const match = url.match(/.\.(mp4|webm|avi|mov|mkv|flv|wmv|m4v|m3u8|mpd)(\?|$)/i);
    if (match && match[1]) return match[1];
    return 'mp4'; // default
  }

  static async getAllDownloads() {
    const allDownloads = await this.getDownloads();
    return {
      active: allDownloads.filter(d => d.status === 'downloading' || d.status === 'queued'),
      completed: allDownloads.filter(d => d.status === 'completed'),
      failed: allDownloads.filter(d => d.status === 'failed' || d.status === 'cancelled'),
    };
  }

  static async clearAllDownloads() {
    try {
      await AsyncStorage.removeItem(this.downloadListKey);
      this.activeDownloads.clear();
      console.log('[DOWNLOAD_TRACKER] Todos os downloads limpos.');
    } catch (error) {
      console.error('[DOWNLOAD_TRACKER] Erro ao limpar todos os downloads:', error);
      throw error;
    }
  }

  static async clearCompletedAndFailedDownloads() {
    try {
      const downloads = await this.getDownloads();
      const filteredDownloads = downloads.filter(d => d.status === 'downloading' || d.status === 'queued');
      await AsyncStorage.setItem(this.downloadListKey, JSON.stringify(filteredDownloads));
      console.log('[DOWNLOAD_TRACKER] Downloads concluídos e falhos limpos.');
    } catch (error) {
      console.error('[DOWNLOAD_TRACKER] Erro ao limpar downloads concluídos e falhos:', error);
      throw error;
    }
  }

  static updateInMemoryStatus(downloadId, updates) {
    const existing = this.activeDownloads.get(downloadId);
    if (existing) {
      this.activeDownloads.set(downloadId, { ...existing, ...updates });
    }
  }
}

export default DownloadTracker;

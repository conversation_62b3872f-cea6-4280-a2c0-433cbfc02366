package com.anonymous.videodownloaderapp.services

import android.content.Context
import com.anonymous.videodownloaderapp.data.AppDatabase
import com.anonymous.videodownloaderapp.data.DownloadDao
import com.anonymous.videodownloaderapp.data.DownloadItem
import kotlinx.coroutines.flow.Flow

class DownloadTracker(context: Context) {

    private val downloadDao: DownloadDao = AppDatabase.get(context).downloadDao()

    val downloads: Flow<List<DownloadItem>> = downloadDao.observeAll()

    suspend fun addDownload(item: DownloadItem) {
        downloadDao.upsert(item)
    }

    suspend fun updateDownload(updatedItem: DownloadItem) {
        downloadDao.update(updatedItem)
    }

    suspend fun removeDownload(id: Long) {
        downloadDao.deleteById(id)
    }

    suspend fun clearAllDownloads() {
        downloadDao.pruneFinished()
    }

    suspend fun clearCompletedAndFailedDownloads() {
        downloadDao.pruneFinished()
    }
}


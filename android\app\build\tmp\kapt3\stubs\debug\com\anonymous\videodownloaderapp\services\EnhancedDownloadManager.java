package com.anonymous.videodownloaderapp.services;

/**
 * Manager service that orchestrates both progressive (OkHttp) and segmented (HLS/DASH via Media3)
 * downloads. For now, progressive is handled by EnhancedDownloadService; segmented manifests are
 * enqueued via UniversalVideoUrlExtractor.enqueueHls/enqueueDash that currently delegates to progressive
 * until full Media3 DownloadService is introduced.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001:\u0002\u001e\u001fB\u0005\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0006J\u0010\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0016H\u0016J\b\u0010\u0017\u001a\u00020\u0011H\u0016J\b\u0010\u0018\u001a\u00020\u0011H\u0016J\u000e\u0010\u0019\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0006J\u000e\u0010\u001a\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0006J\u000e\u0010\u001b\u001a\u00020\u00112\u0006\u0010\u001c\u001a\u00020\u001dR \u0010\u0003\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0012\u0010\b\u001a\u00060\tR\u00020\u0000X\u0082\u0004\u00a2\u0006\u0002\n\u0000R#\u0010\n\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u00050\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006 "}, d2 = {"Lcom/anonymous/videodownloaderapp/services/EnhancedDownloadManager;", "Landroidx/lifecycle/LifecycleService;", "()V", "_downloadStates", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "", "Lcom/anonymous/videodownloaderapp/services/EnhancedDownloadManager$DownloadState;", "binder", "Lcom/anonymous/videodownloaderapp/services/EnhancedDownloadManager$LocalBinder;", "downloadStates", "Lkotlinx/coroutines/flow/StateFlow;", "getDownloadStates", "()Lkotlinx/coroutines/flow/StateFlow;", "progressiveService", "Lcom/anonymous/videodownloaderapp/services/EnhancedDownloadService;", "cancelDownload", "", "downloadId", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onCreate", "onDestroy", "pauseDownload", "resumeDownload", "startDownload", "downloadItem", "Lcom/anonymous/videodownloaderapp/data/DownloadItem;", "DownloadState", "LocalBinder", "app_debug"})
public final class EnhancedDownloadManager extends androidx.lifecycle.LifecycleService {
    @org.jetbrains.annotations.NotNull()
    private final com.anonymous.videodownloaderapp.services.EnhancedDownloadManager.LocalBinder binder = null;
    @org.jetbrains.annotations.NotNull()
    private final com.anonymous.videodownloaderapp.services.EnhancedDownloadService progressiveService = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Map<java.lang.String, com.anonymous.videodownloaderapp.services.EnhancedDownloadManager.DownloadState>> _downloadStates = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, com.anonymous.videodownloaderapp.services.EnhancedDownloadManager.DownloadState>> downloadStates = null;
    
    public EnhancedDownloadManager() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, com.anonymous.videodownloaderapp.services.EnhancedDownloadManager.DownloadState>> getDownloadStates() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.os.IBinder onBind(@org.jetbrains.annotations.NotNull()
    android.content.Intent intent) {
        return null;
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    public final void startDownload(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadItem downloadItem) {
    }
    
    public final void pauseDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId) {
    }
    
    public final void resumeDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId) {
    }
    
    public final void cancelDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BA\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0007\u0012\b\b\u0002\u0010\f\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\nH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0007H\u00c6\u0003JO\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00072\b\b\u0002\u0010\f\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010$\u001a\u00020\u0005H\u00d6\u0001J\t\u0010%\u001a\u00020&H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\f\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000fR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u000f\u00a8\u0006\'"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/EnhancedDownloadManager$DownloadState;", "", "downloadItem", "Lcom/anonymous/videodownloaderapp/data/DownloadItem;", "progress", "", "bytesDownloaded", "", "totalBytes", "status", "Lcom/anonymous/videodownloaderapp/data/DownloadItem$Status;", "speed", "eta", "(Lcom/anonymous/videodownloaderapp/data/DownloadItem;IJJLcom/anonymous/videodownloaderapp/data/DownloadItem$Status;JJ)V", "getBytesDownloaded", "()J", "getDownloadItem", "()Lcom/anonymous/videodownloaderapp/data/DownloadItem;", "getEta", "getProgress", "()I", "getSpeed", "getStatus", "()Lcom/anonymous/videodownloaderapp/data/DownloadItem$Status;", "getTotalBytes", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
    public static final class DownloadState {
        @org.jetbrains.annotations.NotNull()
        private final com.anonymous.videodownloaderapp.data.DownloadItem downloadItem = null;
        private final int progress = 0;
        private final long bytesDownloaded = 0L;
        private final long totalBytes = 0L;
        @org.jetbrains.annotations.NotNull()
        private final com.anonymous.videodownloaderapp.data.DownloadItem.Status status = null;
        private final long speed = 0L;
        private final long eta = 0L;
        
        public DownloadState(@org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.data.DownloadItem downloadItem, int progress, long bytesDownloaded, long totalBytes, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.data.DownloadItem.Status status, long speed, long eta) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.data.DownloadItem getDownloadItem() {
            return null;
        }
        
        public final int getProgress() {
            return 0;
        }
        
        public final long getBytesDownloaded() {
            return 0L;
        }
        
        public final long getTotalBytes() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.data.DownloadItem.Status getStatus() {
            return null;
        }
        
        public final long getSpeed() {
            return 0L;
        }
        
        public final long getEta() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.data.DownloadItem component1() {
            return null;
        }
        
        public final int component2() {
            return 0;
        }
        
        public final long component3() {
            return 0L;
        }
        
        public final long component4() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.data.DownloadItem.Status component5() {
            return null;
        }
        
        public final long component6() {
            return 0L;
        }
        
        public final long component7() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.EnhancedDownloadManager.DownloadState copy(@org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.data.DownloadItem downloadItem, int progress, long bytesDownloaded, long totalBytes, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.data.DownloadItem.Status status, long speed, long eta) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/EnhancedDownloadManager$LocalBinder;", "Landroid/os/Binder;", "(Lcom/anonymous/videodownloaderapp/services/EnhancedDownloadManager;)V", "getService", "Lcom/anonymous/videodownloaderapp/services/EnhancedDownloadManager;", "app_debug"})
    public final class LocalBinder extends android.os.Binder {
        
        public LocalBinder() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.EnhancedDownloadManager getService() {
            return null;
        }
    }
}
package com.anonymous.videodownloaderapp.data

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "downloads")
data class DownloadItem(
    @PrimaryKey(autoGenerate = true) val id: Long = 0,
    val url: String,
    val title: String? = null,
    val mimeType: String? = null,
    val status: Status = Status.Queued,
    val progress: Int = 0,              // 0..100
    val bytesDownloaded: Long = 0L,
    val totalBytes: Long = 0L,
    val mediaStoreUri: String? = null,  // content:// Uri.toString()
    val error: String? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
) {
    enum class Status { Queued, Running, Paused, Completed, Failed, Canceled }
}
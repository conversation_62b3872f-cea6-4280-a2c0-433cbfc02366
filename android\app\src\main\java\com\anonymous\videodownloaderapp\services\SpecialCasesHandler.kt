package com.anonymous.videodownloaderapp.services

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.webkit.WebView
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import kotlin.math.abs

/**
 * Tratamento de Casos Especiais Críticos
 * Quarentena de Conteúdo Offscreen, PiP, Streams Live
 */
class SpecialCasesHandler(private val context: Context) {

    data class QuarantineEntry(
        val candidateId: String,
        val url: String,
        val element: VideoElement?,
        val quarantineStartTime: Long,
        val reason: QuarantineReason,
        val interactionCount: Int = 0,
        val lastInteractionTime: Long = 0
    )

    data class VideoElement(
        val bounds: Rect,
        val isVisible: <PERSON>olean,
        val hasUserInteraction: <PERSON>olean,
        val isPiP: <PERSON><PERSON><PERSON>,
        val isInCarousel: <PERSON><PERSON><PERSON>,
        val isInTab: <PERSON><PERSON><PERSON>,
        val zIndex: Int,
        val opacity: Float
    )

    data class PiPState(
        val isActive: <PERSON>olean,
        val activationTime: Long,
        val originalCandidateId: String?,
        val pipBounds: Rect?
    )

    data class LiveStreamInfo(
        val isLive: Boolean,
        val manifestUrl: String,
        val lastManifestUpdate: Long,
        val availableQualities: List<String>,
        val isDynamic: Boolean
    )

    enum class QuarantineReason {
        OFFSCREEN_INVISIBLE,
        CAROUSEL_HIDDEN,
        TAB_INACTIVE,
        SIZE_TOO_SMALL,
        SUSPICIOUS_BEHAVIOR,
        AUTOPLAY_SPAM
    }

    companion object {
        private const val QUARANTINE_TIMEOUT_MS = 5000L // 5 segundos
        private const val PIP_GRACE_PERIOD_MS = 10000L // 10 segundos
        private const val LIVE_MANIFEST_REFRESH_MS = 300000L // 5 minutos
        private const val MIN_INTERACTION_SCORE = 2
        private const val MIN_VISIBLE_AREA_THRESHOLD = 0.3f // 30% visível
        private const val MIN_SIZE_THRESHOLD = 100 // 100px mínimo
        
        @Volatile
        private var instance: SpecialCasesHandler? = null
        
        fun getInstance(context: Context): SpecialCasesHandler {
            return instance ?: synchronized(this) {
                instance ?: SpecialCasesHandler(context.applicationContext).also {
                    instance = it
                }
            }
        }
    }

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // Quarentena de conteúdo offscreen
    private val quarantineMap = ConcurrentHashMap<String, QuarantineEntry>()
    private val _quarantineState = MutableStateFlow<List<QuarantineEntry>>(emptyList())
    val quarantineState: StateFlow<List<QuarantineEntry>> = _quarantineState
    
    // Estado Picture-in-Picture
    private val _pipState = MutableStateFlow(PiPState(false, 0, null, null))
    val pipState: StateFlow<PiPState> = _pipState
    
    // Streams live
    private val liveStreamsMap = ConcurrentHashMap<String, LiveStreamInfo>()
    private val _liveStreamsState = MutableStateFlow<List<LiveStreamInfo>>(emptyList())
    val liveStreamsState: StateFlow<List<LiveStreamInfo>> = _liveStreamsState
    
    // Jobs de limpeza
    private val cleanupJobs = ConcurrentHashMap<String, Job>()

    init {
        startPeriodicCleanup()
    }

    /**
     * Avaliar se candidato deve ser colocado em quarentena
     */
    fun evaluateForQuarantine(
        candidateId: String,
        url: String,
        element: VideoElement?
    ): Boolean {
        
        val quarantineReason = determineQuarantineReason(element)
        
        if (quarantineReason != null) {
            addToQuarantine(candidateId, url, element, quarantineReason)
            return true
        }
        
        // Remover da quarentena se estava lá
        removeFromQuarantine(candidateId)
        return false
    }

    /**
     * Determinar razão para quarentena
     */
    private fun determineQuarantineReason(element: VideoElement?): QuarantineReason? {
        if (element == null) return null
        
        // Verificar visibilidade
        if (!element.isVisible) {
            return QuarantineReason.OFFSCREEN_INVISIBLE
        }
        
        // Verificar tamanho mínimo
        val area = element.bounds.width() * element.bounds.height()
        if (area < MIN_SIZE_THRESHOLD * MIN_SIZE_THRESHOLD) {
            return QuarantineReason.SIZE_TOO_SMALL
        }
        
        // Verificar se está em carousel/tab inativo
        if (element.isInCarousel && !element.hasUserInteraction) {
            return QuarantineReason.CAROUSEL_HIDDEN
        }
        
        if (element.isInTab && !element.hasUserInteraction) {
            return QuarantineReason.TAB_INACTIVE
        }
        
        // Verificar opacidade
        if (element.opacity < 0.1f) {
            return QuarantineReason.OFFSCREEN_INVISIBLE
        }
        
        return null
    }

    /**
     * Adicionar à quarentena
     */
    private fun addToQuarantine(
        candidateId: String,
        url: String,
        element: VideoElement?,
        reason: QuarantineReason
    ) {
        val entry = QuarantineEntry(
            candidateId = candidateId,
            url = url,
            element = element,
            quarantineStartTime = System.currentTimeMillis(),
            reason = reason
        )
        
        quarantineMap[candidateId] = entry
        updateQuarantineState()
        
        // Agendar remoção automática
        scheduleQuarantineCleanup(candidateId)
    }

    /**
     * Remover da quarentena
     */
    fun removeFromQuarantine(candidateId: String) {
        quarantineMap.remove(candidateId)
        cleanupJobs[candidateId]?.cancel()
        cleanupJobs.remove(candidateId)
        updateQuarantineState()
    }

    /**
     * Promover candidato da quarentena (quando se torna visível)
     */
    fun promoteFromQuarantine(candidateId: String, hasUserInteraction: Boolean = false) {
        val entry = quarantineMap[candidateId] ?: return
        
        val updatedEntry = entry.copy(
            interactionCount = entry.interactionCount + if (hasUserInteraction) 1 else 0,
            lastInteractionTime = if (hasUserInteraction) System.currentTimeMillis() else entry.lastInteractionTime
        )
        
        // Promover se tem interação suficiente
        if (updatedEntry.interactionCount >= MIN_INTERACTION_SCORE || hasUserInteraction) {
            removeFromQuarantine(candidateId)
        } else {
            quarantineMap[candidateId] = updatedEntry
            updateQuarantineState()
        }
    }

    /**
     * Verificar se candidato está em quarentena
     */
    fun isInQuarantine(candidateId: String): Boolean {
        return quarantineMap.containsKey(candidateId)
    }

    /**
     * Ativar modo Picture-in-Picture
     */
    fun activatePiP(candidateId: String, bounds: Rect?) {
        _pipState.value = PiPState(
            isActive = true,
            activationTime = System.currentTimeMillis(),
            originalCandidateId = candidateId,
            pipBounds = bounds
        )
        
        // Remover da quarentena se estava lá
        removeFromQuarantine(candidateId)
        
        // Agendar desativação automática
        scope.launch {
            delay(PIP_GRACE_PERIOD_MS)
            if (_pipState.value.originalCandidateId == candidateId) {
                deactivatePiP()
            }
        }
    }

    /**
     * Desativar modo Picture-in-Picture
     */
    fun deactivatePiP() {
        _pipState.value = PiPState(false, 0, null, null)
    }

    /**
     * Verificar se candidato deve ter exceção de PiP
     */
    fun shouldHavePiPException(candidateId: String): Boolean {
        val currentPiP = _pipState.value
        
        if (!currentPiP.isActive) return false
        
        // Exceção se é o candidato original do PiP
        if (currentPiP.originalCandidateId == candidateId) return true
        
        // Exceção se PiP foi ativado recentemente
        val timeSincePiP = System.currentTimeMillis() - currentPiP.activationTime
        return timeSincePiP < PIP_GRACE_PERIOD_MS
    }

    /**
     * Registrar stream live
     */
    fun registerLiveStream(
        manifestUrl: String,
        isDynamic: Boolean = false,
        initialQualities: List<String> = emptyList()
    ) {
        val liveInfo = LiveStreamInfo(
            isLive = true,
            manifestUrl = manifestUrl,
            lastManifestUpdate = System.currentTimeMillis(),
            availableQualities = initialQualities,
            isDynamic = isDynamic
        )
        
        liveStreamsMap[manifestUrl] = liveInfo
        updateLiveStreamsState()
        
        // Agendar re-parsing periódico para streams dinâmicos
        if (isDynamic) {
            scheduleManifestRefresh(manifestUrl)
        }
    }

    /**
     * Verificar se URL é stream live
     */
    fun isLiveStream(url: String): Boolean {
        return liveStreamsMap.values.any { 
            it.manifestUrl == url || url.contains(it.manifestUrl)
        }
    }

    /**
     * Obter informações de stream live
     */
    fun getLiveStreamInfo(url: String): LiveStreamInfo? {
        return liveStreamsMap[url] ?: liveStreamsMap.values.find { 
            url.contains(it.manifestUrl)
        }
    }

    /**
     * Atualizar qualidades de stream live
     */
    fun updateLiveStreamQualities(manifestUrl: String, newQualities: List<String>) {
        val existing = liveStreamsMap[manifestUrl] ?: return
        
        val updated = existing.copy(
            availableQualities = newQualities,
            lastManifestUpdate = System.currentTimeMillis()
        )
        
        liveStreamsMap[manifestUrl] = updated
        updateLiveStreamsState()
    }

    /**
     * Verificar se deve usar cache invalidation agressiva
     */
    fun shouldUseAggressiveCacheInvalidation(url: String): Boolean {
        val liveInfo = getLiveStreamInfo(url)
        return liveInfo?.isDynamic == true
    }

    /**
     * Obter timeout de cache para URL
     */
    fun getCacheTimeout(url: String): Long {
        return if (isLiveStream(url)) {
            LIVE_MANIFEST_REFRESH_MS // 5 minutos para live
        } else {
            TimeUnit.MINUTES.toMillis(15) // 15 minutos para VOD
        }
    }

    /**
     * Agendar limpeza de quarentena
     */
    private fun scheduleQuarantineCleanup(candidateId: String) {
        cleanupJobs[candidateId]?.cancel()
        
        cleanupJobs[candidateId] = scope.launch {
            delay(QUARANTINE_TIMEOUT_MS)
            
            val entry = quarantineMap[candidateId]
            if (entry != null) {
                // Verificar se teve interação recente
                val timeSinceInteraction = System.currentTimeMillis() - entry.lastInteractionTime
                
                if (entry.interactionCount == 0 || timeSinceInteraction > QUARANTINE_TIMEOUT_MS) {
                    // Descarte definitivo
                    removeFromQuarantine(candidateId)
                }
            }
        }
    }

    /**
     * Agendar refresh de manifest live
     */
    private fun scheduleManifestRefresh(manifestUrl: String) {
        scope.launch {
            while (liveStreamsMap.containsKey(manifestUrl)) {
                delay(LIVE_MANIFEST_REFRESH_MS)
                
                val liveInfo = liveStreamsMap[manifestUrl]
                if (liveInfo?.isDynamic == true) {
                    // Trigger re-parsing do manifest
                    // Isso seria implementado pelo parser específico (HLS/DASH)
                    triggerManifestReparse(manifestUrl)
                }
            }
        }
    }

    /**
     * Trigger re-parsing de manifest (implementação específica)
     */
    private suspend fun triggerManifestReparse(manifestUrl: String) {
        try {
            // Aqui seria chamado o parser apropriado (HLS/DASH)
            // para re-analisar o manifest e atualizar qualidades
            
            when {
                manifestUrl.contains(".m3u8") -> {
                    // Re-parse HLS manifest
                    val hlsParser = HlsManifestParser(context)
                    val newQualities = hlsParser.parseMaster(manifestUrl)
                    updateLiveStreamQualities(manifestUrl, newQualities.map { it.label })
                }
                manifestUrl.contains(".mpd") -> {
                    // Re-parse DASH manifest
                    val dashParser = DashManifestParser(context)
                    val newQualities = dashParser.parse(manifestUrl)
                    updateLiveStreamQualities(manifestUrl, newQualities.map { it.label })
                }
            }
        } catch (e: Exception) {
            // Ignorar erros de re-parsing
        }
    }

    /**
     * Limpeza periódica
     */
    private fun startPeriodicCleanup() {
        scope.launch {
            while (true) {
                delay(TimeUnit.MINUTES.toMillis(5)) // A cada 5 minutos
                
                // Limpar quarentena expirada
                val now = System.currentTimeMillis()
                val expiredEntries = quarantineMap.values.filter { 
                    now - it.quarantineStartTime > QUARANTINE_TIMEOUT_MS * 2
                }
                
                expiredEntries.forEach { entry ->
                    removeFromQuarantine(entry.candidateId)
                }
                
                // Limpar streams live inativos
                val inactiveStreams = liveStreamsMap.values.filter {
                    now - it.lastManifestUpdate > TimeUnit.HOURS.toMillis(1)
                }
                
                inactiveStreams.forEach { stream ->
                    liveStreamsMap.remove(stream.manifestUrl)
                }
                
                updateLiveStreamsState()
            }
        }
    }

    /**
     * Atualizar estado da quarentena
     */
    private fun updateQuarantineState() {
        _quarantineState.value = quarantineMap.values.toList()
    }

    /**
     * Atualizar estado dos streams live
     */
    private fun updateLiveStreamsState() {
        _liveStreamsState.value = liveStreamsMap.values.toList()
    }

    /**
     * Obter estatísticas de casos especiais
     */
    fun getSpecialCasesStats(): Map<String, Any> {
        val currentPiP = _pipState.value
        
        return mapOf(
            "quarantineCount" to quarantineMap.size,
            "quarantineReasons" to quarantineMap.values.groupBy { it.reason }.mapValues { it.value.size },
            "isPiPActive" to currentPiP.isActive,
            "pipActivationTime" to currentPiP.activationTime,
            "liveStreamsCount" to liveStreamsMap.size,
            "dynamicStreamsCount" to liveStreamsMap.values.count { it.isDynamic },
            "cleanupJobsCount" to cleanupJobs.size
        )
    }

    /**
     * Limpeza de recursos
     */
    fun cleanup() {
        scope.cancel()
        quarantineMap.clear()
        liveStreamsMap.clear()
        cleanupJobs.values.forEach { it.cancel() }
        cleanupJobs.clear()
        instance = null
    }
}
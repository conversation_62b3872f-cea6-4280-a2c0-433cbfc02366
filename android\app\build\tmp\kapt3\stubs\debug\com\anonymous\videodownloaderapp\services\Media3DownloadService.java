package com.anonymous.videodownloaderapp.services;

/**
 * Foreground DownloadService using Media3 for segmented downloads (HLS/DASH).
 * This wires a DownloadManager with a simple on-disk cache and shows notifications.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u0000 \u000e2\u00020\u0001:\u0001\u000eB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0014J\u001e\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000bH\u0014J\n\u0010\f\u001a\u0004\u0018\u00010\rH\u0014\u00a8\u0006\u000f"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/Media3DownloadService;", "Landroidx/media3/exoplayer/offline/DownloadService;", "()V", "getDownloadManager", "Landroidx/media3/exoplayer/offline/DownloadManager;", "getForegroundNotification", "Landroid/app/Notification;", "downloads", "", "Landroidx/media3/exoplayer/offline/Download;", "notMetRequirements", "", "getScheduler", "Landroidx/media3/exoplayer/scheduler/Scheduler;", "Companion", "app_debug"})
public final class Media3DownloadService extends androidx.media3.exoplayer.offline.DownloadService {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CHANNEL_ID = "media3_downloads";
    public static final int FOREGROUND_NOTIFICATION_ID = 3335;
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.services.Media3DownloadService.Companion Companion = null;
    
    public Media3DownloadService() {
        super(0);
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    protected androidx.media3.exoplayer.offline.DownloadManager getDownloadManager() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    protected androidx.media3.exoplayer.scheduler.Scheduler getScheduler() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    protected android.app.Notification getForegroundNotification(@org.jetbrains.annotations.NotNull()
    java.util.List<androidx.media3.exoplayer.offline.Download> downloads, int notMetRequirements) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u000b\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/Media3DownloadService$Companion;", "", "()V", "CHANNEL_ID", "", "FOREGROUND_NOTIFICATION_ID", "", "start", "", "context", "Landroid/content/Context;", "stop", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final void start(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
        }
        
        public final void stop(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
        }
    }
}
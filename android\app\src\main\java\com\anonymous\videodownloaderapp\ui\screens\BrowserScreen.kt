/*
   Deprecated duplicate BrowserScreen kept intentionally empty to avoid composable conflicts.
   The app now uses the unified BrowserScreen in features/browser/BrowserScreen.kt
   This file is retained to prevent import/compile references from breaking if any old code points here.
*/
package com.anonymous.videodownloaderapp.ui.screens

import android.annotation.SuppressLint
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.TextField
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.TextButton
import androidx.compose.material3.Text
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import kotlinx.coroutines.launch
import com.anonymous.videodownloaderapp.services.BackgroundDownloadService
import com.anonymous.videodownloaderapp.data.DownloadItem
import java.net.URLEncoder
import java.util.UUID
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import com.anonymous.videodownloaderapp.R
import org.json.JSONObject
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.material3.FloatingActionButton
import androidx.compose.ui.Alignment
import androidx.compose.foundation.layout.size
import androidx.compose.material3.OutlinedTextField
import androidx.compose.ui.unit.dp
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun BrowserScreen() {
    // Deprecated stub composable to avoid symbol resolution errors.
    // Use com.anonymous.videodownloaderapp.features.browser.BrowserScreen instead.
    Column(modifier = Modifier.fillMaxSize()) { }
}

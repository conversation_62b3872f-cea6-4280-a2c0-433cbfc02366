package com.anonymous.videodownloaderapp.services

import android.webkit.CookieManager
import android.webkit.WebSettings
import android.webkit.WebView

class PrivacyService {
    companion object {
        /**
         * Configura as definições de privacidade para o WebView
         */
        fun configurePrivacySettings(webView: WebView) {
            val settings = webView.settings
            
            // Bloquear cookies de terceiros
            val cookieManager = CookieManager.getInstance()
            cookieManager.setAcceptThirdPartyCookies(webView, false)
            
            // Configurações de privacidade
            settings.apply {
                // Desabilitar geolocalização por padrão
                setGeolocationEnabled(false)
                
                // Bloquear acesso a câmera e microfone
                mediaPlaybackRequiresUserGesture = true
                
                // Desabilitar salvamento de senhas
                savePassword = false
                saveFormData = false
                
                // Configurações de segurança
                allowFileAccessFromFileURLs = false
                allowUniversalAccessFromFileURLs = false
                
                // User Agent mais genérico para reduzir fingerprinting
                userAgentString = getPrivacyFriendlyUserAgent()
                
                // Configurações de texto
                textZoom = 100
                
                // Desabilitar mixed content
                mixedContentMode = WebSettings.MIXED_CONTENT_NEVER_ALLOW
            }
        }
        
        /**
         * Retorna um User Agent mais genérico para reduzir fingerprinting
         */
        private fun getPrivacyFriendlyUserAgent(): String {
            return "Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
        }
        
        /**
         * Injeta JavaScript para bloquear tentativas de fingerprinting
         */
        fun injectAntiTrackingScript(webView: WebView) {
            val antiTrackingScript = """
                (function() {
                    // Bloquear canvas fingerprinting
                    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                    HTMLCanvasElement.prototype.toDataURL = function() {
                        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
                    };
                    
                    const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
                    CanvasRenderingContext2D.prototype.getImageData = function() {
                        const imageData = originalGetImageData.apply(this, arguments);
                        for (let i = 0; i < imageData.data.length; i += 4) {
                            imageData.data[i] = 255;
                            imageData.data[i + 1] = 255;
                            imageData.data[i + 2] = 255;
                            imageData.data[i + 3] = 255;
                        }
                        return imageData;
                    };
                    
                    // Bloquear WebGL fingerprinting
                    const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
                    WebGLRenderingContext.prototype.getParameter = function(parameter) {
                        if (parameter === this.RENDERER || parameter === this.VENDOR) {
                            return 'Generic Renderer';
                        }
                        return originalGetParameter.apply(this, arguments);
                    };
                    
                    // Bloquear AudioContext fingerprinting
                    if (window.AudioContext) {
                        const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
                        AudioContext.prototype.createAnalyser = function() {
                            const analyser = originalCreateAnalyser.apply(this, arguments);
                            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                            analyser.getFloatFrequencyData = function(array) {
                                originalGetFloatFrequencyData.apply(this, arguments);
                                for (let i = 0; i < array.length; i++) {
                                    array[i] = array[i] + Math.random() * 0.0001;
                                }
                            };
                            return analyser;
                        };
                    }
                    
                    // Bloquear font fingerprinting
                    const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
                    const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');
                    
                    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
                        get: function() {
                            const width = originalOffsetWidth.get.call(this);
                            return width + Math.round(Math.random() * 2 - 1);
                        }
                    });
                    
                    Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
                        get: function() {
                            const height = originalOffsetHeight.get.call(this);
                            return height + Math.round(Math.random() * 2 - 1);
                        }
                    });
                    
                    // Bloquear timezone fingerprinting
                    const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
                    Date.prototype.getTimezoneOffset = function() {
                        return 0; // UTC
                    };
                    
                    // Bloquear screen fingerprinting
                    Object.defineProperty(screen, 'width', {
                        get: function() { return 1920; }
                    });
                    Object.defineProperty(screen, 'height', {
                        get: function() { return 1080; }
                    });
                    Object.defineProperty(screen, 'availWidth', {
                        get: function() { return 1920; }
                    });
                    Object.defineProperty(screen, 'availHeight', {
                        get: function() { return 1040; }
                    });
                    Object.defineProperty(screen, 'colorDepth', {
                        get: function() { return 24; }
                    });
                    Object.defineProperty(screen, 'pixelDepth', {
                        get: function() { return 24; }
                    });
                    
                    // Bloquear navigator fingerprinting
                    Object.defineProperty(navigator, 'hardwareConcurrency', {
                        get: function() { return 4; }
                    });
                    Object.defineProperty(navigator, 'deviceMemory', {
                        get: function() { return 8; }
                    });
                    Object.defineProperty(navigator, 'platform', {
                        get: function() { return 'Linux x86_64'; }
                    });
                    
                    // Bloquear battery API
                    if (navigator.getBattery) {
                        navigator.getBattery = function() {
                            return Promise.resolve({
                                charging: true,
                                chargingTime: 0,
                                dischargingTime: Infinity,
                                level: 1
                            });
                        };
                    }
                    
                    console.log('Anti-tracking protection enabled');
                })();
            """.trimIndent()
            
            webView.evaluateJavascript(antiTrackingScript, null)
        }
        
        /**
         * Limpa dados de navegação para melhorar a privacidade
         */
        fun clearBrowsingData(webView: WebView) {
            // Limpar cache
            webView.clearCache(true)
            
            // Limpar histórico
            webView.clearHistory()
            
            // Limpar dados de formulários
            webView.clearFormData()
            
            // Limpar cookies
            val cookieManager = CookieManager.getInstance()
            cookieManager.removeAllCookies(null)
            cookieManager.flush()
        }
        
        /**
         * Configura headers de privacidade
         */
        fun getPrivacyHeaders(): Map<String, String> {
            return mapOf(
                "DNT" to "1", // Do Not Track
                "Sec-GPC" to "1", // Global Privacy Control
                "X-Requested-With" to "XMLHttpRequest"
            )
        }
    }
}
package com.anonymous.videodownloaderapp.optimization

import android.app.job.JobInfo
import android.app.job.JobScheduler
import android.content.ComponentName
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.BatteryManager
import android.os.Build
import android.os.PowerManager
import androidx.annotation.RequiresApi
import com.anonymous.videodownloaderapp.services.BatteryOptimizedDownloadService
import com.anonymous.videodownloaderapp.services.DownloadTracker
import kotlinx.coroutines.*
import java.util.concurrent.TimeUnit

class BatteryOptimizationManager(private val context: Context) {
    
    private val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
    
    // Battery optimization settings
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Track battery level and charging state
    fun isBatteryOptimizationEnabled(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            !powerManager.isIgnoringBatteryOptimizations(context.packageName)
        } else {
            true
        }
    }
    
    fun getBatteryLevel(): Int {
        return batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
    }
    
    fun isCharging(): Boolean {
        val batteryStatus = context.registerReceiver(null, android.content.IntentFilter(android.content.Intent.ACTION_BATTERY_CHANGED))
        val status = batteryStatus?.getIntExtra(android.os.BatteryManager.EXTRA_STATUS, -1) ?: -1
        return status == android.os.BatteryManager.BATTERY_STATUS_CHARGING || 
               status == android.os.BatteryManager.BATTERY_STATUS_FULL
    }
    
    fun shouldRestrictDownloads(): Boolean {
        val batteryLevel = getBatteryLevel()
        val isCharging = isCharging()
        val isWifi = isWifiConnected()
        
        // Restrict downloads if battery < 20% and not charging
        return batteryLevel < 20 && !isCharging && !isWifi
    }
    
    fun isWifiConnected(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            networkInfo?.type == ConnectivityManager.TYPE_WIFI
        }
    }
    
    fun optimizeDownloadSpeed(downloadId: String, currentSpeed: Long): Long {
        val batteryLevel = getBatteryLevel()
        val isCharging = isCharging()
        
        return when {
            batteryLevel < 10 && !isCharging -> 0L // Stop downloads
            batteryLevel < 20 && !isCharging -> (currentSpeed * 0.5).toLong() // Reduce speed by 50%
            batteryLevel < 30 && !isCharging -> (currentSpeed * 0.75).toLong() // Reduce speed by 25%
            else -> currentSpeed // Full speed
        }
    }
    
    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    fun scheduleBatteryOptimizedDownload(downloadId: String) {
        val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
        
        val jobInfo = JobInfo.Builder(downloadId.hashCode(), ComponentName(context, BatteryOptimizedDownloadService::class.java))
            .setRequiredNetworkType(JobInfo.NETWORK_TYPE_UNMETERED) // WiFi only
            .setRequiresCharging(true) // Only when charging
            .setPersisted(true) // Persist across reboots
            .setPeriodic(TimeUnit.HOURS.toMillis(1)) // Check every hour
            .build()
        
        jobScheduler.schedule(jobInfo)
    }
    
    // Monitor battery and adjust downloads
    fun startBatteryMonitoring() {
        coroutineScope.launch {
            while (isActive) {
                shouldRestrictDownloads()
                // Implementation would integrate with existing download tracking
                delay(60000) // Check every minute
            }
        }
    }
    
    fun stopBatteryMonitoring() {
        coroutineScope.cancel()
    }
    
    // Get battery optimization recommendations
    fun getOptimizationRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        
        if (getBatteryLevel() < 20) {
            recommendations.add("Battery level low. Consider charging device for downloads.")
        }
        
        if (!isWifiConnected()) {
            recommendations.add("Using mobile data. Consider WiFi for large downloads.")
        }
        
        if (!isCharging() && getBatteryLevel() < 30) {
            recommendations.add("Downloads may be slower to preserve battery.")
        }
        
        return recommendations
    }
}

// Background service for battery-optimized downloads would be implemented separately
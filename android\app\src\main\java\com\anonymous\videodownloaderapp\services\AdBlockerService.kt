package com.anonymous.videodownloaderapp.services

import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import java.io.ByteArrayInputStream
import java.util.regex.Pattern

class AdBlockerService {
    companion object {
        // Lista de domínios de anúncios conhecidos
        private val adDomains = setOf(
            "googleads.g.doubleclick.net",
            "googlesyndication.com",
            "googleadservices.com",
            "google-analytics.com",
            "googletagmanager.com",
            "facebook.com/tr",
            "connect.facebook.net",
            "amazon-adsystem.com",
            "adsystem.amazon.com",
            "ads.yahoo.com",
            "advertising.com",
            "adsense.google.com",
            "outbrain.com",
            "taboola.com",
            "criteo.com",
            "adsystem.amazon.de",
            "adsystem.amazon.co.uk",
            "scorecardresearch.com",
            "quantserve.com",
            "addthis.com",
            "sharethis.com",
            "disqus.com/embed",
            "zedo.com",
            "adsystem.amazon.fr",
            "adsystem.amazon.it",
            "adsystem.amazon.es",
            "adsystem.amazon.ca",
            "adsystem.amazon.com.au",
            "adsystem.amazon.co.jp",
            "adsystem.amazon.in",
            "adsystem.amazon.com.br",
            "adsystem.amazon.com.mx",
            "adsystem.amazon.cn",
            "ads.twitter.com",
            "analytics.twitter.com",
            "ads.linkedin.com",
            "analytics.linkedin.com",
            "ads.pinterest.com",
            "analytics.pinterest.com",
            "ads.reddit.com",
            "redditmedia.com",
            "ads.tiktok.com",
            "analytics.tiktok.com",
            "ads.snapchat.com",
            "analytics.snapchat.com",
            // Adições para melhorar bloqueio de anúncios em vídeos
            "youtube.com/get_video",
            "youtube.com/get_midroll",
            "youtube.com/api/ads",
            "ytimg.com",
            "doubleclick.net",
            "admob.com",
            "adservice.google.com",
            "pagead2.googlesyndication.com",
            "tpc.googlesyndication.com",
            "securepubads.g.doubleclick.net",
            "pubads.g.doubleclick.net"
        )
        
        // Padrões de URLs de anúncios
        private val adPatterns = listOf(
            Pattern.compile(".*/ads? /.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/advertisement.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/advert.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/adsystem.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/adservice.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/adnxs.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/doubleclick.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/googlesyndication.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/googleadservices.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/googletagmanager.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/google-analytics.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/analytics.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/tracking.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/tracker.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/telemetry.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/metrics.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/beacon.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/pixel.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/impression.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/click.*", Pattern.CASE_INSENSITIVE),
            // Adições para padrões de anúncios em vídeos
            Pattern.compile(".*/get_midroll.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/get_video_info.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/adview.*", Pattern.CASE_INSENSITIVE),
            Pattern.compile(".*/sponsor.*", Pattern.CASE_INSENSITIVE)
        )
        
        // Tipos de recursos que podem ser anúncios
        private val adResourceTypes = setOf(
            "image",
            "script",
            "stylesheet",
            "media",
            "font",
            "object",
            "xmlhttprequest",
            "ping",
            "csp_report",
            "other"
        )
    }
    
    /**
     * Verifica se uma requisição deve ser bloqueada
     */
    fun shouldBlockRequest(request: WebResourceRequest): Boolean {
        val url = request.url.toString().lowercase()
        val host = request.url.host?.lowercase() ?: ""
        
        // Verificar domínios de anúncios
        for (adDomain in adDomains) {
            if (host.contains(adDomain.lowercase())) {
                return true
            }
        }
        
        // Verificar padrões de URLs
        for (pattern in adPatterns) {
            if (pattern.matcher(url).matches()) {
                return true
            }
        }
        
        return false
    }
    
    /**
     * Cria uma resposta vazia para requisições bloqueadas
     */
    fun createBlockedResponse(): WebResourceResponse {
        return WebResourceResponse(
            "text/plain",
            "utf-8",
            ByteArrayInputStream("".toByteArray())
        )
    }
    
    /**
     * Verifica se deve bloquear trackers de privacidade
     */
    fun shouldBlockTracker(request: WebResourceRequest): Boolean {
        val url = request.url.toString().lowercase()
        val host = request.url.host?.lowercase() ?: ""
        
        // Lista de trackers conhecidos
        val trackers = setOf(
            "google-analytics.com",
            "googletagmanager.com",
            "facebook.com/tr",
            "connect.facebook.net",
            "scorecardresearch.com",
            "quantserve.com",
            "addthis.com",
            "sharethis.com",
            "hotjar.com",
            "fullstory.com",
            "mouseflow.com",
            "crazyegg.com",
            "mixpanel.com",
            "segment.com",
            "amplitude.com",
            "intercom.io",
            "zendesk.com",
            "freshworks.com",
            "salesforce.com",
            "marketo.com",
            "hubspot.com",
            "pardot.com",
            "eloqua.com",
            "mailchimp.com",
            "constantcontact.com",
            "aweber.com",
            "getresponse.com",
            "convertkit.com",
            "drip.com",
            "klaviyo.com"
        )
        
        for (tracker in trackers) {
            if (host.contains(tracker)) {
                return true
            }
        }
        
        // Verificar padrões de tracking
        val trackingPatterns = listOf(
            "analytics",
            "tracking",
            "tracker",
            "telemetry",
            "metrics",
            "beacon",
            "pixel",
            "impression",
            "click"
        )
        
        for (pattern in trackingPatterns) {
            if (url.contains(pattern)) {
                return true
            }
        }
        
        return false
    }
}
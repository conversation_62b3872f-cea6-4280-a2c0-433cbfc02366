package com.anonymous.videodownloaderapp.services

import android.content.Context
import androidx.media3.common.MimeTypes
import androidx.media3.database.DatabaseProvider
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.cache.Cache
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.datasource.okhttp.OkHttpDataSource
import androidx.media3.exoplayer.offline.DefaultDownloadIndex
import androidx.media3.exoplayer.offline.DownloadManager
import androidx.media3.exoplayer.offline.DownloadNotificationHelper
import androidx.media3.exoplayer.offline.DownloadRequest
import androidx.media3.exoplayer.offline.DownloaderFactory
import androidx.media3.exoplayer.offline.DefaultDownloaderFactory
import okhttp3.OkHttpClient
import java.io.File
import java.util.concurrent.Executors

/**
 * Media3 helper aligned with androidx.media3 1.4.x API.
 */
object Media3DownloadHelper {

    @Volatile private var simpleCache: Cache? = null
    @Volatile private var dbProvider: DatabaseProvider? = null
    @Volatile private var downloadManager: DownloadManager? = null
    @Volatile private var notificationHelper: DownloadNotificationHelper? = null
    private val downloadExecutor = Executors.newFixedThreadPool(2)

    fun getCache(context: Context): Cache {
        return simpleCache ?: synchronized(this) {
            simpleCache ?: run {
                val downloadDir = File(context.getExternalFilesDir(null), "media3_cache").apply { mkdirs() }
                val evictor = LeastRecentlyUsedCacheEvictor(512L * 1024L * 1024L) // 512MB
                SimpleCache(downloadDir, evictor, getDatabaseProvider(context)).also { simpleCache = it }
            }
        }
    }

    private fun getDatabaseProvider(context: Context): DatabaseProvider {
        return dbProvider ?: synchronized(this) {
            dbProvider ?: StandaloneDatabaseProvider(context).also { dbProvider = it }
        }
    }

    private fun getUpstreamFactory(context: Context): DataSource.Factory {
        val okHttp = OkHttpClient()
        return OkHttpDataSource.Factory(okHttp).setUserAgent("VideoDownloader/1.0")
    }

    private fun getCacheDataSourceFactory(context: Context): CacheDataSource.Factory {
        val cache = getCache(context)
        val upstream = getUpstreamFactory(context)
        return CacheDataSource.Factory()
            .setCache(cache)
            .setUpstreamDataSourceFactory(upstream)
            .setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)
    }

    fun getNotificationHelper(context: Context): DownloadNotificationHelper {
        return notificationHelper ?: synchronized(this) {
            notificationHelper ?: DownloadNotificationHelper(context, Media3DownloadService.CHANNEL_ID).also {
                notificationHelper = it
            }
        }
    }

    fun getDownloadManager(context: Context): DownloadManager {
        return downloadManager ?: synchronized(this) {
            downloadManager ?: run {
                val db = getDatabaseProvider(context)
                val downloadIndex = DefaultDownloadIndex(db)
                val downloaderFactory = DefaultDownloaderFactory(
                    getCacheDataSourceFactory(context),
                    downloadExecutor
                )
                // Media3 1.4.1 DownloadManager constructor:
                // DownloadManager(context, WritableDownloadIndex, DownloaderFactory)
                DownloadManager(
                    context,
                    downloadIndex,
                    downloaderFactory
                ).also { dm ->
                    dm.maxParallelDownloads = 2
                    downloadManager = dm
                }
            }
        }
    }

    fun buildHlsRequest(url: String, title: String? = null, desiredHeight: Int? = null): DownloadRequest {
        val id = title?.takeIf { it.isNotBlank() } ?: url
        return DownloadRequest.Builder(id, url.toUriCompat())
            .setMimeType(MimeTypes.APPLICATION_M3U8)
            .setCustomCacheKey(cacheKeyFor(url, desiredHeight))
            .build()
    }

    fun buildDashRequest(url: String, title: String? = null, desiredHeight: Int? = null): DownloadRequest {
        val id = title?.takeIf { it.isNotBlank() } ?: url
        return DownloadRequest.Builder(id, url.toUriCompat())
            .setMimeType(MimeTypes.APPLICATION_MPD)
            .setCustomCacheKey(cacheKeyFor(url, desiredHeight))
            .build()
    }

    private fun cacheKeyFor(url: String, desiredHeight: Int?): String {
        return if (desiredHeight != null) "$url#h=$desiredHeight" else url
    }

    private fun String.toUriCompat(): android.net.Uri = android.net.Uri.parse(this)
}
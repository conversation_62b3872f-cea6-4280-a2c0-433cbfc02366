// Video Detection Performance Service
// Optimizes video thumbnail detection for performance and cross-platform compatibility

class VideoDetectionPerformanceService {
  
  // Performance configuration
  static PERFORMANCE_CONFIG = {
    maxElementsPerBatch: 50,
    batchProcessingDelay: 100,
    debounceDelay: 300,
    throttleDelay: 200,
    maxMutationBatchSize: 100,
    intersectionObserverThreshold: 0.1,
    lazyLoadingMargin: '100px'
  };

  // Device capability detection
  static DEVICE_CAPABILITIES = {
    isLowEndDevice: null,
    supportsIntersectionObserver: null,
    supportsMutationObserver: null,
    supportsPassiveListeners: null,
    maxConcurrentOperations: 10
  };

  // Initialize performance service
  static initialize() {
    this.detectDeviceCapabilities();
    this.setupPerformanceMonitoring();
    return this.getOptimizedConfig();
  }

  // Detect device capabilities
  static detectDeviceCapabilities() {
    // Detect low-end device
    this.DEVICE_CAPABILITIES.isLowEndDevice = this.isLowEndDevice();
    
    // Check API support
    this.DEVICE_CAPABILITIES.supportsIntersectionObserver = 'IntersectionObserver' in window;
    this.DEVICE_CAPABILITIES.supportsMutationObserver = 'MutationObserver' in window;
    this.DEVICE_CAPABILITIES.supportsPassiveListeners = this.supportsPassiveListeners();
    
    // Adjust max concurrent operations based on device
    if (this.DEVICE_CAPABILITIES.isLowEndDevice) {
      this.DEVICE_CAPABILITIES.maxConcurrentOperations = 5;
    }
  }

  // Detect if device is low-end
  static isLowEndDevice() {
    try {
      // Check memory (if available)
      if (navigator.deviceMemory && navigator.deviceMemory <= 2) {
        return true;
      }
      
      // Check hardware concurrency
      if (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) {
        return true;
      }
      
      // Check user agent for low-end indicators
      const userAgent = navigator.userAgent.toLowerCase();
      const lowEndIndicators = [
        'android 4', 'android 5', 'android 6',
        'iphone os 9', 'iphone os 10',
        'windows phone', 'blackberry'
      ];
      
      return lowEndIndicators.some(indicator => userAgent.includes(indicator));
    } catch (e) {
      return false; // Assume high-end if detection fails
    }
  }

  // Check passive listener support
  static supportsPassiveListeners() {
    let supportsPassive = false;
    try {
      const opts = Object.defineProperty({}, 'passive', {
        get: function() {
          supportsPassive = true;
        }
      });
      window.addEventListener('testPassive', null, opts);
      window.removeEventListener('testPassive', null, opts);
    } catch (e) {}
    return supportsPassive;
  }

  // Get optimized configuration based on device capabilities
  static getOptimizedConfig() {
    const config = { ...this.PERFORMANCE_CONFIG };
    
    if (this.DEVICE_CAPABILITIES.isLowEndDevice) {
      // Reduce processing for low-end devices
      config.maxElementsPerBatch = 25;
      config.batchProcessingDelay = 200;
      config.debounceDelay = 500;
      config.throttleDelay = 400;
      config.maxMutationBatchSize = 50;
    }
    
    return config;
  }

  // Setup performance monitoring
  static setupPerformanceMonitoring() {
    if (typeof performance !== 'undefined' && performance.mark) {
      this.performanceEnabled = true;
    }
  }

  // Performance-optimized element processing
  static async processElementsBatch(elements, processor, config) {
    const batches = this.createBatches(elements, config.maxElementsPerBatch);
    const results = [];
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      
      // Process batch
      const batchResults = await this.processBatch(batch, processor);
      results.push(...batchResults);
      
      // Add delay between batches to prevent blocking
      if (i < batches.length - 1) {
        await this.delay(config.batchProcessingDelay);
      }
    }
    
    return results;
  }

  // Create batches from elements array
  static createBatches(elements, batchSize) {
    const batches = [];
    for (let i = 0; i < elements.length; i += batchSize) {
      batches.push(elements.slice(i, i + batchSize));
    }
    return batches;
  }

  // Process a single batch
  static async processBatch(batch, processor) {
    const promises = batch.map(element => {
      try {
        return processor(element);
      } catch (error) {
        console.warn('Error processing element:', error);
        return null;
      }
    });
    
    const results = await Promise.allSettled(promises);
    return results
      .filter(result => result.status === 'fulfilled' && result.value !== null)
      .map(result => result.value);
  }

  // Optimized debounce function
  static createDebounce(func, delay) {
    let timeoutId;
    let lastCallTime = 0;
    
    return function(...args) {
      const now = Date.now();
      
      clearTimeout(timeoutId);
      
      // If enough time has passed, execute immediately
      if (now - lastCallTime > delay * 2) {
        lastCallTime = now;
        return func.apply(this, args);
      }
      
      // Otherwise, debounce
      timeoutId = setTimeout(() => {
        lastCallTime = Date.now();
        func.apply(this, args);
      }, delay);
    };
  }

  // Optimized throttle function
  static createThrottle(func, delay) {
    let lastCallTime = 0;
    let timeoutId;
    
    return function(...args) {
      const now = Date.now();
      
      if (now - lastCallTime >= delay) {
        lastCallTime = now;
        return func.apply(this, args);
      }
      
      if (!timeoutId) {
        timeoutId = setTimeout(() => {
          lastCallTime = Date.now();
          func.apply(this, args);
          timeoutId = null;
        }, delay - (now - lastCallTime));
      }
    };
  }

  // Intersection Observer for lazy loading
  static createIntersectionObserver(callback, options = {}) {
    if (!this.DEVICE_CAPABILITIES.supportsIntersectionObserver) {
      // Fallback for browsers without Intersection Observer
      return this.createScrollBasedObserver(callback);
    }
    
    const defaultOptions = {
      threshold: this.PERFORMANCE_CONFIG.intersectionObserverThreshold,
      rootMargin: this.PERFORMANCE_CONFIG.lazyLoadingMargin
    };
    
    const observerOptions = { ...defaultOptions, ...options };
    
    return new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          callback(entry.target);
        }
      });
    }, observerOptions);
  }

  // Fallback scroll-based observer
  static createScrollBasedObserver(callback) {
    const throttledCallback = this.createThrottle(() => {
      const elements = document.querySelectorAll('[data-lazy-video]');
      elements.forEach(element => {
        if (this.isElementInViewport(element)) {
          callback(element);
        }
      });
    }, this.PERFORMANCE_CONFIG.throttleDelay);
    
    window.addEventListener('scroll', throttledCallback, 
      this.DEVICE_CAPABILITIES.supportsPassiveListeners ? { passive: true } : false
    );
    
    return {
      observe: (element) => {
        element.setAttribute('data-lazy-video', 'true');
      },
      unobserve: (element) => {
        element.removeAttribute('data-lazy-video');
      },
      disconnect: () => {
        window.removeEventListener('scroll', throttledCallback);
      }
    };
  }

  // Check if element is in viewport
  static isElementInViewport(element) {
    const rect = element.getBoundingClientRect();
    const margin = 100; // pixels
    
    return (
      rect.top >= -margin &&
      rect.left >= -margin &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) + margin &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth) + margin
    );
  }

  // Optimized mutation observer
  static createOptimizedMutationObserver(callback, options = {}) {
    if (!this.DEVICE_CAPABILITIES.supportsMutationObserver) {
      console.warn('MutationObserver not supported');
      return null;
    }
    
    const config = this.getOptimizedConfig();
    let mutationQueue = [];
    let processingTimeout;
    
    const processQueue = () => {
      if (mutationQueue.length === 0) return;
      
      const mutations = mutationQueue.splice(0, config.maxMutationBatchSize);
      callback(mutations);
      
      // Continue processing if there are more mutations
      if (mutationQueue.length > 0) {
        processingTimeout = setTimeout(processQueue, config.batchProcessingDelay);
      }
    };
    
    const observer = new MutationObserver((mutations) => {
      mutationQueue.push(...mutations);
      
      if (!processingTimeout) {
        processingTimeout = setTimeout(processQueue, config.debounceDelay);
      }
    });
    
    const defaultOptions = {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'data-video-id', 'data-video-url', 'src']
    };
    
    observer.observe(document.body, { ...defaultOptions, ...options });
    
    return observer;
  }

  // Memory management utilities
  static cleanupWeakReferences(weakSet, elements) {
    // Clean up processed elements that are no longer in DOM
    elements.forEach(element => {
      if (!document.contains(element)) {
        // Element is no longer in DOM, but WeakSet will handle cleanup automatically
      }
    });
  }

  // Performance measurement
  static measurePerformance(name, func) {
    if (!this.performanceEnabled) {
      return func();
    }
    
    const startMark = `${name}-start`;
    const endMark = `${name}-end`;
    const measureName = `${name}-measure`;
    
    performance.mark(startMark);
    const result = func();
    performance.mark(endMark);
    performance.measure(measureName, startMark, endMark);
    
    return result;
  }

  // Utility delay function
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get performance metrics
  static getPerformanceMetrics() {
    if (!this.performanceEnabled) {
      return null;
    }
    
    const measures = performance.getEntriesByType('measure');
    return measures.filter(measure => measure.name.includes('video-detection'));
  }

  // Clear performance metrics
  static clearPerformanceMetrics() {
    if (this.performanceEnabled) {
      performance.clearMarks();
      performance.clearMeasures();
    }
  }
}

export default VideoDetectionPerformanceService;

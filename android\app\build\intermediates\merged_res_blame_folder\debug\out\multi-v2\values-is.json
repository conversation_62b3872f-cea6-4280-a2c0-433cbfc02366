{"logs": [{"outputFile": "com.anonymous.videodownloaderapp-mergeDebugResources-79:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\19dbdb4d446295775d71c6ca0010df0c\\transformed\\media3-ui-1.4.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,488,689,774,858,935,1024,1121,1190,1254,1345,1436,1499,1563,1625,1693,1817,1943,2067,2142,2223,2296,2365,2448,2530,2595,2675,2728,2789,2839,2900,2959,3029,3092,3154,3218,3278,3344,3396,3456,3530,3604,3657", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,51,59,73,73,52,64", "endOffsets": "283,483,684,769,853,930,1019,1116,1185,1249,1340,1431,1494,1558,1620,1688,1812,1938,2062,2137,2218,2291,2360,2443,2525,2590,2670,2723,2784,2834,2895,2954,3024,3087,3149,3213,3273,3339,3391,3451,3525,3599,3652,3717"}, "to": {"startLines": "2,11,15,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,383,583,5604,5689,5773,5850,5939,6036,6105,6169,6260,6351,6414,6478,6540,6608,6732,6858,6982,7057,7138,7211,7280,7363,7445,7510,8220,8273,8334,8384,8445,8504,8574,8637,8699,8763,8823,8889,8941,9001,9075,9149,9202", "endLines": "10,14,18,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,51,59,73,73,52,64", "endOffsets": "378,578,779,5684,5768,5845,5934,6031,6100,6164,6255,6346,6409,6473,6535,6603,6727,6853,6977,7052,7133,7206,7275,7358,7440,7505,7585,8268,8329,8379,8440,8499,8569,8632,8694,8758,8818,8884,8936,8996,9070,9144,9197,9262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a1b6440c5b30b54fb1514c977cfe8efa\\transformed\\material-1.12.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1009,1074,1162,1226,1287,1377,1441,1504,1566,1634,1698,1754,1877,1942,2004,2060,2131,2258,2342,2416,2513,2594,2678,2814,2891,2968,3084,3171,3250,3307,3362,3428,3504,3584,3655,3731,3798,3872,3942,4008,4110,4196,4266,4357,4447,4521,4594,4683,4734,4815,4887,4968,5054,5116,5180,5243,5312,5426,5532,5640,5742,5803,5862,5942,6026,6105", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79,83,78,74", "endOffsets": "265,339,411,490,572,652,749,864,946,1004,1069,1157,1221,1282,1372,1436,1499,1561,1629,1693,1749,1872,1937,1999,2055,2126,2253,2337,2411,2508,2589,2673,2809,2886,2963,3079,3166,3245,3302,3357,3423,3499,3579,3650,3726,3793,3867,3937,4003,4105,4191,4261,4352,4442,4516,4589,4678,4729,4810,4882,4963,5049,5111,5175,5238,5307,5421,5527,5635,5737,5798,5857,5937,6021,6100,6175"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,70,71,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,244,248,249,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,3628,3702,3774,3853,3935,4732,4829,4944,5481,5539,9267,9355,9594,15844,15934,15998,16061,16123,16191,16255,16311,16434,16499,16561,16617,16688,16815,16899,16973,17070,17151,17235,17371,17448,17525,17641,17728,17807,17864,17919,17985,18061,18141,18212,18288,18355,18429,18499,18565,18667,18753,18823,18914,19004,19078,19151,19240,19291,19372,19444,19525,19611,19673,19737,19800,19869,19983,20089,20197,20299,20360,20590,20912,20996,21145", "endLines": "22,50,51,52,53,54,62,63,64,70,71,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,244,248,249,251", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79,83,78,74", "endOffsets": "949,3697,3769,3848,3930,4010,4824,4939,5021,5534,5599,9350,9414,9650,15929,15993,16056,16118,16186,16250,16306,16429,16494,16556,16612,16683,16810,16894,16968,17065,17146,17230,17366,17443,17520,17636,17723,17802,17859,17914,17980,18056,18136,18207,18283,18350,18424,18494,18560,18662,18748,18818,18909,18999,19073,19146,19235,19286,19367,19439,19520,19606,19668,19732,19795,19864,19978,20084,20192,20294,20355,20414,20665,20991,21070,21215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c83662cb1041180272524de55f94ad06\\transformed\\media3-exoplayer-1.4.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,181,240,306,382,445,534,616", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "117,176,235,301,377,440,529,611,680"}, "to": {"startLines": "96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7590,7657,7716,7775,7841,7917,7980,8069,8151", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "7652,7711,7770,7836,7912,7975,8064,8146,8215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd2b74cc75dcc4061ff406ef39af9f1c\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "55,56,57,58,59,60,61,255", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4015,4110,4217,4314,4414,4517,4621,21444", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "4105,4212,4309,4409,4512,4616,4727,21540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\576ca5b088d8fb564e2e136af4f08d54\\transformed\\material3-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,500,597,696,812,953,1080,1215,1305,1406,1503,1603,1718,1844,1950,2075,2199,2341,2512,2635,2751,2870,2992,3090,3188,3297,3419,3525,3633,3736,3866,4001,4109,4214,4290,4384,4477,4591,4676,4761,4870,4950,5041,5142,5243,5338,5446,5534,5639,5740,5846,5966,6046,6148", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "164,276,383,495,592,691,807,948,1075,1210,1300,1401,1498,1598,1713,1839,1945,2070,2194,2336,2507,2630,2746,2865,2987,3085,3183,3292,3414,3520,3628,3731,3861,3996,4104,4209,4285,4379,4472,4586,4671,4756,4865,4945,5036,5137,5238,5333,5441,5529,5634,5735,5841,5961,6041,6143,6239"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9655,9769,9881,9988,10100,10197,10296,10412,10553,10680,10815,10905,11006,11103,11203,11318,11444,11550,11675,11799,11941,12112,12235,12351,12470,12592,12690,12788,12897,13019,13125,13233,13336,13466,13601,13709,13814,13890,13984,14077,14191,14276,14361,14470,14550,14641,14742,14843,14938,15046,15134,15239,15340,15446,15566,15646,15748", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "9764,9876,9983,10095,10192,10291,10407,10548,10675,10810,10900,11001,11098,11198,11313,11439,11545,11670,11794,11936,12107,12230,12346,12465,12587,12685,12783,12892,13014,13120,13228,13331,13461,13596,13704,13809,13885,13979,14072,14186,14271,14356,14465,14545,14636,14737,14838,14933,15041,15129,15234,15335,15441,15561,15641,15743,15839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ea0d069a154b88d6448f277841df52b5\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,376,475,560,640,735,824,906,984,1067,1137,1212,1287,1361,1438,1506", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,74,74,73,76,67,119", "endOffsets": "191,272,371,470,555,635,730,819,901,979,1062,1132,1207,1282,1356,1433,1501,1621"}, "to": {"startLines": "65,66,67,68,69,124,125,242,243,245,246,250,252,253,254,256,257,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5026,5117,5198,5297,5396,9419,9499,20419,20508,20670,20748,21075,21220,21295,21370,21545,21622,21690", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,74,74,73,76,67,119", "endOffsets": "5112,5193,5292,5391,5476,9494,9589,20503,20585,20743,20826,21140,21290,21365,21439,21617,21685,21805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c594a6b1ad90298d1781df40da764bd0\\transformed\\foundation-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "259,260", "startColumns": "4,4", "startOffsets": "21810,21897", "endColumns": "86,86", "endOffsets": "21892,21979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\366aa02d18db07ca95bf2d64251e5a94\\transformed\\appcompat-1.7.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "954,1054,1151,1263,1348,1449,1563,1644,1723,1814,1907,2000,2094,2200,2293,2388,2483,2574,2668,2749,2859,2966,3063,3172,3272,3375,3530,20831", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "1049,1146,1258,1343,1444,1558,1639,1718,1809,1902,1995,2089,2195,2288,2383,2478,2569,2663,2744,2854,2961,3058,3167,3267,3370,3525,3623,20907"}}]}]}
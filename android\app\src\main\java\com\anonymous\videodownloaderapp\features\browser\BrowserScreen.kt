package com.anonymous.videodownloaderapp.features.browser

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.VideoLibrary
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.anonymous.videodownloaderapp.R
import com.anonymous.videodownloaderapp.data.DownloadRepository
import com.anonymous.videodownloaderapp.data.SettingsRepository
import com.anonymous.videodownloaderapp.features.browser.components.*
import androidx.activity.compose.BackHandler

// Data classes
data class VideoQuality(
    val quality: String,
    val url: String,
    val format: String = "mp4",
    val isDownloaded: Boolean = false
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BrowserScreen(
    navController: NavController,
    downloadRepository: DownloadRepository
) {
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val viewModel: BrowserViewModel = viewModel(
        factory = BrowserViewModelFactory(context, downloadRepository)
    )
    
    val uiState by viewModel.uiState.collectAsState()
    val isAdBlockEnabled by settingsRepository.isAdBlockingEnabled.collectAsState(initial = true)
    var webViewRef by remember { mutableStateOf<android.webkit.WebView?>(null) }
    
    // Observar mudanças de URL para atualizar a barra de endereço
    LaunchedEffect(uiState.currentUrl) {
        if (uiState.addressBarText != uiState.currentUrl && !uiState.isAddressBarFocused) {
            viewModel.updateAddressBarText(uiState.currentUrl)
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Barra de endereço com ícones do lado direito
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            AddressBar(
                text = uiState.addressBarText,
                isExpanded = uiState.isAddressBarExpanded,
                isLoading = uiState.isLoading,
                onTextChange = viewModel::updateAddressBarText,
                onExpandedChange = viewModel::onAddressBarExpandedChanged,
                onFocusChange = viewModel::onAddressBarFocusChanged,
                onGo = { viewModel.onGoToUrl(webViewRef) },
                modifier = Modifier.weight(1f)
            )
            
            // Ações da barra superior do lado direito
            TopBarActions(
                onHomeClick = { viewModel.onHomeClicked(webViewRef) },
                onDownloadsClick = { navController.navigate("downloads") },
                onMenuClick = { viewModel.onSettingsMenuVisibilityChanged(true) },
                showSettingsMenu = uiState.showSettingsMenu,
                onSettingsMenuDismiss = { viewModel.onSettingsMenuVisibilityChanged(false) },
                onSettingsClick = { navController.navigate("settings") },
                onRefreshClick = { viewModel.onRefresh(webViewRef) }
            )
        }
         
        // WebView
        Box(
            modifier = Modifier.weight(1f)
        ) {
            BrowserWebView(
                url = uiState.currentUrl,
                isLoading = uiState.isLoading,
                onUrlChanged = viewModel::onUrlChanged,
                onLoadingChanged = viewModel::onLoadingStateChanged,
                onPageStarted = { url -> viewModel.onUrlChanged(url) },
                onPageFinished = { url -> viewModel.onUrlChanged(url) },
                onVideoDetectionRequested = { webView -> viewModel.detectVideos(webView) },
                onRefresh = { webView -> viewModel.onRefresh(webView) },
                onWebViewCreated = { webView -> webViewRef = webView },
                onCanGoBackChanged = viewModel::onCanGoBackChanged,
                onCanGoForwardChanged = viewModel::onCanGoForwardChanged,
                adBlockEnabled = isAdBlockEnabled,
                modifier = Modifier.fillMaxSize()
            )
            
            // FAB para vídeos detectados
            if (uiState.detectedVideos.isNotEmpty()) {
                FloatingActionButton(
                    onClick = { viewModel.onVideoDialogVisibilityChanged(true) },
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(16.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.VideoLibrary,
                            contentDescription = stringResource(R.string.videos_detectados, uiState.detectedVideos.size)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("${uiState.detectedVideos.size}")
                    }
                }
            }
        }
    }
     
    // Dialog de vídeos
    if (uiState.showVideoDialog) {
        VideoQualityDialog(
            videos = uiState.detectedVideos,
            onDismiss = { viewModel.onVideoDialogVisibilityChanged(false) },
            onDownload = { quality, url, format ->
                viewModel.onDownloadVideo(quality, url, format)
                viewModel.onVideoDialogVisibilityChanged(false)
            }
        )
    }
    BackHandler(enabled = uiState.canGoBack) {
        viewModel.onGoBack(webViewRef)
    }
}
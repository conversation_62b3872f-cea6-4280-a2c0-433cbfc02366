package com.anonymous.videodownloaderapp.services

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.ResponseBody
import okio.Buffer
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import java.util.regex.Pattern

/**
 * Interceptor OkHttp nativo que captura todos os requests de mídia antes mesmo 
 * que cheguem ao WebView. Substitui completamente a Performance API limitada por CORS.
 */
class NetworkInterceptorService private constructor() : Interceptor {
    
    companion object {
        @Volatile
        private var INSTANCE: NetworkInterceptorService? = null
        
        fun getInstance(): NetworkInterceptorService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NetworkInterceptorService().also { INSTANCE = it }
            }
        }
    }
    
    // Patterns para detecção automática de padrões de mídia
    private val hlsPattern = Pattern.compile("\\.(m3u8)(\\?.*)?$", Pattern.CASE_INSENSITIVE)
    private val dashPattern = Pattern.compile("\\.(mpd)(\\?.*)?$", Pattern.CASE_INSENSITIVE)
    private val videoPattern = Pattern.compile("\\.(mp4|webm|avi|mov|m4v|ts)(\\?.*)?$", Pattern.CASE_INSENSITIVE)
    private val googleVideoPattern = Pattern.compile("googlevideo\\.com", Pattern.CASE_INSENSITIVE)
    private val videoPlaybackPattern = Pattern.compile("/videoplayback", Pattern.CASE_INSENSITIVE)
    
    // Cache de requests detectados
    private val detectedRequests = ConcurrentHashMap<String, MediaRequest>()
    private val requestCounter = AtomicLong(0)
    private val scope = CoroutineScope(SupervisorJob())
    
    data class MediaRequest(
        val id: String,
        val url: String,
        val method: String,
        val contentType: String?,
        val contentLength: Long?,
        val userAgent: String?,
        val referer: String?,
        val timestamp: Long,
        val mediaType: MediaType,
        val frameOrigin: String? = null
    )
    
    enum class MediaType {
        HLS_MASTER, HLS_SEGMENT, DASH_MANIFEST, DASH_SEGMENT, 
        PROGRESSIVE_VIDEO, GOOGLE_VIDEO, UNKNOWN
    }
    
    interface MediaRequestListener {
        fun onMediaRequestDetected(request: MediaRequest)
        fun onManifestContentAvailable(request: MediaRequest, content: String)
    }
    
    private val listeners = mutableSetOf<MediaRequestListener>()
    
    fun addListener(listener: MediaRequestListener) {
        synchronized(listeners) {
            listeners.add(listener)
        }
    }
    
    fun removeListener(listener: MediaRequestListener) {
        synchronized(listeners) {
            listeners.remove(listener)
        }
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val url = request.url.toString()
        val response = chain.proceed(request)
        
        // Verificar se é uma requisição de mídia relevante
        val mediaType = detectMediaType(url, response.header("Content-Type"))
        if (mediaType != MediaType.UNKNOWN) {
            val mediaRequest = MediaRequest(
                id = "req_${requestCounter.incrementAndGet()}",
                url = url,
                method = request.method,
                contentType = response.header("Content-Type"),
                contentLength = response.body?.contentLength(),
                userAgent = request.header("User-Agent"),
                referer = request.header("Referer"),
                timestamp = System.currentTimeMillis(),
                mediaType = mediaType,
                frameOrigin = extractFrameOrigin(request.header("Referer"))
            )
            
            // Cache do request
            detectedRequests[mediaRequest.id] = mediaRequest
            
            // Notificar listeners
            scope.launch {
                synchronized(listeners) {
                    listeners.forEach { it.onMediaRequestDetected(mediaRequest) }
                }
            }
            
            // Se for manifest, capturar conteúdo para parsing nativo
            if (shouldCaptureContent(mediaType, response)) {
                return captureAndReturnResponse(response, mediaRequest)
            }
        }
        
        return response
    }
    
    private fun detectMediaType(url: String, contentType: String?): MediaType {
        return when {
            hlsPattern.matcher(url).find() -> {
                // Distinguir master playlist de segment
                if (contentType?.contains("application/vnd.apple.mpegurl") == true ||
                    contentType?.contains("application/x-mpegURL") == true) {
                    MediaType.HLS_MASTER
                } else {
                    MediaType.HLS_SEGMENT
                }
            }
            dashPattern.matcher(url).find() -> MediaType.DASH_MANIFEST
            googleVideoPattern.matcher(url).find() -> MediaType.GOOGLE_VIDEO
            videoPlaybackPattern.matcher(url).find() -> MediaType.GOOGLE_VIDEO
            videoPattern.matcher(url).find() -> MediaType.PROGRESSIVE_VIDEO
            contentType?.startsWith("video/") == true -> MediaType.PROGRESSIVE_VIDEO
            else -> MediaType.UNKNOWN
        }
    }
    
    private fun shouldCaptureContent(mediaType: MediaType, response: Response): Boolean {
        val contentLength = response.body?.contentLength() ?: 0
        return when (mediaType) {
            MediaType.HLS_MASTER, MediaType.DASH_MANIFEST -> {
                // Capturar apenas manifests pequenos para evitar overhead
                contentLength in 1..1_000_000L && response.isSuccessful
            }
            else -> false
        }
    }
    
    private fun captureAndReturnResponse(response: Response, mediaRequest: MediaRequest): Response {
        val body = response.body ?: return response
        
        try {
            // Ler o conteúdo
            val source = body.source()
            source.request(Long.MAX_VALUE)
            val buffer = source.buffer.clone()
            val content = buffer.readUtf8()
            
            // Notificar conteúdo do manifest
            scope.launch {
                synchronized(listeners) {
                    listeners.forEach { 
                        it.onManifestContentAvailable(mediaRequest, content)
                    }
                }
            }
            
            // Retornar response com novo body
            return response.newBuilder()
                .body(ResponseBody.create(body.contentType(), content))
                .build()
                
        } catch (e: Exception) {
            // Em caso de erro, retornar response original
            return response
        }
    }
    
    private fun extractFrameOrigin(referer: String?): String? {
        if (referer.isNullOrBlank()) return null
        return try {
            val url = java.net.URL(referer)
            "${url.protocol}://${url.host}"
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Obter requests detectados por frame/origem para priorizar conteúdo do player visível
     */
    fun getRequestsByOrigin(origin: String, maxAge: Long = 300_000L): List<MediaRequest> {
        val cutoff = System.currentTimeMillis() - maxAge
        return detectedRequests.values.filter { 
            it.timestamp >= cutoff && 
            (it.frameOrigin == origin || it.url.contains(origin, ignoreCase = true))
        }
    }
    
    /**
     * Limpar requests antigos para evitar vazamento de memória
     */
    fun cleanup(maxAge: Long = 900_000L) {
        val cutoff = System.currentTimeMillis() - maxAge
        val iterator = detectedRequests.entries.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            if (entry.value.timestamp < cutoff) {
                iterator.remove()
            }
        }
    }
    
    /**
     * Obter estatísticas de requests para debugging
     */
    fun getStats(): Map<String, Any> {
        val now = System.currentTimeMillis()
        val last5Min = detectedRequests.values.count { now - it.timestamp <= 300_000L }
        val byType = detectedRequests.values.groupingBy { it.mediaType }.eachCount()
        
        return mapOf(
            "totalRequests" to detectedRequests.size,
            "requestsLast5Min" to last5Min,
            "requestsByType" to byType,
            "memoryUsageKB" to (Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()) / 1024
        )
    }
}
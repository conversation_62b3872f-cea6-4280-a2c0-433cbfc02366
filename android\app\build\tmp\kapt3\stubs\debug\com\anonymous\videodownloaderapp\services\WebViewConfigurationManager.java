package com.anonymous.videodownloaderapp.services;

/**
 * Gerenciador de Configuração de WebView Otimizado
 * Configurações específicas para detecção de vídeo e performance
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0014\n\u0002\u0010\t\n\u0002\b\u0007\u0018\u0000 :2\u00020\u0001:\u0004:;<=B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\u0014H\u0002J\u0006\u0010\u001b\u001a\u00020\u0018J,\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0019\u001a\u00020\n2\b\b\u0002\u0010\u001e\u001a\u00020\u000b2\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u0014H\u0086@\u00a2\u0006\u0002\u0010 J\u0010\u0010!\u001a\u00020\"2\u0006\u0010\u001e\u001a\u00020\u000bH\u0002J\b\u0010#\u001a\u00020\"H\u0002J\u0012\u0010$\u001a\u000e\u0012\u0004\u0012\u00020\"\u0012\u0004\u0012\u00020\u00010\u0013J\b\u0010%\u001a\u00020\"H\u0002J\b\u0010&\u001a\u00020\"H\u0002J\u0010\u0010\'\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u0019\u001a\u00020\nJ\u0018\u0010(\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\nH\u0082@\u00a2\u0006\u0002\u0010)J\u0018\u0010*\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\nH\u0082@\u00a2\u0006\u0002\u0010)J\u001e\u0010+\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\n2\u0006\u0010\u001e\u001a\u00020\u000bH\u0082@\u00a2\u0006\u0002\u0010,J \u0010-\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\n2\u0006\u0010.\u001a\u00020\"H\u0082@\u00a2\u0006\u0002\u0010/J\u000e\u00100\u001a\u00020\u001d2\u0006\u0010\u0019\u001a\u00020\nJ\u000e\u00101\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\nJ\u0018\u00102\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\n2\u0006\u0010\u001e\u001a\u00020\u000bH\u0002J\u0018\u00103\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\n2\u0006\u0010\u001e\u001a\u00020\u000bH\u0002J\b\u00104\u001a\u00020\u0018H\u0002J \u00105\u001a\u00020\u00182\u0006\u00106\u001a\u0002072\u0006\u00108\u001a\u0002072\u0006\u00109\u001a\u000207H\u0002R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u001a\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00140\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006>"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_performanceMetrics", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$PerformanceMetrics;", "configuredWebViews", "Ljava/util/concurrent/ConcurrentHashMap;", "Landroid/webkit/WebView;", "Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$ConfigurationProfile;", "isConfiguring", "Ljava/util/concurrent/atomic/AtomicBoolean;", "performanceMetrics", "Lkotlinx/coroutines/flow/StateFlow;", "getPerformanceMetrics", "()Lkotlinx/coroutines/flow/StateFlow;", "predefinedConfigs", "", "Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$WebViewConfig;", "scope", "Lkotlinx/coroutines/CoroutineScope;", "applyBasicConfiguration", "", "webView", "config", "cleanup", "configureWebView", "", "profile", "customConfig", "(Landroid/webkit/WebView;Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$ConfigurationProfile;Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$WebViewConfig;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getBaseDetectionScript", "", "getBatterySaverScript", "getConfigurationStats", "getEnhancedDetectionScript", "getPerformanceOptimizedScript", "getWebViewProfile", "injectEarlyDetectionScript", "(Landroid/webkit/WebView;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "injectFinalDetectionScript", "injectOptimizedJavaScript", "(Landroid/webkit/WebView;Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$ConfigurationProfile;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "injectProgressiveScript", "stage", "(Landroid/webkit/WebView;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isWebViewConfigured", "removeWebView", "setupWebChromeClient", "setupWebViewClient", "startPeriodicCleanup", "updatePerformanceMetrics", "configTime", "", "jsTime", "totalTime", "Companion", "ConfigurationProfile", "PerformanceMetrics", "WebViewConfig", "app_debug"})
public final class WebViewConfigurationManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    private static final long JS_INJECTION_TIMEOUT_MS = 1000L;
    private static final long CONFIGURATION_TIMEOUT_MS = 2000L;
    private static final long MEMORY_CLEANUP_INTERVAL_MS = 60000L;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.anonymous.videodownloaderapp.services.WebViewConfigurationManager instance;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope scope = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<android.webkit.WebView, com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.ConfigurationProfile> configuredWebViews = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicBoolean isConfiguring = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.PerformanceMetrics> _performanceMetrics = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.PerformanceMetrics> performanceMetrics = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.ConfigurationProfile, com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.WebViewConfig> predefinedConfigs = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.Companion Companion = null;
    
    private WebViewConfigurationManager(android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.PerformanceMetrics> getPerformanceMetrics() {
        return null;
    }
    
    /**
     * Configurar WebView com perfil específico
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object configureWebView(@org.jetbrains.annotations.NotNull()
    android.webkit.WebView webView, @org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.ConfigurationProfile profile, @org.jetbrains.annotations.Nullable()
    com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.WebViewConfig customConfig, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Aplicar configurações básicas do WebView
     */
    private final void applyBasicConfiguration(android.webkit.WebView webView, com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.WebViewConfig config) {
    }
    
    /**
     * Configurar WebViewClient personalizado
     */
    private final void setupWebViewClient(android.webkit.WebView webView, com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.ConfigurationProfile profile) {
    }
    
    /**
     * Configurar WebChromeClient personalizado
     */
    private final void setupWebChromeClient(android.webkit.WebView webView, com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.ConfigurationProfile profile) {
    }
    
    /**
     * Injetar JavaScript otimizado
     */
    private final java.lang.Object injectOptimizedJavaScript(android.webkit.WebView webView, com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.ConfigurationProfile profile, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Script base de detecção
     */
    private final java.lang.String getBaseDetectionScript(com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.ConfigurationProfile profile) {
        return null;
    }
    
    /**
     * Script otimizado para performance
     */
    private final java.lang.String getPerformanceOptimizedScript() {
        return null;
    }
    
    /**
     * Script de detecção aprimorada
     */
    private final java.lang.String getEnhancedDetectionScript() {
        return null;
    }
    
    /**
     * Script para economia de bateria
     */
    private final java.lang.String getBatterySaverScript() {
        return null;
    }
    
    /**
     * Injetar script de detecção precoce
     */
    private final java.lang.Object injectEarlyDetectionScript(android.webkit.WebView webView, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Injetar script de detecção final
     */
    private final java.lang.Object injectFinalDetectionScript(android.webkit.WebView webView, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Injetar script progressivo
     */
    private final java.lang.Object injectProgressiveScript(android.webkit.WebView webView, java.lang.String stage, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Atualizar métricas de performance
     */
    private final void updatePerformanceMetrics(long configTime, long jsTime, long totalTime) {
    }
    
    /**
     * Limpeza periódica
     */
    private final void startPeriodicCleanup() {
    }
    
    /**
     * Obter configuração atual de uma WebView
     */
    @org.jetbrains.annotations.Nullable()
    public final com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.ConfigurationProfile getWebViewProfile(@org.jetbrains.annotations.NotNull()
    android.webkit.WebView webView) {
        return null;
    }
    
    /**
     * Verificar se WebView está configurada
     */
    public final boolean isWebViewConfigured(@org.jetbrains.annotations.NotNull()
    android.webkit.WebView webView) {
        return false;
    }
    
    /**
     * Remover WebView do gerenciamento
     */
    public final void removeWebView(@org.jetbrains.annotations.NotNull()
    android.webkit.WebView webView) {
    }
    
    /**
     * Obter estatísticas de configuração
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getConfigurationStats() {
        return null;
    }
    
    /**
     * Limpeza de recursos
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\t\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$Companion;", "", "()V", "CONFIGURATION_TIMEOUT_MS", "", "JS_INJECTION_TIMEOUT_MS", "MEMORY_CLEANUP_INTERVAL_MS", "instance", "Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager;", "getInstance", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.WebViewConfigurationManager getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$ConfigurationProfile;", "", "(Ljava/lang/String;I)V", "PERFORMANCE_OPTIMIZED", "VIDEO_DETECTION_FOCUSED", "BATTERY_SAVER", "COMPATIBILITY_MODE", "CUSTOM", "app_debug"})
    public static enum ConfigurationProfile {
        /*public static final*/ PERFORMANCE_OPTIMIZED /* = new PERFORMANCE_OPTIMIZED() */,
        /*public static final*/ VIDEO_DETECTION_FOCUSED /* = new VIDEO_DETECTION_FOCUSED() */,
        /*public static final*/ BATTERY_SAVER /* = new BATTERY_SAVER() */,
        /*public static final*/ COMPATIBILITY_MODE /* = new COMPATIBILITY_MODE() */,
        /*public static final*/ CUSTOM /* = new CUSTOM() */;
        
        ConfigurationProfile() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.ConfigurationProfile> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\bH\u00c6\u0003J;\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\bH\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\bH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000bR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000bR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u001d"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$PerformanceMetrics;", "", "configurationTime", "", "jsInjectionTime", "totalSetupTime", "memoryUsage", "webViewCount", "", "(JJJJI)V", "getConfigurationTime", "()J", "getJsInjectionTime", "getMemoryUsage", "getTotalSetupTime", "getWebViewCount", "()I", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
    public static final class PerformanceMetrics {
        private final long configurationTime = 0L;
        private final long jsInjectionTime = 0L;
        private final long totalSetupTime = 0L;
        private final long memoryUsage = 0L;
        private final int webViewCount = 0;
        
        public PerformanceMetrics(long configurationTime, long jsInjectionTime, long totalSetupTime, long memoryUsage, int webViewCount) {
            super();
        }
        
        public final long getConfigurationTime() {
            return 0L;
        }
        
        public final long getJsInjectionTime() {
            return 0L;
        }
        
        public final long getTotalSetupTime() {
            return 0L;
        }
        
        public final long getMemoryUsage() {
            return 0L;
        }
        
        public final int getWebViewCount() {
            return 0;
        }
        
        public final long component1() {
            return 0L;
        }
        
        public final long component2() {
            return 0L;
        }
        
        public final long component3() {
            return 0L;
        }
        
        public final long component4() {
            return 0L;
        }
        
        public final int component5() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.PerformanceMetrics copy(long configurationTime, long jsInjectionTime, long totalSetupTime, long memoryUsage, int webViewCount) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\t\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b5\b\u0086\b\u0018\u00002\u00020\u0001B\u00b9\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\u0003\u0012\b\b\u0002\u0010\n\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\r\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0017J\t\u0010-\u001a\u00020\u0003H\u00c6\u0003J\t\u0010.\u001a\u00020\rH\u00c6\u0003J\t\u0010/\u001a\u00020\u000fH\u00c6\u0003J\t\u00100\u001a\u00020\u0003H\u00c6\u0003J\t\u00101\u001a\u00020\u0003H\u00c6\u0003J\t\u00102\u001a\u00020\u0003H\u00c6\u0003J\t\u00103\u001a\u00020\u0003H\u00c6\u0003J\t\u00104\u001a\u00020\u0003H\u00c6\u0003J\t\u00105\u001a\u00020\u0003H\u00c6\u0003J\t\u00106\u001a\u00020\u0003H\u00c6\u0003J\t\u00107\u001a\u00020\u0003H\u00c6\u0003J\t\u00108\u001a\u00020\u0003H\u00c6\u0003J\t\u00109\u001a\u00020\u0003H\u00c6\u0003J\t\u0010:\u001a\u00020\u0003H\u00c6\u0003J\t\u0010;\u001a\u00020\u0003H\u00c6\u0003J\t\u0010<\u001a\u00020\u0003H\u00c6\u0003J\t\u0010=\u001a\u00020\u0003H\u00c6\u0003J\t\u0010>\u001a\u00020\u0003H\u00c6\u0003J\u00bd\u0001\u0010?\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u00032\b\b\u0002\u0010\u0011\u001a\u00020\u00032\b\b\u0002\u0010\u0012\u001a\u00020\u00032\b\b\u0002\u0010\u0013\u001a\u00020\u00032\b\b\u0002\u0010\u0014\u001a\u00020\u00032\b\b\u0002\u0010\u0015\u001a\u00020\u00032\b\b\u0002\u0010\u0016\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010@\u001a\u00020\u00032\b\u0010A\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010B\u001a\u00020\rH\u00d6\u0001J\t\u0010C\u001a\u00020\u000fH\u00d6\u0001R\u0011\u0010\u0012\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0011\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0019R\u0011\u0010\u0013\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0019R\u0011\u0010\u0014\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0019R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0019R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0019R\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0019R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0019R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0019R\u0011\u0010\u0016\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u0019R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0019R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u0019R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u0019R\u0011\u0010\u0015\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u0019R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u0019R\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u0019R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010,\u00a8\u0006D"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$WebViewConfig;", "", "enableJavaScript", "", "enableDomStorage", "enableFileAccess", "enableContentAccess", "enableZoom", "enableBuiltInZoomControls", "enableDisplayZoomControls", "enableMixedContent", "enableSafeBrowsing", "cacheMode", "", "userAgentSuffix", "", "mediaPlaybackRequiresUserGesture", "allowUniversalAccessFromFileURLs", "allowFileAccessFromFileURLs", "blockNetworkImage", "blockNetworkLoads", "enableSmoothTransition", "enableHardwareAcceleration", "(ZZZZZZZZZILjava/lang/String;ZZZZZZZ)V", "getAllowFileAccessFromFileURLs", "()Z", "getAllowUniversalAccessFromFileURLs", "getBlockNetworkImage", "getBlockNetworkLoads", "getCacheMode", "()I", "getEnableBuiltInZoomControls", "getEnableContentAccess", "getEnableDisplayZoomControls", "getEnableDomStorage", "getEnableFileAccess", "getEnableHardwareAcceleration", "getEnableJavaScript", "getEnableMixedContent", "getEnableSafeBrowsing", "getEnableSmoothTransition", "getEnableZoom", "getMediaPlaybackRequiresUserGesture", "getUserAgentSuffix", "()Ljava/lang/String;", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class WebViewConfig {
        private final boolean enableJavaScript = false;
        private final boolean enableDomStorage = false;
        private final boolean enableFileAccess = false;
        private final boolean enableContentAccess = false;
        private final boolean enableZoom = false;
        private final boolean enableBuiltInZoomControls = false;
        private final boolean enableDisplayZoomControls = false;
        private final boolean enableMixedContent = false;
        private final boolean enableSafeBrowsing = false;
        private final int cacheMode = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String userAgentSuffix = null;
        private final boolean mediaPlaybackRequiresUserGesture = false;
        private final boolean allowUniversalAccessFromFileURLs = false;
        private final boolean allowFileAccessFromFileURLs = false;
        private final boolean blockNetworkImage = false;
        private final boolean blockNetworkLoads = false;
        private final boolean enableSmoothTransition = false;
        private final boolean enableHardwareAcceleration = false;
        
        public WebViewConfig(boolean enableJavaScript, boolean enableDomStorage, boolean enableFileAccess, boolean enableContentAccess, boolean enableZoom, boolean enableBuiltInZoomControls, boolean enableDisplayZoomControls, boolean enableMixedContent, boolean enableSafeBrowsing, int cacheMode, @org.jetbrains.annotations.NotNull()
        java.lang.String userAgentSuffix, boolean mediaPlaybackRequiresUserGesture, boolean allowUniversalAccessFromFileURLs, boolean allowFileAccessFromFileURLs, boolean blockNetworkImage, boolean blockNetworkLoads, boolean enableSmoothTransition, boolean enableHardwareAcceleration) {
            super();
        }
        
        public final boolean getEnableJavaScript() {
            return false;
        }
        
        public final boolean getEnableDomStorage() {
            return false;
        }
        
        public final boolean getEnableFileAccess() {
            return false;
        }
        
        public final boolean getEnableContentAccess() {
            return false;
        }
        
        public final boolean getEnableZoom() {
            return false;
        }
        
        public final boolean getEnableBuiltInZoomControls() {
            return false;
        }
        
        public final boolean getEnableDisplayZoomControls() {
            return false;
        }
        
        public final boolean getEnableMixedContent() {
            return false;
        }
        
        public final boolean getEnableSafeBrowsing() {
            return false;
        }
        
        public final int getCacheMode() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUserAgentSuffix() {
            return null;
        }
        
        public final boolean getMediaPlaybackRequiresUserGesture() {
            return false;
        }
        
        public final boolean getAllowUniversalAccessFromFileURLs() {
            return false;
        }
        
        public final boolean getAllowFileAccessFromFileURLs() {
            return false;
        }
        
        public final boolean getBlockNetworkImage() {
            return false;
        }
        
        public final boolean getBlockNetworkLoads() {
            return false;
        }
        
        public final boolean getEnableSmoothTransition() {
            return false;
        }
        
        public final boolean getEnableHardwareAcceleration() {
            return false;
        }
        
        public WebViewConfig() {
            super();
        }
        
        public final boolean component1() {
            return false;
        }
        
        public final int component10() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component11() {
            return null;
        }
        
        public final boolean component12() {
            return false;
        }
        
        public final boolean component13() {
            return false;
        }
        
        public final boolean component14() {
            return false;
        }
        
        public final boolean component15() {
            return false;
        }
        
        public final boolean component16() {
            return false;
        }
        
        public final boolean component17() {
            return false;
        }
        
        public final boolean component18() {
            return false;
        }
        
        public final boolean component2() {
            return false;
        }
        
        public final boolean component3() {
            return false;
        }
        
        public final boolean component4() {
            return false;
        }
        
        public final boolean component5() {
            return false;
        }
        
        public final boolean component6() {
            return false;
        }
        
        public final boolean component7() {
            return false;
        }
        
        public final boolean component8() {
            return false;
        }
        
        public final boolean component9() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.WebViewConfig copy(boolean enableJavaScript, boolean enableDomStorage, boolean enableFileAccess, boolean enableContentAccess, boolean enableZoom, boolean enableBuiltInZoomControls, boolean enableDisplayZoomControls, boolean enableMixedContent, boolean enableSafeBrowsing, int cacheMode, @org.jetbrains.annotations.NotNull()
        java.lang.String userAgentSuffix, boolean mediaPlaybackRequiresUserGesture, boolean allowUniversalAccessFromFileURLs, boolean allowFileAccessFromFileURLs, boolean blockNetworkImage, boolean blockNetworkLoads, boolean enableSmoothTransition, boolean enableHardwareAcceleration) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}
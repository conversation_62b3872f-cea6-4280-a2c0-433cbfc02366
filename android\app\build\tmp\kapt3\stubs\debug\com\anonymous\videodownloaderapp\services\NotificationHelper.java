package com.anonymous.videodownloaderapp.services;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fJ=\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\u00042\b\u0010\u0014\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018\u00a2\u0006\u0002\u0010\u0019J\b\u0010\u001a\u001a\u00020\u000bH\u0002J\u001e\u0010\u001b\u001a\u00020\u00112\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NotificationHelper;", "", "()V", "ACTION_CANCEL", "", "ACTION_PAUSE", "ACTION_RESUME", "CHANNEL_DOWNLOADS_DESC", "CHANNEL_DOWNLOADS_ID", "CHANNEL_DOWNLOADS_NAME", "NOTIFICATION_ID_FOREGROUND", "", "ensureChannels", "", "context", "Landroid/content/Context;", "foregroundNotification", "Landroid/app/Notification;", "title", "text", "progress", "isPaused", "", "downloadId", "", "(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;ZJ)Landroid/app/Notification;", "pendingIntentFlags", "simpleCompleted", "app_debug"})
public final class NotificationHelper {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CHANNEL_DOWNLOADS_ID = "downloads_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CHANNEL_DOWNLOADS_NAME = "Downloads";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CHANNEL_DOWNLOADS_DESC = "Active and completed downloads";
    public static final int NOTIFICATION_ID_FOREGROUND = 1001;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_PAUSE = "com.anonymous.videodownloaderapp.action.PAUSE";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_RESUME = "com.anonymous.videodownloaderapp.action.RESUME";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_CANCEL = "com.anonymous.videodownloaderapp.action.CANCEL";
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.services.NotificationHelper INSTANCE = null;
    
    private NotificationHelper() {
        super();
    }
    
    public final void ensureChannels(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.app.Notification foregroundNotification(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.Nullable()
    java.lang.Integer progress, boolean isPaused, long downloadId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.app.Notification simpleCompleted(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String text) {
        return null;
    }
    
    private final int pendingIntentFlags() {
        return 0;
    }
}
package com.anonymous.videodownloaderapp.services

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import com.anonymous.videodownloaderapp.data.DownloadItem
import kotlinx.coroutines.*
import okhttp3.*
import okio.buffer
import okio.sink
import java.io.File
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap
import kotlin.coroutines.cancellation.CancellationException
import java.util.concurrent.TimeUnit
import kotlin.math.pow
import kotlin.random.Random

class EnhancedDownloadService(private val context: Context) {
    
    // Connection pool for efficient connection reuse
    private val connectionPool = ConnectionPool(10, 5, TimeUnit.MINUTES)
    
    // HTTP client with optimized configuration
    private val httpClient = run {
        val builder = OkHttpClient.Builder()
            .connectionPool(connectionPool)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .retryOnConnectionFailure(false) // We'll handle retries manually
        
        // Configurar com HybridVideoDetectionService (inclui interceptor de rede)
        HybridVideoDetectionService.getInstance(context).configureOkHttpClient(builder)
        
        builder.build()
    }
    
    // Memory pool for buffer reuse
    private val bufferPool = BufferPool(8 * 1024) // 8KB buffers
    
    // Download queue management
    private val downloadQueue = ConcurrentHashMap<String, DownloadTask>()
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Retry configuration
    private val maxRetries = 3
    private val baseRetryDelay = 1000L // 1 second
    
    data class DownloadTask(
        val downloadItem: DownloadItem,
        var retryCount: Int = 0,
        var isPaused: Boolean = false,
        var bytesDownloaded: Long = 0,
        var totalBytes: Long = 0
    )
    
    fun startDownload(downloadItem: DownloadItem) {
        coroutineScope.launch {
            val task = DownloadTask(downloadItem)
            downloadQueue[downloadItem.id.toString()] = task
            executeDownload(task)
        }
    }
    
    private suspend fun executeDownload(task: DownloadTask) {
        val downloadItem = task.downloadItem
        
        try {
            // Check network connectivity
            if (!isNetworkAvailable()) {
                handleError(task, "Network not available")
                return
            }
            
            // Check storage space
            if (!hasSufficientStorage(downloadItem.totalBytes)) {
                handleError(task, "Insufficient storage space")
                return
            }
            
            // Create temporary file
            val tempFile = File(context.cacheDir, "temp_${downloadItem.title}")
            val finalFile = File(context.getExternalFilesDir(null), downloadItem.title)
            
            // Execute download with retry logic
            downloadWithRetry(task, tempFile, finalFile)
            
        } catch (e: Exception) {
            handleError(task, e.message ?: "Unknown error")
        }
    }
    
    private suspend fun downloadWithRetry(task: DownloadTask, tempFile: File, finalFile: File) {
        while (task.retryCount <= maxRetries) {
            try {
                performDownload(task, tempFile, finalFile)
                return // Success
            } catch (e: IOException) {
                task.retryCount++
                if (task.retryCount > maxRetries) {
                    throw e
                }
                
                // Exponential backoff with jitter
                val delay = baseRetryDelay * 2.0.pow(task.retryCount - 1).toLong() + Random.nextLong(0, 1000)
                delay(delay)
            }
        }
    }
    
    private suspend fun performDownload(task: DownloadTask, tempFile: File, finalFile: File) {
        val downloadItem = task.downloadItem
        
        val request = Request.Builder()
            .url(downloadItem.url)
            .header("Range", "bytes=${task.bytesDownloaded}-") // Resume capability
            .build()
        
        httpClient.newCall(request).execute().use { response ->
            if (!response.isSuccessful && response.code != 206) {
                throw IOException("HTTP ${response.code}: ${response.message}")
            }
            
            val body = response.body ?: throw IOException("Empty response body")
            val contentLength = body.contentLength()
            if (contentLength != -1L) {
                task.totalBytes = contentLength
            }
            
            // Use buffered output with memory pool
            val sink = tempFile.sink().buffer()
            val buffer = bufferPool.acquire()
            
            try {
                var bytesRead: Int
                while (body.source().read(buffer).also { bytesRead = it } != -1) {
                    if (task.isPaused) {
                        throw CancellationException("Download paused")
                    }
                    
                    sink.write(buffer, 0, bytesRead)
                    task.bytesDownloaded += bytesRead
                    
                    // Update progress
                    val progress = if (task.totalBytes > 0) {
                        (task.bytesDownloaded * 100 / task.totalBytes).toInt()
                    } else 0
                    
                    updateProgress(downloadItem, progress, task.bytesDownloaded, task.totalBytes)
                }
                
                sink.close()
                
                // Move to final location
                tempFile.renameTo(finalFile)
                markComplete(downloadItem, finalFile)
                
            } finally {
                bufferPool.release(buffer)
            }
        }
    }
    
    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            networkInfo?.isConnected == true
        }
    }
    
    private fun hasSufficientStorage(requiredBytes: Long): Boolean {
        val stats = context.getExternalFilesDir(null)?.let { dir ->
            android.os.StatFs(dir.path).let { stat ->
                val availableBytes = stat.availableBytes
                availableBytes > requiredBytes * 1.2 // 20% buffer
            }
        } ?: false
        return stats
    }
    
    private fun updateProgress(downloadItem: DownloadItem, progress: Int, bytesDownloaded: Long, totalBytes: Long) {
        // Update download progress in database/tracker
        val updatedItem = downloadItem.copy(
            progress = progress,
            bytesDownloaded = bytesDownloaded,
            totalBytes = totalBytes,
            status = DownloadItem.Status.Running,
            updatedAt = System.currentTimeMillis()
        )
        
        // Notify through DownloadTracker
        coroutineScope.launch { DownloadTracker(context).updateDownload(updatedItem) }
    }
    
    private fun markComplete(downloadItem: DownloadItem, file: File) {
        val updatedItem = downloadItem.copy(
            progress = 100,
            bytesDownloaded = file.length(),
            totalBytes = file.length(),
            status = DownloadItem.Status.Completed,
            updatedAt = System.currentTimeMillis()
        )
        
        coroutineScope.launch { DownloadTracker(context).updateDownload(updatedItem) }
    }
    
    private fun handleError(task: DownloadTask, errorMessage: String) {
        val updatedItem = task.downloadItem.copy(
            status = DownloadItem.Status.Failed,
            error = errorMessage,
            updatedAt = System.currentTimeMillis()
        )
        
        coroutineScope.launch { DownloadTracker(context).updateDownload(updatedItem) }
        downloadQueue.remove(task.downloadItem.id.toString())
    }
    
    fun pauseDownload(downloadId: String) {
        downloadQueue[downloadId]?.isPaused = true
    }
    
    fun resumeDownload(downloadId: String) {
        downloadQueue[downloadId]?.let { task ->
            task.isPaused = false
            coroutineScope.launch {
                executeDownload(task)
            }
        }
    }
    
    fun cancelDownload(downloadId: String) {
        downloadQueue.remove(downloadId)
    }
    
    // Cleanup resources
    fun shutdown() {
        coroutineScope.cancel()
        connectionPool.evictAll()
        httpClient.dispatcher.executorService.shutdown()
    }
}

// Buffer pool for memory reuse
class BufferPool(private val bufferSize: Int) {
    private val pool = ArrayDeque<ByteArray>()
    
    fun acquire(): ByteArray {
        return pool.removeFirstOrNull() ?: ByteArray(bufferSize)
    }
    
    fun release(buffer: ByteArray) {
        if (pool.size < 10) { // Limit pool size
            pool.addFirst(buffer)
        }
    }
}
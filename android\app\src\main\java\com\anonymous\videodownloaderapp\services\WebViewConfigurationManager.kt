package com.anonymous.videodownloaderapp.services

import android.content.Context
import android.os.Build
import android.webkit.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Gerenciador de Configuração de WebView Otimizado
 * Configurações específicas para detecção de vídeo e performance
 */
class WebViewConfigurationManager private constructor(private val context: Context) {

    data class WebViewConfig(
        val enableJavaScript: Boolean = true,
        val enableDomStorage: Boolean = false,
        val enableFileAccess: Boolean = false,
        val enableContentAccess: Boolean = false,
        val enableZoom: Boolean = false,
        val enableBuiltInZoomControls: Boolean = false,
        val enableDisplayZoomControls: Boolean = false,
        val enableMixedContent: Boolean = true,
        val enableSafeBrowsing: Boolean = false,
        val cacheMode: Int = WebSettings.LOAD_DEFAULT,
        val userAgentSuffix: String = "VideoDetector/1.0",
        val mediaPlaybackRequiresUserGesture: Boolean = false,
        val allowUniversalAccessFromFileURLs: Boolean = false,
        val allowFileAccessFromFileURLs: Boolean = false,
        val blockNetworkImage: Boolean = false,
        val blockNetworkLoads: Boolean = false,
        val enableSmoothTransition: Boolean = true,
        val enableHardwareAcceleration: Boolean = true
    )

    data class PerformanceMetrics(
        val configurationTime: Long,
        val jsInjectionTime: Long,
        val totalSetupTime: Long,
        val memoryUsage: Long,
        val webViewCount: Int
    )

    enum class ConfigurationProfile {
        PERFORMANCE_OPTIMIZED,
        VIDEO_DETECTION_FOCUSED,
        BATTERY_SAVER,
        COMPATIBILITY_MODE,
        CUSTOM
    }

    companion object {
        private const val JS_INJECTION_TIMEOUT_MS = 1000L
        private const val CONFIGURATION_TIMEOUT_MS = 2000L
        private const val MEMORY_CLEANUP_INTERVAL_MS = 60000L // 1 minuto
        
        @Volatile
        private var instance: WebViewConfigurationManager? = null
        
        fun getInstance(context: Context): WebViewConfigurationManager {
            return instance ?: synchronized(this) {
                instance ?: WebViewConfigurationManager(context.applicationContext).also {
                    instance = it
                }
            }
        }
    }

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private val configuredWebViews = ConcurrentHashMap<WebView, ConfigurationProfile>()
    private val isConfiguring = AtomicBoolean(false)
    
    // Estado e métricas
    private val _performanceMetrics = MutableStateFlow(
        PerformanceMetrics(
            configurationTime = 0,
            jsInjectionTime = 0,
            totalSetupTime = 0,
            memoryUsage = 0,
            webViewCount = 0
        )
    )
    val performanceMetrics: StateFlow<PerformanceMetrics> = _performanceMetrics
    
    // Configurações predefinidas
    private val predefinedConfigs = mapOf(
        ConfigurationProfile.PERFORMANCE_OPTIMIZED to WebViewConfig(
            enableJavaScript = true,
            enableDomStorage = false,
            enableFileAccess = false,
            enableContentAccess = false,
            enableZoom = false,
            enableBuiltInZoomControls = false,
            enableDisplayZoomControls = false,
            enableMixedContent = true,
            enableSafeBrowsing = false,
            cacheMode = WebSettings.LOAD_CACHE_ELSE_NETWORK,
            userAgentSuffix = "VideoDetector/1.0 Performance",
            mediaPlaybackRequiresUserGesture = false,
            allowUniversalAccessFromFileURLs = false,
            allowFileAccessFromFileURLs = false,
            blockNetworkImage = true, // Bloquear imagens para performance
            blockNetworkLoads = false,
            enableSmoothTransition = false, // Desabilitar para performance
            enableHardwareAcceleration = true
        ),
        
        ConfigurationProfile.VIDEO_DETECTION_FOCUSED to WebViewConfig(
            enableJavaScript = true,
            enableDomStorage = false,
            enableFileAccess = false,
            enableContentAccess = false,
            enableZoom = false,
            enableBuiltInZoomControls = false,
            enableDisplayZoomControls = false,
            enableMixedContent = true,
            enableSafeBrowsing = false,
            cacheMode = WebSettings.LOAD_DEFAULT,
            userAgentSuffix = "VideoDetector/1.0 Enhanced",
            mediaPlaybackRequiresUserGesture = false,
            allowUniversalAccessFromFileURLs = false,
            allowFileAccessFromFileURLs = false,
            blockNetworkImage = false,
            blockNetworkLoads = false,
            enableSmoothTransition = true,
            enableHardwareAcceleration = true
        ),
        
        ConfigurationProfile.BATTERY_SAVER to WebViewConfig(
            enableJavaScript = true,
            enableDomStorage = false,
            enableFileAccess = false,
            enableContentAccess = false,
            enableZoom = false,
            enableBuiltInZoomControls = false,
            enableDisplayZoomControls = false,
            enableMixedContent = false,
            enableSafeBrowsing = false,
            cacheMode = WebSettings.LOAD_CACHE_ELSE_NETWORK,
            userAgentSuffix = "VideoDetector/1.0 BatterySaver",
            mediaPlaybackRequiresUserGesture = true,
            allowUniversalAccessFromFileURLs = false,
            allowFileAccessFromFileURLs = false,
            blockNetworkImage = true,
            blockNetworkLoads = false,
            enableSmoothTransition = false,
            enableHardwareAcceleration = false // Desabilitar para economizar bateria
        ),
        
        ConfigurationProfile.COMPATIBILITY_MODE to WebViewConfig(
            enableJavaScript = true,
            enableDomStorage = true,
            enableFileAccess = true,
            enableContentAccess = true,
            enableZoom = true,
            enableBuiltInZoomControls = true,
            enableDisplayZoomControls = false,
            enableMixedContent = true,
            enableSafeBrowsing = true,
            cacheMode = WebSettings.LOAD_DEFAULT,
            userAgentSuffix = "VideoDetector/1.0 Compatible",
            mediaPlaybackRequiresUserGesture = false,
            allowUniversalAccessFromFileURLs = true,
            allowFileAccessFromFileURLs = true,
            blockNetworkImage = false,
            blockNetworkLoads = false,
            enableSmoothTransition = true,
            enableHardwareAcceleration = true
        )
    )

    init {
        startPeriodicCleanup()
    }

    /**
     * Configurar WebView com perfil específico
     */
    suspend fun configureWebView(
        webView: WebView,
        profile: ConfigurationProfile = ConfigurationProfile.VIDEO_DETECTION_FOCUSED,
        customConfig: WebViewConfig? = null
    ): Boolean = withContext(Dispatchers.Main) {
        
        if (isConfiguring.get()) {
            return@withContext false
        }
        
        isConfiguring.set(true)
        val startTime = System.currentTimeMillis()
        
        try {
            val config = customConfig ?: predefinedConfigs[profile] ?: predefinedConfigs[ConfigurationProfile.VIDEO_DETECTION_FOCUSED]!!
            
            // Aplicar configurações básicas
            val configStartTime = System.currentTimeMillis()
            applyBasicConfiguration(webView, config)
            val configTime = System.currentTimeMillis() - configStartTime
            
            // Configurar cliente personalizado
            setupWebViewClient(webView, profile)
            setupWebChromeClient(webView, profile)
            
            // Injetar JavaScript otimizado
            val jsStartTime = System.currentTimeMillis()
            injectOptimizedJavaScript(webView, profile)
            val jsTime = System.currentTimeMillis() - jsStartTime
            
            // Registrar WebView configurada
            configuredWebViews[webView] = profile
            
            // Atualizar métricas
            val totalTime = System.currentTimeMillis() - startTime
            updatePerformanceMetrics(configTime, jsTime, totalTime)
            
            return@withContext true
            
        } catch (e: Exception) {
            return@withContext false
        } finally {
            isConfiguring.set(false)
        }
    }

    /**
     * Aplicar configurações básicas do WebView
     */
    private fun applyBasicConfiguration(webView: WebView, config: WebViewConfig) {
        webView.settings.apply {
            // JavaScript
            javaScriptEnabled = config.enableJavaScript
            javaScriptCanOpenWindowsAutomatically = false
            
            // Storage
            domStorageEnabled = config.enableDomStorage
            databaseEnabled = false
            
            // Acesso a arquivos
            allowFileAccess = config.enableFileAccess
            allowContentAccess = config.enableContentAccess
            
            // Zoom
            setSupportZoom(config.enableZoom)
            builtInZoomControls = config.enableBuiltInZoomControls
            displayZoomControls = config.enableDisplayZoomControls
            
            // Conteúdo misto
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                mixedContentMode = if (config.enableMixedContent) {
                    WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                } else {
                    WebSettings.MIXED_CONTENT_NEVER_ALLOW
                }
            }
            
            // Safe Browsing
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                safeBrowsingEnabled = config.enableSafeBrowsing
            }
            
            // Cache
            cacheMode = config.cacheMode
            // setAppCacheEnabled removed on modern WebView; ensure not called
            // Try/catch to avoid NoSuchMethodError on some devices
            try {
                @Suppress("DEPRECATION")
                this.javaClass.getMethod("setAppCacheEnabled", Boolean::class.javaPrimitiveType).let { m ->
                    m.invoke(this, false)
                }
            } catch (e: Exception) {
                // Ignore: method not available
            }

            // User Agent
            userAgentString = "$userAgentString ${config.userAgentSuffix}"

            // Media
            mediaPlaybackRequiresUserGesture = config.mediaPlaybackRequiresUserGesture

            // Acesso universal
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                allowUniversalAccessFromFileURLs = config.allowUniversalAccessFromFileURLs
                allowFileAccessFromFileURLs = config.allowFileAccessFromFileURLs
            }

            // Bloqueio de recursos
            blockNetworkImage = config.blockNetworkImage
            blockNetworkLoads = config.blockNetworkLoads

            // Performance
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                layoutAlgorithm = WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING
            }

            // Renderização
            try {
                @Suppress("DEPRECATION")
                setRenderPriority(WebSettings.RenderPriority.HIGH)
            } catch (e: Exception) {
                // Ignore on modern WebView where it's a no-op/removed
            }

            // Configurações específicas do Android
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // Configurações adicionais para Android 5.0+
            }
        }
        
        // Hardware acceleration
        if (config.enableHardwareAcceleration) {
            webView.setLayerType(WebView.LAYER_TYPE_HARDWARE, null)
        } else {
            webView.setLayerType(WebView.LAYER_TYPE_SOFTWARE, null)
        }
    }

    /**
     * Configurar WebViewClient personalizado
     */
    private fun setupWebViewClient(webView: WebView, profile: ConfigurationProfile) {
        webView.webViewClient = object : WebViewClient() {
            
            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                // Permitir navegação normal
                return false
            }
            
            override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                super.onPageStarted(view, url, favicon)
                
                // Injetar JavaScript assim que a página começar a carregar
                if (profile == ConfigurationProfile.VIDEO_DETECTION_FOCUSED || 
                    profile == ConfigurationProfile.PERFORMANCE_OPTIMIZED) {
                    
                    scope.launch {
                        delay(100) // Pequeno delay para garantir que a página iniciou
                        injectEarlyDetectionScript(view)
                    }
                }
            }
            
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                
                // Injetar scripts finais
                scope.launch {
                    injectFinalDetectionScript(view)
                }
            }
            
            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
                
                // Log de erros para debugging
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    android.util.Log.w("WebViewConfig", "Error: ${error?.description}")
                }
            }
        }
    }

    /**
     * Configurar WebChromeClient personalizado
     */
    private fun setupWebChromeClient(webView: WebView, profile: ConfigurationProfile) {
        webView.webChromeClient = object : WebChromeClient() {
            
            override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
                // Capturar mensagens do console para debugging
                consoleMessage?.let { msg ->
                    when (msg.messageLevel()) {
                        ConsoleMessage.MessageLevel.ERROR -> {
                            android.util.Log.e("WebViewConsole", "${msg.sourceId()}:${msg.lineNumber()} - ${msg.message()}")
                        }
                        ConsoleMessage.MessageLevel.WARNING -> {
                            android.util.Log.w("WebViewConsole", "${msg.sourceId()}:${msg.lineNumber()} - ${msg.message()}")
                        }
                        else -> {
                            android.util.Log.d("WebViewConsole", "${msg.sourceId()}:${msg.lineNumber()} - ${msg.message()}")
                        }
                    }
                }
                return true
            }
            
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                
                // Injetar scripts em momentos específicos do carregamento
                when (newProgress) {
                    25 -> {
                        // Injeção precoce
                        scope.launch {
                            injectProgressiveScript(view, "early")
                        }
                    }
                    75 -> {
                        // Injeção tardia
                        scope.launch {
                            injectProgressiveScript(view, "late")
                        }
                    }
                }
            }
        }
    }

    /**
     * Injetar JavaScript otimizado
     */
    private suspend fun injectOptimizedJavaScript(webView: WebView, profile: ConfigurationProfile) {
        withTimeoutOrNull(JS_INJECTION_TIMEOUT_MS) {
            withContext(Dispatchers.Main) {
                try {
                    val baseScript = getBaseDetectionScript(profile)
                    webView.evaluateJavascript(baseScript, null)
                    
                    // Scripts específicos do perfil
                    when (profile) {
                        ConfigurationProfile.PERFORMANCE_OPTIMIZED -> {
                            val performanceScript = getPerformanceOptimizedScript()
                            webView.evaluateJavascript(performanceScript, null)
                        }
                        ConfigurationProfile.VIDEO_DETECTION_FOCUSED -> {
                            val detectionScript = getEnhancedDetectionScript()
                            webView.evaluateJavascript(detectionScript, null)
                        }
                        ConfigurationProfile.BATTERY_SAVER -> {
                            val batterySaverScript = getBatterySaverScript()
                            webView.evaluateJavascript(batterySaverScript, null)
                        }
                        else -> {
                            // Script padrão
                        }
                    }
                    
                } catch (e: Exception) {
                    android.util.Log.e("WebViewConfig", "Erro ao injetar JavaScript", e)
                }
            }
        }
    }

    /**
     * Script base de detecção
     */
    private fun getBaseDetectionScript(profile: ConfigurationProfile): String {
        return """
            (function() {
                if (window.VideoDetectorConfig) return;
                
                window.VideoDetectorConfig = {
                    profile: '${profile.name}',
                    initialized: true,
                    startTime: performance.now(),
                    detectionCount: 0,
                    lastDetection: 0
                };
                
                // Configuração global
                window.VideoDetectorConfig.settings = {
                    maxCandidates: ${if (profile == ConfigurationProfile.PERFORMANCE_OPTIMIZED) 10 else 20},
                    minScore: ${if (profile == ConfigurationProfile.BATTERY_SAVER) 8 else 5},
                    debounceMs: ${if (profile == ConfigurationProfile.PERFORMANCE_OPTIMIZED) 500 else 300},
                    enablePerformanceAPI: ${profile != ConfigurationProfile.BATTERY_SAVER},
                    enableYouTubeSpecific: true,
                    enableHLSDetection: true,
                    enableDASHDetection: true
                };
                
                console.log('VideoDetector initialized with profile:', '${profile.name}');
            })();
        """.trimIndent()
    }

    /**
     * Script otimizado para performance
     */
    private fun getPerformanceOptimizedScript(): String {
        return """
            (function() {
                // Otimizações específicas para performance
                window.VideoDetectorConfig.performance = {
                    useRequestIdleCallback: true,
                    batchOperations: true,
                    limitDOMQueries: true,
                    cacheResults: true
                };
                
                // Throttle para operações pesadas
                window.VideoDetectorConfig.throttle = function(func, limit) {
                    let inThrottle;
                    return function() {
                        const args = arguments;
                        const context = this;
                        if (!inThrottle) {
                            func.apply(context, args);
                            inThrottle = true;
                            setTimeout(() => inThrottle = false, limit);
                        }
                    }
                };
            })();
        """.trimIndent()
    }

    /**
     * Script de detecção aprimorada
     */
    private fun getEnhancedDetectionScript(): String {
        return """
            (function() {
                // Configurações de detecção aprimorada
                window.VideoDetectorConfig.enhanced = {
                    deepScan: true,
                    qualityMapping: true,
                    formatDetection: true,
                    metadataExtraction: true
                };
                
                // Observer para mudanças no DOM
                if (window.MutationObserver) {
                    const observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'childList') {
                                mutation.addedNodes.forEach(function(node) {
                                    if (node.tagName === 'VIDEO' || node.tagName === 'SOURCE') {
                                        // Notificar sobre novo elemento de vídeo
                                        if (window.OptimizedVideoDetector) {
                                            window.OptimizedVideoDetector.reportCandidates(
                                                JSON.stringify({candidates: [], newVideoElement: true})
                                            );
                                        }
                                    }
                                });
                            }
                        });
                    });
                    
                    observer.observe(document.body, {
                        childList: true,
                        subtree: true
                    });
                }
            })();
        """.trimIndent()
    }

    /**
     * Script para economia de bateria
     */
    private fun getBatterySaverScript(): String {
        return """
            (function() {
                // Configurações para economia de bateria
                window.VideoDetectorConfig.batterySaver = {
                    reducedFrequency: true,
                    limitedScanning: true,
                    disableAnimations: true,
                    minimalLogging: true
                };
                
                // Reduzir frequência de operações
                const originalSetInterval = window.setInterval;
                window.setInterval = function(callback, delay) {
                    return originalSetInterval(callback, Math.max(delay * 2, 1000));
                };
            })();
        """.trimIndent()
    }

    /**
     * Injetar script de detecção precoce
     */
    private suspend fun injectEarlyDetectionScript(webView: WebView?) {
        webView?.let { view ->
            withContext(Dispatchers.Main) {
                try {
                    val script = """
                        (function() {
                            if (window.VideoDetectorConfig) {
                                window.VideoDetectorConfig.earlyInjection = true;
                                console.log('Early detection script injected');
                            }
                        })();
                    """.trimIndent()
                    
                    view.evaluateJavascript(script, null)
                } catch (e: Exception) {
                    // Ignorar erros de injeção precoce
                }
            }
        }
    }

    /**
     * Injetar script de detecção final
     */
    private suspend fun injectFinalDetectionScript(webView: WebView?) {
        webView?.let { view ->
            withContext(Dispatchers.Main) {
                try {
                    val script = """
                        (function() {
                            if (window.VideoDetectorConfig) {
                                window.VideoDetectorConfig.finalInjection = true;
                                window.VideoDetectorConfig.pageLoadTime = performance.now() - window.VideoDetectorConfig.startTime;
                                console.log('Final detection script injected, load time:', window.VideoDetectorConfig.pageLoadTime);
                            }
                        })();
                    """.trimIndent()
                    
                    view.evaluateJavascript(script, null)
                } catch (e: Exception) {
                    // Ignorar erros de injeção final
                }
            }
        }
    }

    /**
     * Injetar script progressivo
     */
    private suspend fun injectProgressiveScript(webView: WebView?, stage: String) {
        webView?.let { view ->
            withContext(Dispatchers.Main) {
                try {
                    val script = """
                        (function() {
                            if (window.VideoDetectorConfig) {
                                window.VideoDetectorConfig.progressiveStage = '$stage';
                                console.log('Progressive script injected at stage:', '$stage');
                            }
                        })();
                    """.trimIndent()
                    
                    view.evaluateJavascript(script, null)
                } catch (e: Exception) {
                    // Ignorar erros de injeção progressiva
                }
            }
        }
    }

    /**
     * Atualizar métricas de performance
     */
    private fun updatePerformanceMetrics(configTime: Long, jsTime: Long, totalTime: Long) {
        val currentMetrics = _performanceMetrics.value
        val runtime = Runtime.getRuntime()
        val memoryUsage = runtime.totalMemory() - runtime.freeMemory()
        
        _performanceMetrics.value = currentMetrics.copy(
            configurationTime = configTime,
            jsInjectionTime = jsTime,
            totalSetupTime = totalTime,
            memoryUsage = memoryUsage,
            webViewCount = configuredWebViews.size
        )
    }

    /**
     * Limpeza periódica
     */
    private fun startPeriodicCleanup() {
        scope.launch {
            while (true) {
                delay(MEMORY_CLEANUP_INTERVAL_MS)
                
                // Remover WebViews que não existem mais
                val iterator = configuredWebViews.iterator()
                while (iterator.hasNext()) {
                    val entry = iterator.next()
                    try {
                        // Verificar se a WebView ainda é válida
                        entry.key.url
                    } catch (e: Exception) {
                        // WebView foi destruída
                        iterator.remove()
                    }
                }
                
                // Forçar garbage collection se necessário
                if (configuredWebViews.size > 10) {
                    System.gc()
                }
            }
        }
    }

    /**
     * Obter configuração atual de uma WebView
     */
    fun getWebViewProfile(webView: WebView): ConfigurationProfile? {
        return configuredWebViews[webView]
    }

    /**
     * Verificar se WebView está configurada
     */
    fun isWebViewConfigured(webView: WebView): Boolean {
        return configuredWebViews.containsKey(webView)
    }

    /**
     * Remover WebView do gerenciamento
     */
    fun removeWebView(webView: WebView) {
        configuredWebViews.remove(webView)
    }

    /**
     * Obter estatísticas de configuração
     */
    fun getConfigurationStats(): Map<String, Any> {
        val profileCounts = configuredWebViews.values.groupingBy { it }.eachCount()
        val currentMetrics = _performanceMetrics.value
        
        return mapOf(
            "totalConfiguredWebViews" to configuredWebViews.size,
            "profileDistribution" to profileCounts,
            "averageConfigurationTime" to currentMetrics.configurationTime,
            "averageJsInjectionTime" to currentMetrics.jsInjectionTime,
            "averageTotalSetupTime" to currentMetrics.totalSetupTime,
            "currentMemoryUsage" to currentMetrics.memoryUsage,
            "isCurrentlyConfiguring" to isConfiguring.get()
        )
    }

    /**
     * Limpeza de recursos
     */
    fun cleanup() {
        scope.cancel()
        configuredWebViews.clear()
        instance = null
    }
}
package com.anonymous.videodownloaderapp.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u001e\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u00a8\u0006\t"}, d2 = {"BackgroundDownloadsScreen", "", "downloadTracker", "Lcom/anonymous/videodownloaderapp/services/DownloadTracker;", "DownloadItemRow", "download", "Lcom/anonymous/videodownloaderapp/data/DownloadItem;", "onCancel", "Lkotlin/Function0;", "app_debug"})
public final class BackgroundDownloadStatusScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void BackgroundDownloadsScreen(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.services.DownloadTracker downloadTracker) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DownloadItemRow(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadItem download, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCancel) {
    }
}
package com.anonymous.videodownloaderapp.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.anonymous.videodownloaderapp.features.browser.BrowserScreen
import com.anonymous.videodownloaderapp.features.downloads.DownloadsScreen
import com.anonymous.videodownloaderapp.features.player.VideoPlayerScreen
import com.anonymous.videodownloaderapp.features.settings.SettingsScreen
import com.anonymous.videodownloaderapp.data.DownloadRepository

sealed class Destinations(val route: String) {
    data object Browser : Destinations("browser")
    data object Downloads : Destinations("downloads")
    data object Player : Destinations("player")
    data object Settings : Destinations("settings")
}

@Composable
fun NavGraph(
    navController: NavHostController,
    downloadRepository: DownloadRepository,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = Destinations.Browser.route,
        modifier = modifier
    ) {
        // Updated to match zero-arg composables in features.*
        composable(Destinations.Browser.route) { BrowserScreen(navController, downloadRepository) }
        composable(Destinations.Downloads.route) { DownloadsScreen(navController) }
        composable(Destinations.Player.route) { backStackEntry ->
            val videoUrl = backStackEntry.arguments?.getString("videoUrl") ?: ""
            VideoPlayerScreen(navController, videoUrl)
        }
        composable(Destinations.Settings.route) { SettingsScreen() }
    }
}
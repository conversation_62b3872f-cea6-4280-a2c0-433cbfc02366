package com.anonymous.videodownloaderapp.optimization;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u00020\u0001:\u0002\u001f B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u0010J\u0010\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u0014J\u0010\u0010\u0015\u001a\u00020\u00102\b\u0010\u0016\u001a\u0004\u0018\u00010\u0006J\u0006\u0010\u0017\u001a\u00020\u0018J\u000e\u0010\u0019\u001a\u00020\u00062\u0006\u0010\u001a\u001a\u00020\u000bJ\u0018\u0010\u001b\u001a\u00020\u000b2\u0006\u0010\u001c\u001a\u00020\u001d2\b\b\u0002\u0010\u001e\u001a\u00020\u0010R\u0016\u0010\u0005\u001a\n \u0007*\u0004\u0018\u00010\u00060\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\r\u001a\n \u0007*\u0004\u0018\u00010\u00060\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006!"}, d2 = {"Lcom/anonymous/videodownloaderapp/optimization/StorageSegmentationManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "cacheStorage", "Ljava/io/File;", "kotlin.jvm.PlatformType", "downloadSegments", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/anonymous/videodownloaderapp/optimization/StorageSegmentationManager$DownloadSegment;", "externalStorage", "internalStorage", "chooseOptimalStorageLocation", "fileSize", "", "cleanupOldSegments", "", "maxAgeHours", "", "getAvailableSpace", "directory", "getStorageStats", "Lcom/anonymous/videodownloaderapp/optimization/StorageSegmentationManager$StorageStats;", "mergeSegments", "segment", "segmentDownload", "downloadItem", "Lcom/anonymous/videodownloaderapp/data/DownloadItem;", "segmentSize", "DownloadSegment", "StorageStats", "app_debug"})
public final class StorageSegmentationManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.anonymous.videodownloaderapp.optimization.StorageSegmentationManager.DownloadSegment> downloadSegments = null;
    private final java.io.File internalStorage = null;
    @org.jetbrains.annotations.Nullable()
    private final java.io.File externalStorage = null;
    private final java.io.File cacheStorage = null;
    
    public StorageSegmentationManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.io.File chooseOptimalStorageLocation(long fileSize) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.anonymous.videodownloaderapp.optimization.StorageSegmentationManager.DownloadSegment segmentDownload(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.data.DownloadItem downloadItem, long segmentSize) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.io.File mergeSegments(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.optimization.StorageSegmentationManager.DownloadSegment segment) {
        return null;
    }
    
    public final long getAvailableSpace(@org.jetbrains.annotations.Nullable()
    java.io.File directory) {
        return 0L;
    }
    
    public final void cleanupOldSegments(int maxAgeHours) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.anonymous.videodownloaderapp.optimization.StorageSegmentationManager.StorageStats getStorageStats() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0015\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001c\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001d\u001a\u00020\fH\u00c6\u0003JK\u0010\u001e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u00c6\u0001J\u0013\u0010\u001f\u001a\u00020\f2\b\u0010 \u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010!\u001a\u00020\"H\u00d6\u0001J\t\u0010#\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u000eR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\n\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0016\u00a8\u0006$"}, d2 = {"Lcom/anonymous/videodownloaderapp/optimization/StorageSegmentationManager$DownloadSegment;", "", "segmentId", "", "originalFile", "Ljava/io/File;", "segmentFiles", "", "totalSize", "", "segmentSize", "isComplete", "", "(Ljava/lang/String;Ljava/io/File;Ljava/util/List;JJZ)V", "()Z", "getOriginalFile", "()Ljava/io/File;", "getSegmentFiles", "()Ljava/util/List;", "getSegmentId", "()Ljava/lang/String;", "getSegmentSize", "()J", "getTotalSize", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class DownloadSegment {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String segmentId = null;
        @org.jetbrains.annotations.NotNull()
        private final java.io.File originalFile = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.io.File> segmentFiles = null;
        private final long totalSize = 0L;
        private final long segmentSize = 0L;
        private final boolean isComplete = false;
        
        public DownloadSegment(@org.jetbrains.annotations.NotNull()
        java.lang.String segmentId, @org.jetbrains.annotations.NotNull()
        java.io.File originalFile, @org.jetbrains.annotations.NotNull()
        java.util.List<? extends java.io.File> segmentFiles, long totalSize, long segmentSize, boolean isComplete) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSegmentId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.io.File getOriginalFile() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.io.File> getSegmentFiles() {
            return null;
        }
        
        public final long getTotalSize() {
            return 0L;
        }
        
        public final long getSegmentSize() {
            return 0L;
        }
        
        public final boolean isComplete() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.io.File component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.io.File> component3() {
            return null;
        }
        
        public final long component4() {
            return 0L;
        }
        
        public final long component5() {
            return 0L;
        }
        
        public final boolean component6() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.optimization.StorageSegmentationManager.DownloadSegment copy(@org.jetbrains.annotations.NotNull()
        java.lang.String segmentId, @org.jetbrains.annotations.NotNull()
        java.io.File originalFile, @org.jetbrains.annotations.NotNull()
        java.util.List<? extends java.io.File> segmentFiles, long totalSize, long segmentSize, boolean isComplete) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0007H\u00c6\u0003J;\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u0007H\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\rR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000b\u00a8\u0006\u001d"}, d2 = {"Lcom/anonymous/videodownloaderapp/optimization/StorageSegmentationManager$StorageStats;", "", "internalAvailable", "", "externalAvailable", "cacheAvailable", "totalSegments", "", "activeDownloads", "(JJJII)V", "getActiveDownloads", "()I", "getCacheAvailable", "()J", "getExternalAvailable", "getInternalAvailable", "getTotalSegments", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
    public static final class StorageStats {
        private final long internalAvailable = 0L;
        private final long externalAvailable = 0L;
        private final long cacheAvailable = 0L;
        private final int totalSegments = 0;
        private final int activeDownloads = 0;
        
        public StorageStats(long internalAvailable, long externalAvailable, long cacheAvailable, int totalSegments, int activeDownloads) {
            super();
        }
        
        public final long getInternalAvailable() {
            return 0L;
        }
        
        public final long getExternalAvailable() {
            return 0L;
        }
        
        public final long getCacheAvailable() {
            return 0L;
        }
        
        public final int getTotalSegments() {
            return 0;
        }
        
        public final int getActiveDownloads() {
            return 0;
        }
        
        public final long component1() {
            return 0L;
        }
        
        public final long component2() {
            return 0L;
        }
        
        public final long component3() {
            return 0L;
        }
        
        public final int component4() {
            return 0;
        }
        
        public final int component5() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.optimization.StorageSegmentationManager.StorageStats copy(long internalAvailable, long externalAvailable, long cacheAvailable, int totalSegments, int activeDownloads) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}
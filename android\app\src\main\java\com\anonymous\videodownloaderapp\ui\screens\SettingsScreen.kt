package com.anonymous.videodownloaderapp.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.anonymous.videodownloaderapp.data.SettingsRepository
import com.anonymous.videodownloaderapp.services.AudioSessionManager
import kotlinx.coroutines.launch

@Composable
fun SettingsScreen() {
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val audioSessionManager = remember { AudioSessionManager(context) }
    val coroutineScope = rememberCoroutineScope()

    val isAdBlockingEnabled by settingsRepository.isAdBlockingEnabled.collectAsState(initial = false)
    val isTrackingProtectionEnabled by settingsRepository.isTrackingProtectionEnabled.collectAsState(initial = false)
    val selectedQuality by settingsRepository.selectedQuality.collectAsState(initial = "auto")
    val currentAudioMixingMode by settingsRepository.audioMixingMode.collectAsState(initial = "mix")
    
    var isDarkTheme by remember { mutableStateOf(true) }
    var isAutoDownload by remember { mutableStateOf(false) }
    var isNotificationsEnabled by remember { mutableStateOf(true) }
    var downloadLocation by remember { mutableStateOf("/storage/emulated/0/Download") }
    var maxConcurrentDownloads by remember { mutableStateOf(3) }
    var isWifiOnlyDownload by remember { mutableStateOf(false) }
    var isBackgroundDownload by remember { mutableStateOf(true) }
    
    var showQualityDialog by remember { mutableStateOf(false) }
    var showAudioMixingDialog by remember { mutableStateOf(false) }
    var showDownloadLocationDialog by remember { mutableStateOf(false) }
    var showMaxDownloadsDialog by remember { mutableStateOf(false) }
    var showAboutDialog by remember { mutableStateOf(false) }
    var showClearCacheDialog by remember { mutableStateOf(false) }

    // Diálogos
    if (showQualityDialog) {
        QualitySelectionDialog(
            currentQuality = selectedQuality,
            onQualitySelected = { quality ->
                coroutineScope.launch {
                    settingsRepository.setSelectedQuality(quality)
                }
                showQualityDialog = false
            },
            onDismiss = { showQualityDialog = false }
        )
    }

    if (showAudioMixingDialog) {
        AudioMixingDialog(
            currentMode = currentAudioMixingMode,
            onModeSelected = { mode ->
                coroutineScope.launch {
                    audioSessionManager.setAudioMode(mode)
                }
                showAudioMixingDialog = false
            },
            onDismiss = { showAudioMixingDialog = false }
        )
    }

    if (showDownloadLocationDialog) {
        DownloadLocationDialog(
            currentLocation = downloadLocation,
            onLocationSelected = { location ->
                downloadLocation = location
                showDownloadLocationDialog = false
            },
            onDismiss = { showDownloadLocationDialog = false }
        )
    }

    if (showMaxDownloadsDialog) {
        MaxDownloadsDialog(
            currentMax = maxConcurrentDownloads,
            onMaxSelected = { max ->
                maxConcurrentDownloads = max
                showMaxDownloadsDialog = false
            },
            onDismiss = { showMaxDownloadsDialog = false }
        )
    }

    if (showAboutDialog) {
        AboutDialog(
            onDismiss = { showAboutDialog = false }
        )
    }

    if (showClearCacheDialog) {
        ClearCacheDialog(
            onConfirm = {
                // Implementar limpeza de cache
                showClearCacheDialog = false
            },
            onDismiss = { showClearCacheDialog = false }
        )
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Seção Aparência
        item {
            SettingsSection("Aparência") {
                SettingsToggleItem(
                    icon = Icons.Default.DarkMode,
                    title = "Tema Escuro",
                    subtitle = "Usar tema escuro em todo o aplicativo",
                    checked = isDarkTheme,
                    onCheckedChange = { isDarkTheme = it }
                )
            }
        }

        // Seção Privacidade e Segurança
        item {
            SettingsSection("Privacidade e Segurança") {
                SettingsToggleItem(
                    icon = Icons.Default.Block,
                    title = "Bloquear Anúncios",
                    subtitle = "Bloquear anúncios e pop-ups",
                    checked = isAdBlockingEnabled,
                    onCheckedChange = {
                        coroutineScope.launch {
                            settingsRepository.setAdBlockingEnabled(it)
                        }
                    }
                )
                SettingsToggleItem(
                    icon = Icons.Default.Security,
                    title = "Proteção Contra Rastreamento",
                    subtitle = "Bloquear rastreadores de terceiros",
                    checked = isTrackingProtectionEnabled,
                    onCheckedChange = {
                        coroutineScope.launch {
                            settingsRepository.setTrackingProtectionEnabled(it)
                        }
                    }
                )
            }
        }

        // Seção Downloads
        item {
            SettingsSection("Downloads") {
                SettingsClickableItem(
                    icon = Icons.Default.HighQuality,
                    title = "Qualidade Padrão",
                    subtitle = "Qualidade: $selectedQuality",
                    onClick = { showQualityDialog = true }
                )
                SettingsClickableItem(
                    icon = Icons.Default.Folder,
                    title = "Local de Download",
                    subtitle = downloadLocation,
                    onClick = { showDownloadLocationDialog = true }
                )
                SettingsClickableItem(
                    icon = Icons.Default.Download,
                    title = "Downloads Simultâneos",
                    subtitle = "Máximo: $maxConcurrentDownloads downloads",
                    onClick = { showMaxDownloadsDialog = true }
                )
                SettingsToggleItem(
                    icon = Icons.Default.Wifi,
                    title = "Apenas Wi-Fi",
                    subtitle = "Baixar apenas quando conectado ao Wi-Fi",
                    checked = isWifiOnlyDownload,
                    onCheckedChange = { isWifiOnlyDownload = it }
                )
                SettingsToggleItem(
                    icon = Icons.Default.CloudDownload,
                    title = "Download em Segundo Plano",
                    subtitle = "Continuar downloads quando o app estiver fechado",
                    checked = isBackgroundDownload,
                    onCheckedChange = { isBackgroundDownload = it }
                )
                SettingsToggleItem(
                    icon = Icons.Default.GetApp,
                    title = "Download Automático",
                    subtitle = "Iniciar download automaticamente ao detectar vídeo",
                    checked = isAutoDownload,
                    onCheckedChange = { isAutoDownload = it }
                )
            }
        }

        // Seção Reprodução
        item {
            SettingsSection("Reprodução de Vídeo") {
                SettingsClickableItem(
                    icon = Icons.Default.AudioFile,
                    title = "Modo de Mixagem de Áudio",
                    subtitle = "Modo atual: $currentAudioMixingMode",
                    onClick = { showAudioMixingDialog = true }
                )
            }
        }

        // Seção Notificações
        item {
            SettingsSection("Notificações") {
                SettingsToggleItem(
                    icon = Icons.Default.Notifications,
                    title = "Notificações",
                    subtitle = "Receber notificações sobre downloads",
                    checked = isNotificationsEnabled,
                    onCheckedChange = { isNotificationsEnabled = it }
                )
            }
        }

        // Seção Armazenamento
        item {
            SettingsSection("Armazenamento") {
                SettingsClickableItem(
                    icon = Icons.Default.CleaningServices,
                    title = "Limpar Cache",
                    subtitle = "Limpar arquivos temporários e cache",
                    onClick = { showClearCacheDialog = true }
                )
            }
        }

        // Seção Sobre
        item {
            SettingsSection("Sobre") {
                SettingsClickableItem(
                    icon = Icons.Default.Info,
                    title = "Sobre o App",
                    subtitle = "Versão, licenças e informações",
                    onClick = { showAboutDialog = true }
                )
            }
        }
    }
}

@Composable
fun SettingsSection(
    title: String,
    content: @Composable () -> Unit
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(vertical = 8.dp)
        )
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(8.dp)
            ) {
                content()
            }
        }
    }
}

@Composable
fun SettingsToggleItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.padding(end = 16.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

@Composable
fun SettingsClickableItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.padding(end = 16.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun QualitySelectionDialog(
    currentQuality: String,
    onQualitySelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Qualidade Padrão de Download") },
        text = {
            Column {
                val qualities = listOf(
                    "2160p" to "4K Ultra HD",
                    "1440p" to "2K Quad HD",
                    "1080p" to "Full HD",
                    "720p" to "HD",
                    "480p" to "SD",
                    "360p" to "Baixa",
                    "auto" to "Automática (Melhor disponível)"
                )
                qualities.forEach { (quality, description) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onQualitySelected(quality) }
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentQuality == quality,
                            onClick = { onQualitySelected(quality) }
                        )
                        Column(
                            modifier = Modifier.padding(start = 8.dp)
                        ) {
                            Text(
                                text = quality,
                                style = MaterialTheme.typography.bodyLarge
                            )
                            Text(
                                text = description,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Fechar")
            }
        }
    )
}

@Composable
fun AudioMixingDialog(
    currentMode: String,
    onModeSelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Modo de Mixagem de Áudio") },
        text = {
            Column {
                val modes = listOf(
                    "mix" to "Mixar com outros áudios",
                    "duck" to "Reduzir volume de outros áudios",
                    "exclusive" to "Áudio exclusivo"
                )
                modes.forEach { (mode, description) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onModeSelected(mode) }
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentMode == mode,
                            onClick = { onModeSelected(mode) }
                        )
                        Column(
                            modifier = Modifier.padding(start = 8.dp)
                        ) {
                            Text(
                                text = mode.capitalize(),
                                style = MaterialTheme.typography.bodyLarge
                            )
                            Text(
                                text = description,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Fechar")
            }
        }
    )
}

@Composable
fun DownloadLocationDialog(
    currentLocation: String,
    onLocationSelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Local de Download") },
        text = {
            Column {
                val locations = listOf(
                    "/storage/emulated/0/Download" to "Downloads",
                    "/storage/emulated/0/Movies" to "Filmes",
                    "/storage/emulated/0/DCIM" to "Câmera",
                    "/storage/emulated/0/VideoDownloader" to "VideoDownloader"
                )
                locations.forEach { (path, name) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onLocationSelected(path) }
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentLocation == path,
                            onClick = { onLocationSelected(path) }
                        )
                        Column(
                            modifier = Modifier.padding(start = 8.dp)
                        ) {
                            Text(
                                text = name,
                                style = MaterialTheme.typography.bodyLarge
                            )
                            Text(
                                text = path,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Fechar")
            }
        }
    )
}

@Composable
fun MaxDownloadsDialog(
    currentMax: Int,
    onMaxSelected: (Int) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Downloads Simultâneos") },
        text = {
            Column {
                val maxOptions = listOf(1, 2, 3, 4, 5, 6)
                maxOptions.forEach { max ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onMaxSelected(max) }
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentMax == max,
                            onClick = { onMaxSelected(max) }
                        )
                        Text(
                            text = "$max download${if (max > 1) "s" else ""} simultâneo${if (max > 1) "s" else ""}",
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Fechar")
            }
        }
    )
}

@Composable
fun AboutDialog(
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Sobre o Video Downloader") },
        text = {
            Column {
                Text(
                    text = "Video Downloader App",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Versão: 1.0.0",
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Um aplicativo completo para download de vídeos com navegador integrado e gerenciamento avançado de downloads.",
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Desenvolvido com ❤️ usando Jetpack Compose",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Fechar")
            }
        }
    )
}

@Composable
fun ClearCacheDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Limpar Cache") },
        text = {
            Text("Tem certeza de que deseja limpar todos os arquivos de cache? Esta ação não pode ser desfeita.")
        },
        confirmButton = {
            TextButton(
                onClick = onConfirm,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("Limpar")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancelar")
            }
        }
    )
}
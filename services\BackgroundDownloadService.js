import RNFS from 'react-native-fs';
import <PERSON><PERSON>oll from '@react-native-community/cameraroll';
import AsyncStorage from '@react-native-async-storage/async-storage';
import VideoDownloadService from './VideoDownloadService';
import DownloadTracker from './DownloadTracker';
import DownloadNotificationService from './DownloadNotificationService';
import { Alert, Platform } from 'react-native';
import PermissionManager from '../utils/PermissionManager';

// Serviço para downloads em background
class BackgroundDownloadService {
  static activeDownloads = new Map(); // Controle de downloads ativos

  static async initialize() {
    // Inicializar o serviço de notificações
    await DownloadNotificationService.initialize();
  }

  static async startBackgroundDownload(downloadInfo) {
    await this.clearOldDownloads();

    const downloadId = this.generateDownloadId(downloadInfo);

    const trackerCheck = await DownloadTracker.checkDownloadExists(downloadInfo);
    if (trackerCheck.exists) {
      console.log('🚫 Download duplicado detectado:', trackerCheck.reason);
      throw new Error('Este vídeo já foi baixado');
    }

    if (this.activeDownloads.has(downloadId)) {
      console.log('📥 Download similar em progresso, criando novo ID para download simultâneo');
      downloadId = `${downloadId}_${Date.now()}`;
    }

    const fileName = VideoDownloadService.generateFileName(downloadInfo.url);
    const downloadDest = `${RNFS.DocumentDirectoryPath}/${fileName}`;

    const hasPermission = await PermissionManager.ensurePermissions();
    if (!hasPermission.success) {
      throw new Error('Permissão negada. Não é possível iniciar o download.');
    }

    const downloadTask = RNFS.downloadFile({
      fromUrl: downloadInfo.url,
      toFile: downloadDest,
      background: true,
      discretionary: true,
      cache: true,
      progress: (res) => {
        const progress = (res.bytesWritten / res.contentLength) * 100;
        const status = DownloadTracker.getDownloadStatusById(downloadId) || { id: downloadId, status: 'downloading', progress: 0 };
        DownloadNotificationService.updateNotification(downloadId, fileName, progress, status.status, false);
        DownloadTracker.updateDownloadProgress(downloadId, progress, res.bytesWritten, res.contentLength);
      },
    });

    this.activeDownloads.set(downloadId, { ...downloadInfo, fileName, downloadTask, downloadDest, status: 'queued' });

    DownloadNotificationService.showProgressNotification(downloadId, fileName, 0, 'Iniciando...');

    // Iniciar o download e aguardar
    try {
      await DownloadTracker.addDownload({ ...downloadInfo, id: downloadId, fileName, uri: downloadDest, status: 'downloading' });
      const result = await downloadTask.promise;

      if (result.statusCode === 200) {
        console.log('✅ Download concluído para:', fileName);
        await this.saveDownloadedFile(downloadId, downloadDest, fileName);
        DownloadNotificationService.showCompletionNotification(downloadId, fileName, true);
        DownloadTracker.updateDownloadStatus(downloadId, 'completed');
      } else {
        console.error('❌ Download falhou com status code:', result.statusCode);
        throw new Error(`Download falhou com status code: ${result.statusCode}`);
      }
    } catch (error) {
      console.error('Erro no download em background:', error);
      const errorMessage = error.message.includes('NETWORK_ERROR') ?
        'Erro de rede. Verifique sua conexão.' :
        error.message;
      DownloadNotificationService.showCompletionNotification(downloadId, fileName, false, errorMessage);
      DownloadTracker.updateDownloadStatus(downloadId, 'failed', errorMessage);
    } finally {
      this.activeDownloads.delete(downloadId);
    }
  }

  static async saveDownloadedFile(downloadId, filePath, fileName) {
    try {
      const albumName = 'Video Downloader';
      const asset = await CameraRoll.save(filePath, { type: 'video', album: albumName });
      console.log('Arquivo salvo na galeria:', asset.uri);
      DownloadTracker.updateDownloadUri(downloadId, asset.uri); // Atualiza URI para o URI da galeria
      return asset;
    } catch (error) {
      console.error('Erro ao salvar arquivo na galeria:', error);
      Alert.alert('Erro', 'Não foi possível salvar o vídeo na galeria.');
      throw error;
    }
  }

  static async clearOldDownloads() {
    const downloadList = await DownloadTracker.getDownloads();
    const oldDownloads = downloadList.filter(download => {
      if (!download.timestamp) return true; // Se não tem timestamp, considerar velho
      const downloadDate = new Date(download.timestamp);
      const now = new Date();
      const diffHours = Math.abs(now - downloadDate) / 36e5; // Diferença em horas
      return diffHours > 72; // Remover downloads mais antigos que 3 dias (72 horas)
    });

    for (const download of oldDownloads) {
      try {
        if (download.uri && await RNFS.exists(download.uri)) {
          await RNFS.unlink(download.uri);
          console.log('Arquivo antigo removido do disco:', download.uri);
        }
        await DownloadTracker.removeDownload(download.id);
        console.log('Registro de download antigo removido:', download.id);
      } catch (error) {
        console.error('Erro ao limpar download antigo:', download.id, error);
      }
    }
  }

  static getDownloadStatus() {
    return DownloadTracker.getAllDownloads();
  }

  static async cancelDownload(downloadId) {
    const downloadInfo = this.activeDownloads.get(downloadId);
    if (downloadInfo && downloadInfo.downloadTask) {
      downloadInfo.downloadTask.cancel();
      this.activeDownloads.delete(downloadId);
      DownloadNotificationService.showCompletionNotification(downloadId, downloadInfo.fileName, false, 'Download Cancelado');
      DownloadTracker.updateDownloadStatus(downloadId, 'cancelled', 'Download cancelado pelo usuário.');
      // Tentar remover o arquivo parcial
      try {
        if (await RNFS.exists(downloadInfo.downloadDest)) {
          await RNFS.unlink(downloadInfo.downloadDest);
          console.log('Arquivo parcial removido:', downloadInfo.downloadDest);
        }
      } catch (error) {
        console.error('Erro ao remover arquivo parcial:', error);
      }
    }
  }
}

export default BackgroundDownloadService;

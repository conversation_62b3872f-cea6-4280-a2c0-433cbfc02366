package com.anonymous.videodownloaderapp.services

import android.app.ActivityManager
import android.content.Context
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * Cache LRU inteligente para manifests parseados com gestão automática de memória
 * baseada na RAM disponível do dispositivo.
 */
class NetworkVideoCache private constructor(
    private val context: Context
) {
    
    companion object {
        @Volatile
        private var INSTANCE: NetworkVideoCache? = null
        
        fun getInstance(context: Context): NetworkVideoCache {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NetworkVideoCache(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val MIN_CACHE_SIZE = 50
        private const val MAX_CACHE_SIZE = 200
        private const val LIVE_CONTENT_TTL_MS = 15 * 60 * 1000L // 15 minutos
        private const val VOD_CONTENT_TTL_MS = 60 * 60 * 1000L // 1 hora
    }
    
    data class CachedVideo(
        val id: String,
        val url: String,
        val qualities: List<ParsedQuality>,
        val manifestContent: String?,
        val mediaType: MediaType,
        val isLive: Boolean,
        val timestamp: Long,
        val accessCount: AtomicLong = AtomicLong(0),
        val lastAccess: AtomicLong = AtomicLong(System.currentTimeMillis())
    ) {
        fun touch() {
            accessCount.incrementAndGet()
            lastAccess.set(System.currentTimeMillis())
        }
        
        fun isExpired(now: Long = System.currentTimeMillis()): Boolean {
            val ttl = if (isLive) LIVE_CONTENT_TTL_MS else VOD_CONTENT_TTL_MS
            return now - timestamp > ttl
        }
    }
    
    data class ParsedQuality(
        val label: String,          // "1080p", "720p", etc
        val width: Int?,
        val height: Int?,
        val bandwidth: Long?,       // bits per second
        val codecs: String?,
        val url: String,
        val isAudioOnly: Boolean = false,
        val frameRate: Double? = null,
        val tracks: List<TrackInfo> = emptyList()
    )
    
    data class TrackInfo(
        val type: TrackType,        // VIDEO, AUDIO, SUBTITLE
        val language: String?,
        val label: String?,
        val url: String
    )
    
    enum class TrackType { VIDEO, AUDIO, SUBTITLE }
    enum class MediaType { HLS, DASH, PROGRESSIVE }
    
    // Cache principal com weak references para evitar vazamentos
    private val cache = ConcurrentHashMap<String, WeakReference<CachedVideo>>()
    private val accessOrder = mutableListOf<String>()
    private val maxCacheSize: Int
    
    init {
        maxCacheSize = calculateOptimalCacheSize()
    }
    
    private fun calculateOptimalCacheSize(): Int {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val totalRamMB = memoryInfo.totalMem / (1024 * 1024)
        
        return when {
            totalRamMB >= 6144 -> MAX_CACHE_SIZE        // 6GB+ = 200 items
            totalRamMB >= 4096 -> (MAX_CACHE_SIZE * 0.75).toInt()  // 4GB+ = 150 items
            totalRamMB >= 2048 -> (MAX_CACHE_SIZE * 0.5).toInt()   // 2GB+ = 100 items
            else -> MIN_CACHE_SIZE                       // <2GB = 50 items
        }
    }
    
    /**
     * Armazenar vídeo parseado no cache
     */
    fun put(
        url: String,
        qualities: List<ParsedQuality>,
        manifestContent: String? = null,
        mediaType: MediaType,
        isLive: Boolean = false
    ): String {
        val normalizedUrl = normalizeManifestUrl(url)
        val id = generateCacheId(normalizedUrl)
        
        val cached = CachedVideo(
            id = id,
            url = normalizedUrl,
            qualities = qualities,
            manifestContent = manifestContent,
            mediaType = mediaType,
            isLive = isLive,
            timestamp = System.currentTimeMillis()
        )
        
        synchronized(this) {
            cache[id] = WeakReference(cached)
            accessOrder.remove(id)
            accessOrder.add(id)
            
            // Enforcar limite de tamanho
            enforceSize()
        }
        
        return id
    }
    
    /**
     * Recuperar vídeo do cache
     */
    fun get(url: String): CachedVideo? {
        val normalizedUrl = normalizeManifestUrl(url)
        val id = generateCacheId(normalizedUrl)
        
        return synchronized(this) {
            val ref = cache[id]
            val cached = ref?.get()
            
            if (cached == null) {
                // Limpeza automática de referências mortas
                cache.remove(id)
                accessOrder.remove(id)
                null
            } else if (cached.isExpired()) {
                // Conteúdo expirado
                cache.remove(id)
                accessOrder.remove(id)
                null
            } else {
                // Atualizar ordem de acesso
                cached.touch()
                accessOrder.remove(id)
                accessOrder.add(id)
                cached
            }
        }
    }
    
    /**
     * Verificar se URL está no cache (sem acessar)
     */
    fun contains(url: String): Boolean {
        val normalizedUrl = normalizeManifestUrl(url)
        val id = generateCacheId(normalizedUrl)
        val ref = cache[id]
        val cached = ref?.get()
        return cached != null && !cached.isExpired()
    }
    
    /**
     * Invalidar cache para conteúdo live (chamado periodicamente)
     */
    fun invalidateExpired() {
        val now = System.currentTimeMillis()
        val toRemove = mutableListOf<String>()
        
        synchronized(this) {
            cache.entries.forEach { (id, ref) ->
                val cached = ref.get()
                if (cached == null || cached.isExpired(now)) {
                    toRemove.add(id)
                }
            }
            
            toRemove.forEach { id ->
                cache.remove(id)
                accessOrder.remove(id)
            }
        }
    }
    
    /**
     * Forçar limpeza do cache
     */
    fun clear() {
        synchronized(this) {
            cache.clear()
            accessOrder.clear()
        }
    }
    
    /**
     * Obter estatísticas do cache
     */
    fun getStats(): Map<String, Any> {
        synchronized(this) {
            val activeEntries = cache.values.count { it.get() != null }
            val deadReferences = cache.size - activeEntries
            val liveContent = cache.values.mapNotNull { it.get() }.count { it.isLive }
            
            return mapOf(
                "maxSize" to maxCacheSize,
                "currentSize" to cache.size,
                "activeEntries" to activeEntries,
                "deadReferences" to deadReferences,
                "liveContent" to liveContent,
                "memoryUsageKB" to estimateMemoryUsage()
            )
        }
    }
    
    private fun enforceSize() {
        while (cache.size > maxCacheSize && accessOrder.isNotEmpty()) {
            val oldestId = accessOrder.removeAt(0)
            cache.remove(oldestId)
        }
    }
    
    private fun normalizeManifestUrl(url: String): String {
        // Remove apenas parâmetros voláteis, preserva tokens de autenticação
        val volatileParams = setOf(
            "expires", "signature", "token", "ts", "range", "rn",
            "mm", "mn", "ms", "mv", "mvi", "ei", "cpn", "gir", "n"
        )
        
        return try {
            val uri = android.net.Uri.parse(url)
            val builder = uri.buildUpon().clearQuery()
            
            uri.queryParameterNames.forEach { param ->
                if (param !in volatileParams) {
                    builder.appendQueryParameter(param, uri.getQueryParameter(param))
                }
            }
            
            builder.build().toString()
        } catch (e: Exception) {
            url
        }
    }
    
    private fun generateCacheId(url: String): String {
        return url.hashCode().toString()
    }
    
    private fun estimateMemoryUsage(): Long {
        val runtime = Runtime.getRuntime()
        return (runtime.totalMemory() - runtime.freeMemory()) / 1024
    }
    
    /**
     * Converter de UniversalVideoUrlExtractor.Quality para ParsedQuality
     */
    fun convertFromUniversal(qualities: List<UniversalVideoUrlExtractor.Quality>): List<ParsedQuality> {
        return qualities.map { q ->
            ParsedQuality(
                label = q.label,
                width = q.width,
                height = q.height,
                bandwidth = q.bandwidth,
                codecs = q.codecs,
                url = q.url,
                isAudioOnly = false,
                frameRate = null,
                tracks = emptyList()
            )
        }
    }
}
/**
 * Top-level Gradle configuration for native Android app (Kotlin + Compose).
 * React Native removed.
 */
buildscript {
    repositories {
        google()
        mavenCentral()
        // Explicit Google Maven mirrors for reliability
        maven { url "https://dl.google.com/dl/android/maven2" }
        // JitPack and J<PERSON><PERSON> (fallback only if needed)
        maven { url "https://jitpack.io" }
        // gradle plugin portal should be last
        gradlePluginPortal()
    }
    dependencies {
        // Keep AGP/Kotlin on classpath for legacy-compatible resolution
        classpath("com.android.tools.build:gradle:8.2.2")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.22")
        classpath("androidx.room:room-gradle-plugin:2.6.1")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        // Explicit Google Maven mirrors for reliability
        maven { url "https://dl.google.com/dl/android/maven2" }
        // JitPack and <PERSON><PERSON><PERSON> (fallback only if needed)
        maven { url "https://jitpack.io" }
        // gradle plugin portal should be last
        gradlePluginPortal()
    }
}

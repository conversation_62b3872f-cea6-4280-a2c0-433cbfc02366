--------- beginning of main
08-10 10:47:00.039  1116 20583 I netd    : tetherGetStats() -> {[TetherStatsParcel{iface: wlan0, rxBytes: 265879450, rxPackets: 108982, txBytes: 48571488, txPackets: 62479, ifIndex: 0}]} <3.77ms>
08-10 10:47:00.053  1116 20583 I netd    : bandwidthSetGlobalAlert(2097152) <0.39ms>
08-10 10:47:00.053  2736  2764 I QImsService: VtDataUsageProvider : onSetAlert:2097152
08-10 10:47:00.054  2736  2736 I QImsService: ImsServiceClassTracker : updateAlertQuota:newQuota=2097152,Remaining=2097152
--------- beginning of system
08-10 10:47:00.340  1707  2339 D ActivityManager: freezing 12117 com.mgoogle.android.gms
08-10 10:47:00.668  9308  9351 D LOWI-********.y: [LOWI-<PERSON>an] wait_event:Wait done with Cmd 103
08-10 10:47:00.668  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:00.669  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:00.669  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:00.669  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
--------- beginning of kernel
08-10 10:47:00.997 20368 20368 E msm_dwc3_perf_vote_update: latency updated to: 61
08-10 10:47:01.315  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:01.326  1116 20583 I netd    : tetherGetStats() -> {[TetherStatsParcel{iface: wlan0, rxBytes: 265884850, rxPackets: 109114, txBytes: 49008581, txPackets: 62781, ifIndex: 0}]} <3.24ms>
08-10 10:47:01.359  1707  1924 D KernelCpuUidUserSysTimeReader: Removing uids 90001-90001
08-10 10:47:01.361  1707  1924 D KernelCpuUidUserSysTimeReader: Removing uids 99037-99037
08-10 10:47:01.430  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:02.558  2914 20925 I NearbyMediums: No BLE Fast/GATT advertisements found in the latest cycle.
08-10 10:47:02.670  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:02.670  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:02.671  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:02.671  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:02.671  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:03.130 20368 20368 E msm_dwc3_perf_vote_update: latency updated to: -1
08-10 10:47:03.206 16389 16389 I sd 0    : 0:0:4: [sde] Synchronizing SCSI cache
08-10 10:47:03.209 16389 16389 I ufshpb_suspend: 3235 info: ufshpb_lu 4 goto suspend
08-10 10:47:03.209 16389 16389 I ufshpb_suspend: 3236 info: ufshpb_lu 4 changes suspend state
08-10 10:47:03.546  1236  2320 I slpi    : STK3A5X ALS_OC: lux:101.015999, last_ps:1[1725], CH0:69, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:03.649  1236  2320 I slpi    : STK3A5X ALS_OC: lux:112.727997, last_ps:1[1715], CH0:77, CH2:2, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:03.752  1236  2320 I slpi    : STK3A5X ALS_OC: lux:127.367996, last_ps:1[1721], CH0:87, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:03.857  1236  2320 I slpi    : STK3A5X ALS_OC: lux:142.007996, last_ps:1[1717], CH0:97, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:04.167  1236  2320 I slpi    : STK3A5X ALS_OC: lux:156.647995, last_ps:1[1720], CH0:107, CH2:5, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:04.449  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:04.675  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:04.675  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:04.675  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:04.676  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:04.676  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:05.095  1236  2320 I slpi    : STK3A5X ALS_OC: lux:136.151993, last_ps:1[1723], CH0:93, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:06.696  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:06.696  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:06.696  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:06.697  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:06.697  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:07.057  1236  2320 I slpi    : STK3A5X ALS_OC: lux:93.695999, last_ps:1[1725], CH0:64, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:07.159  1236  2320 I slpi    : STK3A5X ALS_OC: lux:83.447998, last_ps:1[1718], CH0:57, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:07.473  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:07.576  1236  2320 I slpi    : STK3A5X ALS_OC: lux:112.727997, last_ps:1[1717], CH0:77, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:07.677  1236  2320 I slpi    : STK3A5X ALS_OC: lux:136.151993, last_ps:1[1726], CH0:93, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:07.885  1236  2320 I slpi    : STK3A5X ALS_OC: lux:156.647995, last_ps:1[1720], CH0:107, CH2:5, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:08.458  1233  1233 I timerfd_poll: comm:health@2.1-serv pid:1233 exp:9837486
08-10 10:47:08.470  1233  1233 W healthd : battery l=82 v=4189 t=31.0 h=2 st=3 c=0 fc=4126000 cc=1678 chg=u
08-10 10:47:08.498  2736  2736 D QtiCarrierConfigHelper: WARNING, no carrier configs on phone Id: 1
08-10 10:47:08.499  1707  1707 W ActivityManager: Receiver with filter android.content.IntentFilter@6abc75b already registered for pid 1707, callerPackage is android
08-10 10:47:08.501  1707  1707 I LineageHealth: isSupported mode called, param: 2, supported: 1
08-10 10:47:08.501  1707  1707 I LineageHealth: mIsPowerConnected: true, mBatteryPct: 82.0
08-10 10:47:08.501  1707  1707 I LineageHealth: Current battery level: 82.0, target: 90, limit set: false
08-10 10:47:08.505  2478  3881 D PowerUI : can't show warning due to - plugged: true status unknown: false
08-10 10:47:08.697 20518 20518 I ufshpb_resume: 3261 info: ufshpb_lu 4 resume. do_task_work 0 retry 0
08-10 10:47:08.707  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:08.707  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:08.707  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:08.707  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:08.707  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:08.854  2914 20925 I FusedLocation: location delivery to 10137/com.google.android.gms[earthquake_alerting]/cb026136 blocked - too close [CONTEXT service_id=6 ]
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification package name (android) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification channel name (Mensagens importantes do desenvolvedor) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification channel id (DEVELOPER_IMPORTANT) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification package name (com.novapontocom.casasbahia) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification channel name (Geral) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification channel id (default_notification_channel_id) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification package name (android) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification channel name (Conexão USB) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification channel id (USB) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification package name (android) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification channel name (Mensagens importantes do desenvolvedor) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification channel id (DEVELOPER_IMPORTANT) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification package name (android) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification channel name (Conexão USB) not found in string cache
08-10 10:47:08.855  1707  1859 W NotifHistoryProto: notification channel id (USB) not found in string cache
08-10 10:47:08.857  2914 20925 I FusedLocation: location delivery to 10137/com.google.android.gms[earthquake_detection]/e06a9e11 blocked - too close [CONTEXT service_id=6 ]
08-10 10:47:08.858  2914 11498 W PersonalSafety: Reject: No sufficient displacement. [CONTEXT service_id=310 ]
08-10 10:47:08.863  2914 11498 I PersonalSafety: AlertH notified of inferred, triggering. [CONTEXT service_id=310 ]
08-10 10:47:08.882  2914 11498 W PersonalSafety: Ble scanner not running, ignore location update [CONTEXT service_id=310 ]
08-10 10:47:08.903  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:08.908  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.908  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.908  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.908  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.910  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.910  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.911  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.911  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.912  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.912  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.912  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.912  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.913  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.913  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.914  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.914  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.915  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.915  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.915  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.915  2914  8379 I GmsCoreXrpcWrapper: Returning a channel provider with trafficStatsTag=67145728 trafficStatsUid=-1
08-10 10:47:08.955  2914 20940 I TFLite-in-PlayServices: Created interpreter.
08-10 10:47:08.967  2914 20940 I TFLite-in-PlayServices: Created interpreter.
08-10 10:47:08.979  2914 20940 I TFLite-in-PlayServices: Created interpreter.
08-10 10:47:08.983  2914 20940 I SemanticLocation: [Inferrer] IncrementalTimelineInferrer.predict() logs: 
08-10 10:47:08.983  2914 20940 I SemanticLocation: Inferrer completed successfully
08-10 10:47:08.983  2914 20940 I SemanticLocation: [CONTEXT service_id=173 ]
08-10 10:47:09.025  2914 20940 I TFLite-in-PlayServices: Created interpreter.
08-10 10:47:09.032  2914 20940 I TFLite-in-PlayServices: Created interpreter.
08-10 10:47:09.040  2914 20940 I TFLite-in-PlayServices: Created interpreter.
08-10 10:47:09.044  2914 20940 I SemanticLocation: [Inferrer] IncrementalTimelineInferrer.predict() logs: 
08-10 10:47:09.044  2914 20940 I SemanticLocation: Inferrer completed successfully
08-10 10:47:09.044  2914 20940 I SemanticLocation: [CONTEXT service_id=173 ]
08-10 10:47:10.503  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:10.721  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:10.722  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:10.722  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:10.722  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:10.722  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:11.632  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-10 10:47:11.632  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-10 10:47:11.634  1227  6058 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-10 10:47:11.634  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-10 10:47:11.643  2914 20937 I NearbyMediums: This GATT advertisement BleAdvertisement { version=2, socketVersion=2, isFast=true, serviceIdHash=null, data=[ 0x23 0x4e 0x55 0x41 0x48 0x11 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ], deviceToken=[ 0x8f 0xcf ], rxAdvertisement=null } is a Fast Advertisement and matched UUID 0000fef3-0000-1000-8000-00805f9b34fb in fastAdvertisementServiceUuids map with serviceID NearbySharing
08-10 10:47:11.644  2914 20936 I NearbyConnections: Found BleAdvertisement [ 0x23 0x4e 0x55 0x41 0x48 0x11 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ] (with EndpointId NUAH and EndpointInfo [ 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ])
08-10 10:47:11.646  2914 20936 I NearbyConnections: ClientProxy(166113317) reporting onEndpointFound(NUAH)
08-10 10:47:11.647  2914 20936 I NearbyConnections: ClientProxy(166113317) reporting onEndpointDistanceChanged(NUAH, UNKNOWN)
08-10 10:47:11.648  2914 20925 I NearbySharing: NearbySharing Internal event EndpointDiscovered(endpointId=NUAH)
08-10 10:47:11.677  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-10 10:47:11.677  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-10 10:47:11.828  1227  5890 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-10 10:47:12.221  1236  2320 I slpi    : STK3A5X ALS_OC: lux:136.151993, last_ps:1[1715], CH0:93, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:12.268 20518 20518 I sd 0    : 0:0:4: [sde] Synchronizing SCSI cache
08-10 10:47:12.271 20518 20518 I ufshpb_suspend: 3235 info: ufshpb_lu 4 goto suspend
08-10 10:47:12.271 20518 20518 I ufshpb_suspend: 3236 info: ufshpb_lu 4 changes suspend state
08-10 10:47:12.532  1236  2320 I slpi    : STK3A5X ALS_OC: lux:112.727997, last_ps:1[1722], CH0:77, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:12.561  2914 20938 I NearbyMediums: No BLE Fast/GATT advertisements found in the latest cycle.
08-10 10:47:12.632  1236  2320 I slpi    : STK3A5X ALS_OC: lux:86.375999, last_ps:1[1712], CH0:59, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:12.735  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:12.735  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:12.735  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:12.735  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:12.736  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:12.945  1236  2320 I slpi    : STK3A5X ALS_OC: lux:77.592003, last_ps:1[1716], CH0:53, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:13.525  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:14.747  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:14.747  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:14.747  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:14.748  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:14.748  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:15.009  1236  2320 I slpi    : STK3A5X ALS_OC: lux:90.767998, last_ps:1[1721], CH0:62, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:15.213  1236  2320 I slpi    : STK3A5X ALS_OC: lux:103.944000, last_ps:1[1711], CH0:71, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:15.524  1236  2320 I slpi    : STK3A5X ALS_OC: lux:89.304001, last_ps:1[1721], CH0:61, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:15.965  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-10 10:47:15.965  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-10 10:47:15.966  1227  6058 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-10 10:47:15.966  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-10 10:47:16.009  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-10 10:47:16.009  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-10 10:47:16.160  1227  5890 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-10 10:47:16.247  1236  2320 I slpi    : STK3A5X ALS_OC: lux:102.480003, last_ps:1[1709], CH0:70, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:16.563  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:16.762  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:16.762  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:16.763  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:16.763  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:16.763  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:16.763  1236  2320 I slpi    : STK3A5X ALS_OC: lux:117.120003, last_ps:1[1718], CH0:80, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:17.796  1236  2320 I slpi    : STK3A5X ALS_OC: lux:99.552002, last_ps:1[1715], CH0:68, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:18.519  1236  2320 I slpi    : STK3A5X ALS_OC: lux:109.800003, last_ps:1[1717], CH0:75, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:18.775  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:18.775  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:18.776  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:18.776  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:18.776  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:18.931  1236  2320 I slpi    : STK3A5X ALS_OC: lux:96.624001, last_ps:1[1715], CH0:66, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:18.938 20518 20518 I ufshpb_resume: 3261 info: ufshpb_lu 4 resume. do_task_work 0 retry 0
08-10 10:47:19.241  1236  2320 I slpi    : STK3A5X ALS_OC: lux:86.375999, last_ps:1[1714], CH0:59, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:19.595  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:19.655  1236  2320 I slpi    : STK3A5X ALS_OC: lux:101.015999, last_ps:1[1712], CH0:69, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:19.757  1236  2320 I slpi    : STK3A5X ALS_OC: lux:118.584000, last_ps:1[1719], CH0:81, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:20.010  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-10 10:47:20.010  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-10 10:47:20.012  1227  6058 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-10 10:47:20.012  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-10 10:47:20.058  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-10 10:47:20.058  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-10 10:47:20.209  1227  5890 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-10 10:47:20.584  1236  2320 I slpi    : STK3A5X ALS_OC: lux:102.480003, last_ps:1[1718], CH0:70, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:20.776  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:20.776  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:20.776  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:20.776  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:20.776  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:20.894  1236  2320 I slpi    : STK3A5X ALS_OC: lux:118.584000, last_ps:1[1718], CH0:81, CH2:3, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:21.363  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-10 10:47:21.364  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-10 10:47:21.365  1227  6058 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-10 10:47:21.365  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-10 10:47:21.407  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-10 10:47:21.407  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-10 10:47:21.409  1236  2320 I slpi    : STK3A5X ALS_OC: lux:140.544006, last_ps:1[1717], CH0:96, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:21.558  1227  5890 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-10 10:47:22.340  1236  2320 I slpi    : STK3A5X ALS_OC: lux:121.512001, last_ps:1[1719], CH0:83, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:22.565  2914 20938 I NearbyMediums: Found Fast Ble Advertisements :
08-10 10:47:22.565  2914 20938 I NearbyMediums: BleAdvertisement { version=2, socketVersion=2, isFast=true, serviceIdHash=null, data=[ 0x23 0x4e 0x55 0x41 0x48 0x11 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ], deviceToken=[ 0x8f 0xcf ], rxAdvertisement=null } : 3 times.
08-10 10:47:22.565  2914 20938 I NearbyMediums: Total 3 fast advertisements from 1 unique advertisers.
08-10 10:47:22.587 20518 20518 I sd 0    : 0:0:4: [sde] Synchronizing SCSI cache
08-10 10:47:22.591 20518 20518 I ufshpb_suspend: 3235 info: ufshpb_lu 4 goto suspend
08-10 10:47:22.591 20518 20518 I ufshpb_suspend: 3236 info: ufshpb_lu 4 changes suspend state
08-10 10:47:22.620  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:22.649  1236  2320 I slpi    : STK3A5X ALS_OC: lux:108.335999, last_ps:1[1725], CH0:74, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:22.795  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:22.795  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:22.795  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:22.795  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:22.795  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:23.570  2343  2343 I timerfd_poll: comm:AlarmManager pid:2343 exp:9852598
08-10 10:47:23.578  1236  2320 I slpi    : STK3A5X ALS_OC: lux:121.512001, last_ps:1[1717], CH0:83, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:23.586  1707  1766 D ActivityManager: sync unfroze 12117 com.mgoogle.android.gms for 7
08-10 10:47:23.587  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:23.594 12215 12277 D GmsGcmMcsSvc: Heartbeat initiated, reason: Intent { act=org.microg.gms.gcm.mcs.HEARTBEAT flg=0x4 cmp=com.mgoogle.android.gms/org.microg.gms.gcm.McsService (has extras) }
08-10 10:47:23.605 12215 12277 D GmsGcmMcsSvc: Scheduling heartbeat in 60 seconds...
08-10 10:47:23.607 12215  6667 D GmsGcmMcsOutput: Outgoing message: HeartbeatPing{last_stream_id_received=90}
08-10 10:47:23.888  1236  2320 I slpi    : STK3A5X ALS_OC: lux:144.936005, last_ps:1[1713], CH0:99, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:23.934 12215  6666 D GmsGcmMcsInput: Incoming message: HeartbeatAck{last_stream_id_received=90}
08-10 10:47:23.940 12215  6666 D GmsGcmPrefs: learnReached: gcm_network_wifi / 59684
08-10 10:47:24.198  1236  2320 I slpi    : STK3A5X ALS_OC: lux:124.440002, last_ps:1[1717], CH0:85, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:24.404  1236  2320 I slpi    : STK3A5X ALS_OC: lux:109.800003, last_ps:1[1720], CH0:75, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:24.714  1236  2320 I slpi    : STK3A5X ALS_OC: lux:125.903999, last_ps:1[1726], CH0:86, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:24.795  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:24.795  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:24.795  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:24.796  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:24.796  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:24.815  1236  2320 I slpi    : STK3A5X ALS_OC: lux:101.015999, last_ps:1[1718], CH0:69, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:24.918  1236  2320 I slpi    : STK3A5X ALS_OC: lux:83.447998, last_ps:1[1711], CH0:57, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:25.230  1236  2320 I slpi    : STK3A5X ALS_OC: lux:121.512001, last_ps:1[1727], CH0:83, CH2:3, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:25.333  1236  2320 I slpi    : STK3A5X ALS_OC: lux:146.399994, last_ps:1[1716], CH0:100, CH2:5, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:25.540  1236  2320 I slpi    : STK3A5X ALS_OC: lux:162.503998, last_ps:1[1716], CH0:111, CH2:5, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:25.653  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:25.770  1116 20583 I netd    : tetherGetStats() -> {[TetherStatsParcel{iface: wlan0, rxBytes: 268258860, rxPackets: 111311, txBytes: 49967439, txPackets: 63708, ifIndex: 0}]} <2.38ms>
08-10 10:47:25.800 20320 20320 I ufshpb_resume: 3261 info: ufshpb_lu 4 resume. do_task_work 0 retry 0
08-10 10:47:25.804  1116 20583 I netd    : bandwidthSetGlobalAlert(2097152) <0.26ms>
08-10 10:47:25.805  2736  2764 I QImsService: VtDataUsageProvider : onSetAlert:2097152
08-10 10:47:25.805  2736  2736 I QImsService: ImsServiceClassTracker : updateAlertQuota:newQuota=2097152,Remaining=2097152
08-10 10:47:26.160  1236  2320 I slpi    : STK3A5X ALS_OC: lux:144.936005, last_ps:1[1718], CH0:99, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:26.598 20368 20368 E msm_dwc3_perf_vote_update: latency updated to: 61
08-10 10:47:26.805  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:26.805  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:26.806  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:26.806  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:26.806  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:27.501  1236  2320 I slpi    : STK3A5X ALS_OC: lux:124.440002, last_ps:1[1716], CH0:85, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:27.706  1236  2320 I slpi    : STK3A5X ALS_OC: lux:96.624001, last_ps:1[1723], CH0:66, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:27.811  1236  2320 I slpi    : STK3A5X ALS_OC: lux:81.984001, last_ps:1[1721], CH0:56, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:28.224  1236  2320 I slpi    : STK3A5X ALS_OC: lux:71.736000, last_ps:1[1709], CH0:49, CH2:0, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:28.637  1236  2320 I slpi    : STK3A5X ALS_OC: lux:81.984001, last_ps:1[1716], CH0:56, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:28.684  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:28.730 20368 20368 E msm_dwc3_perf_vote_update: latency updated to: -1
08-10 10:47:28.824  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:28.824  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:28.824  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:28.825  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:28.825  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:28.881 32074 32074 I qti_glink_charger: qti_charger_get_chg_info: LPD: present=0, rsbu1=0, rsbu2=0, cid=2500
08-10 10:47:28.882 32074 32074 E qti_glink_charger: qti_charger_read: Invalid data size 0, on property: 36
08-10 10:47:28.882 32074 32074 I qti_glink_charger: qti_wireless_charge_dump_info: Wireless dump info -1: CHIP_ID: 0x0000, MTP_FW_VER: 0x0000, IRQ STATUS: 0x0000, SYS_MODE:  RX/TX 0, OP_MODE:  BPP/EPP 0x0, RX_FOP: 0kHz, RX_VOUT: 0mV, RX_VRECT: 0mV, RX_IRECT: 0mV, RX_NEG_POWER: 0w
08-10 10:47:28.882 32074 32074 I qti_glink_charger: qti_wireless_charge_dump_info: Wireless dump info -2: TX_IIN: 0mA, TX_VIN: 0mV, TX_VRECT: 0mV, TX_DET_RX_POWER: 0mW, TX_POWER: 0mW, POWER_LOSS: 0mW, TX_FOD: 0,
08-10 10:47:28.882 32074 32074 I qti_glink_charger: qti_wireless_charge_dump_info: Wireless dump info -3: FOLIO_MODE: 0, PEN_STATUS: 0, PEN_SOC: 0, PEN_ERROR: 0, USB_OTG: 0, WLS_BOOST: 0, WLS_ICL_MA: 0mA, WLS_ICL_THERM_MA: 0mA
08-10 10:47:28.882 32074 32074 I qti_glink_charger: qti_charger_get_chg_info: Thermal: primary_limit_level = 0, primary_fcc_ma = 0, secondary_limit_level = 0, thermal_secondary_fcc_ma = 0
08-10 10:47:28.882 32074 32074 I mmi_charger: mmi_get_charger_info: [C:qti_glink_charger]: batt_mv 4189, batt_ma 0, batt_soc 81, batt_temp 31, batt_status 2, batt_sn SB18D10750, batt_fv_mv 0, batt_fcc_ma 0
08-10 10:47:28.882 32074 32074 I mmi_charger: mmi_get_charger_info: [C:qti_glink_charger]: chrg_present 1, chrg_type 1, chrg_pmax_mw 2500, chrg_mv 4984, chrg_ma 278, chrg_otg_enabled 0, thermal_level 0
08-10 10:47:28.882 32074 32074 I mmi_charger: mmi_update_charger_status: [C:qti_glink_charger]: StepChg: MAX, TempZone: 0, LimitMode: 2, DemoSuspend: 0
08-10 10:47:28.882 32074 32074 I mmi_charger: mmi_configure_charger: [C:qti_glink_charger]: FV=4400, FCC=4000, CDIS=0, CSUS=0, CRES=0, CFULL=0
08-10 10:47:28.895  1233  1233 W healthd : battery l=82 v=4189 t=30.0 h=2 st=3 c=0 fc=4041000 cc=1678 chg=u
08-10 10:47:28.911 20513 20513 I qti_glink_charger: qti_charger_get_chg_info: LPD: present=0, rsbu1=0, rsbu2=0, cid=2500
08-10 10:47:28.913 20513 20513 E qti_glink_charger: qti_charger_read: Invalid data size 0, on property: 36
08-10 10:47:28.913 20513 20513 I qti_glink_charger: qti_wireless_charge_dump_info: Wireless dump info -1: CHIP_ID: 0x0000, MTP_FW_VER: 0x0000, IRQ STATUS: 0x0000, SYS_MODE:  RX/TX 0, OP_MODE:  BPP/EPP 0x0, RX_FOP: 0kHz, RX_VOUT: 0mV, RX_VRECT: 0mV, RX_IRECT: 0mV, RX_NEG_POWER: 0w
08-10 10:47:28.913 20513 20513 I qti_glink_charger: qti_wireless_charge_dump_info: Wireless dump info -2: TX_IIN: 0mA, TX_VIN: 0mV, TX_VRECT: 0mV, TX_DET_RX_POWER: 0mW, TX_POWER: 0mW, POWER_LOSS: 0mW, TX_FOD: 0,
08-10 10:47:28.913 20513 20513 I qti_glink_charger: qti_wireless_charge_dump_info: Wireless dump info -3: FOLIO_MODE: 0, PEN_STATUS: 0, PEN_SOC: 0, PEN_ERROR: 0, USB_OTG: 0, WLS_BOOST: 0, WLS_ICL_MA: 0mA, WLS_ICL_THERM_MA: 0mA
08-10 10:47:28.913 20513 20513 I qti_glink_charger: qti_charger_get_chg_info: Thermal: primary_limit_level = 0, primary_fcc_ma = 0, secondary_limit_level = 0, thermal_secondary_fcc_ma = 0
08-10 10:47:28.913 20513 20513 I mmi_charger: mmi_get_charger_info: [C:qti_glink_charger]: batt_mv 4189, batt_ma 0, batt_soc 81, batt_temp 31, batt_status 2, batt_sn SB18D10750, batt_fv_mv 0, batt_fcc_ma 0
08-10 10:47:28.913 20513 20513 I mmi_charger: mmi_get_charger_info: [C:qti_glink_charger]: chrg_present 1, chrg_type 1, chrg_pmax_mw 2500, chrg_mv 4959, chrg_ma 319, chrg_otg_enabled 0, thermal_level 0
08-10 10:47:28.913 20513 20513 I mmi_charger: mmi_update_charger_status: [C:qti_glink_charger]: StepChg: MAX, TempZone: 0, LimitMode: 2, DemoSuspend: 0
08-10 10:47:28.913 20513 20513 I mmi_charger: mmi_configure_charger: [C:qti_glink_charger]: FV=4400, FCC=4000, CDIS=0, CSUS=0, CRES=0, CFULL=0
08-10 10:47:28.933  2736  2736 D QtiCarrierConfigHelper: WARNING, no carrier configs on phone Id: 1
08-10 10:47:28.934  1707  1707 W ActivityManager: Receiver with filter android.content.IntentFilter@44c6327 already registered for pid 1707, callerPackage is android
08-10 10:47:28.937  1707  1707 I LineageHealth: isSupported mode called, param: 2, supported: 1
08-10 10:47:28.937  1707  1707 I LineageHealth: mIsPowerConnected: true, mBatteryPct: 82.0
08-10 10:47:28.937  1707  1707 I LineageHealth: Current battery level: 82.0, target: 90, limit set: false
08-10 10:47:28.944  2478  3115 D PowerUI : can't show warning due to - plugged: true status unknown: false
08-10 10:47:28.948 20513 20513 I mmi_charger: mmi_update_battery_status: state_of_health is 4041000
08-10 10:47:28.957  1233  1233 W healthd : battery l=82 v=4189 t=31.0 h=2 st=3 c=0 fc=4041000 cc=1678 chg=u
08-10 10:47:28.982  1233  1233 W healthd : battery l=82 v=4189 t=31.0 h=2 st=3 c=0 fc=4041000 cc=1678 chg=u
08-10 10:47:29.235 19741 19741 I qti_glink_charger: qti_charger_get_chg_info: LPD: present=0, rsbu1=0, rsbu2=0, cid=2500
08-10 10:47:29.236 19741 19741 E qti_glink_charger: qti_charger_read: Invalid data size 0, on property: 36
08-10 10:47:29.236 19741 19741 I qti_glink_charger: qti_wireless_charge_dump_info: Wireless dump info -1: CHIP_ID: 0x0000, MTP_FW_VER: 0x0000, IRQ STATUS: 0x0000, SYS_MODE:  RX/TX 0, OP_MODE:  BPP/EPP 0x0, RX_FOP: 0kHz, RX_VOUT: 0mV, RX_VRECT: 0mV, RX_IRECT: 0mV, RX_NEG_POWER: 0w
08-10 10:47:29.236 19741 19741 I qti_glink_charger: qti_wireless_charge_dump_info: Wireless dump info -2: TX_IIN: 0mA, TX_VIN: 0mV, TX_VRECT: 0mV, TX_DET_RX_POWER: 0mW, TX_POWER: 0mW, POWER_LOSS: 0mW, TX_FOD: 0,
08-10 10:47:29.236 19741 19741 I qti_glink_charger: qti_wireless_charge_dump_info: Wireless dump info -3: FOLIO_MODE: 0, PEN_STATUS: 0, PEN_SOC: 0, PEN_ERROR: 0, USB_OTG: 0, WLS_BOOST: 0, WLS_ICL_MA: 0mA, WLS_ICL_THERM_MA: 0mA
08-10 10:47:29.236 19741 19741 I qti_glink_charger: qti_charger_get_chg_info: Thermal: primary_limit_level = 0, primary_fcc_ma = 0, secondary_limit_level = 0, thermal_secondary_fcc_ma = 0
08-10 10:47:29.236 19741 19741 I mmi_charger: mmi_get_charger_info: [C:qti_glink_charger]: batt_mv 4189, batt_ma 0, batt_soc 81, batt_temp 31, batt_status 1, batt_sn SB18D10750, batt_fv_mv 0, batt_fcc_ma 0
08-10 10:47:29.236 19741 19741 I mmi_charger: mmi_get_charger_info: [C:qti_glink_charger]: chrg_present 1, chrg_type 1, chrg_pmax_mw 2500, chrg_mv 4871, chrg_ma 503, chrg_otg_enabled 0, thermal_level 0
08-10 10:47:29.236 19741 19741 I mmi_charger: mmi_update_charger_status: [C:qti_glink_charger]: StepChg: MAX, TempZone: 0, LimitMode: 2, DemoSuspend: 0
08-10 10:47:29.236 19741 19741 I mmi_charger: mmi_configure_charger: [C:qti_glink_charger]: FV=4400, FCC=4000, CDIS=0, CSUS=0, CRES=0, CFULL=0
08-10 10:47:29.236 19741 19741 I mmi_charger: mmi_update_battery_status: Combo status: soc:81, status:1, temp:31, health:1, soh 4041000, age:92, cycles:0, voltage:4189, current:0, counter:6000000, rate:Normal, lpd:0, vbus:1
08-10 10:47:29.255  1233  1233 W healthd : battery l=82 v=4189 t=31.0 h=2 st=2 c=0 fc=4041000 cc=1678 chg=u
08-10 10:47:29.282  2736  2736 D QtiCarrierConfigHelper: WARNING, no carrier configs on phone Id: 1
08-10 10:47:29.282  1707  1707 W ActivityManager: Receiver with filter android.content.IntentFilter@3e1e672 already registered for pid 1707, callerPackage is android
08-10 10:47:29.284  1707  1707 I LineageHealth: isSupported mode called, param: 2, supported: 1
08-10 10:47:29.285  1707  1707 I LineageHealth: mIsPowerConnected: true, mBatteryPct: 82.0
08-10 10:47:29.285  1707  1707 I LineageHealth: Current battery level: 82.0, target: 90, limit set: false
08-10 10:47:29.284  1233  1233 W healthd : battery l=82 v=4189 t=31.0 h=2 st=2 c=0 fc=4041000 cc=1678 chg=u
08-10 10:47:29.293  2478  3124 D PowerUI : can't show warning due to - plugged: true status unknown: false
08-10 10:47:29.336 20320 20320 I sd 0    : 0:0:4: [sde] Synchronizing SCSI cache
08-10 10:47:29.339 20320 20320 I ufshpb_suspend: 3235 info: ufshpb_lu 4 goto suspend
08-10 10:47:29.339 20320 20320 I ufshpb_suspend: 3236 info: ufshpb_lu 4 changes suspend state
08-10 10:47:30.836  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:30.836  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:30.836  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:30.836  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:30.836  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:31.013  1236  2320 I slpi    : STK3A5X ALS_OC: lux:73.199997, last_ps:1[1711], CH0:50, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:31.218  1236  2320 I slpi    : STK3A5X ALS_OC: lux:92.232002, last_ps:1[1720], CH0:63, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:31.715  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:31.836  1236  2320 I slpi    : STK3A5X ALS_OC: lux:112.727997, last_ps:1[1714], CH0:77, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:31.939  1236  2320 I slpi    : STK3A5X ALS_OC: lux:136.151993, last_ps:1[1720], CH0:93, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:32.044  1236  2320 I slpi    : STK3A5X ALS_OC: lux:153.720001, last_ps:1[1718], CH0:105, CH2:5, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:32.568  2914 20938 I NearbyMediums: No BLE Fast/GATT advertisements found in the latest cycle.
08-10 10:47:32.571  2914 20955 I NearbyConnections: Processing lost BlePeripheral BlePeripheral{advertisement=BleAdvertisement { version=2, socketVersion=2, isFast=true, serviceIdHash=null, data=[ 0x23 0x4e 0x55 0x41 0x48 0x11 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ], deviceToken=[ 0x8f 0xcf ], rxAdvertisement=null }, dctAdvertisement=null, address=null, psm=0, deviceToken=<ByteString@b9f7a64 size=2 contents="\217\317">, isSecondProfile=false, rxInstantConnectionAdv=null}.
08-10 10:47:32.572  2914 20955 I NearbyConnections: Lost BleEndpoint for BlePeripheral BlePeripheral{advertisement=BleAdvertisement { version=2, socketVersion=2, isFast=true, serviceIdHash=null, data=[ 0x23 0x4e 0x55 0x41 0x48 0x11 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ], deviceToken=[ 0x8f 0xcf ], rxAdvertisement=null }, dctAdvertisement=null, address=null, psm=0, deviceToken=<ByteString@b9f7a64 size=2 contents="\217\317">, isSecondProfile=false, rxInstantConnectionAdv=null} (with EndpointId NUAH and EndpointInfo [ 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ]).
08-10 10:47:32.573  2914 20955 I NearbyConnections: ClientProxy(166113317) reporting onEndpointLost(NUAH)
08-10 10:47:32.575  2914 20956 I NearbySharing: NearbySharing Internal event EndpointLost(endpointId=NUAH)
08-10 10:47:32.851  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:32.851  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:32.852  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:32.852  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:32.852  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:34.746  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:34.864  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:34.864  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:34.864  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:34.864  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:34.865  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:34.936  1236  2320 I slpi    : STK3A5X ALS_OC: lux:130.296005, last_ps:1[1717], CH0:89, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:35.348  1236  2320 I slpi    : STK3A5X ALS_OC: lux:117.120003, last_ps:1[1715], CH0:80, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:35.968  1236  2320 I slpi    : STK3A5X ALS_OC: lux:101.015999, last_ps:1[1717], CH0:69, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:36.278  1236  2320 I slpi    : STK3A5X ALS_OC: lux:114.192001, last_ps:1[1713], CH0:78, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:36.691  1236  2320 I slpi    : STK3A5X ALS_OC: lux:125.903999, last_ps:1[1718], CH0:86, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:36.878  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:36.878  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:36.878  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:36.878  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:36.879  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:37.002  1236  2320 I slpi    : STK3A5X ALS_OC: lux:158.112000, last_ps:1[1720], CH0:108, CH2:5, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:37.779  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:38.240  1236  2320 I slpi    : STK3A5X ALS_OC: lux:136.151993, last_ps:1[1711], CH0:93, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:38.342  1236  2320 I slpi    : STK3A5X ALS_OC: lux:102.480003, last_ps:1[1707], CH0:70, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:38.422  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-10 10:47:38.422  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-10 10:47:38.424  1227  6058 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-10 10:47:38.424  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-10 10:47:38.432  2914 20955 I NearbyConnections: Found BleAdvertisement [ 0x23 0x4e 0x55 0x41 0x48 0x11 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ] (with EndpointId NUAH and EndpointInfo [ 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ])
08-10 10:47:38.433  2914 20955 I NearbyConnections: ClientProxy(166113317) reporting onEndpointFound(NUAH)
08-10 10:47:38.434  2914 20955 I NearbyConnections: ClientProxy(166113317) reporting onEndpointDistanceChanged(NUAH, UNKNOWN)
08-10 10:47:38.435  2914 20956 I NearbySharing: NearbySharing Internal event EndpointDiscovered(endpointId=NUAH)
08-10 10:47:38.444  1236  2320 I slpi    : STK3A5X ALS_OC: lux:81.984001, last_ps:1[1710], CH0:56, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:38.465  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-10 10:47:38.465  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-10 10:47:38.617  1227  5890 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-10 10:47:38.890  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:38.890  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:38.890  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:38.891  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:38.891  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:39.418 20320 20320 I ufshpb_resume: 3261 info: ufshpb_lu 4 resume. do_task_work 0 retry 0
08-10 10:47:39.949  1707  2865 W ActivityTaskManager: callingPackage for (uid=2000, pid=20964) has no WPC
08-10 10:47:39.951  1707  2865 V GrammaticalInflectionUtils: AttributionSource: AttributionSource { uid = 10507, packageName = null, attributionTag = null, token = android.os.Binder@a30b359, deviceId = 0, next = null } does not have READ_SYSTEM_GRAMMATICAL_GENDER permission.
08-10 10:47:39.952  1707  2865 W OptProp : Cannot read opt property android.window.PROPERTY_COMPAT_ALLOW_RESIZEABLE_ACTIVITY_OVERRIDES
08-10 10:47:39.952  1707  2865 W OptProp : Cannot read opt property android.window.PROPERTY_COMPAT_ALLOW_MIN_ASPECT_RATIO_OVERRIDE
08-10 10:47:39.956  1331  1579 D DisplayModeController: setDesiredMode 4630947043778501762 {mode={fps=60.00 Hz, modePtr={id=0, vsyncRate=60.00 Hz, peakRefreshRate=60.00 Hz}}, emitEvent=true, force=false}
08-10 10:47:39.958  1707  2865 W OptProp : Cannot read opt property android.window.PROPERTY_COMPAT_ALLOW_ORIENTATION_OVERRIDE
08-10 10:47:39.960  1331  1396 I BpBinder: onLastStrongRef automatically unlinking death recipients: 
08-10 10:47:39.960  1257  1361 I SDM     : HWDeviceDRM::UpdateMixerAttributes: Mixer WxH 1080x2400-0 for Peripheral
08-10 10:47:39.960  1257  1361 I SDM     : HWCDisplay::SubmitDisplayConfig: Active configuration changed to: 0
08-10 10:47:39.961  1235  1235 I android.hardware.power-service-qti: Power setMode: 5 to: 1
08-10 10:47:39.962  1707  2865 V SplashScreenExceptionList: SplashScreen checking exception for package com.anonymous.videodownloaderapp (target sdk:34) -> false
08-10 10:47:39.963   563   563 I         : (mipi_mot_cmd_csot_1080p_dsc_667)+
08-10 10:47:39.966  1707  2865 I ActivityTaskManager: START u0 {flg=0x10000000 cmp=com.anonymous.videodownloaderapp/.MainActivity} with LAUNCH_MULTIPLE from uid 2000 (BAL_ALLOW_PERMISSION) result code=0
08-10 10:47:39.967  2478  2505 W HWUI    : Image decoding logging dropped!
08-10 10:47:39.971  1707  1860 V GrammaticalInflectionUtils: AttributionSource: AttributionSource { uid = 10507, packageName = null, attributionTag = null, token = android.os.Binder@a30b359, deviceId = 0, next = null } does not have READ_SYSTEM_GRAMMATICAL_GENDER permission.
08-10 10:47:39.971  2478  2498 V WindowManagerShell: Transition requested (#208): android.os.BinderProxy@50611e1 TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=51 effectiveUid=10507 displayId=0 isRunning=true baseIntent=Intent { flg=0x10000000 cmp=com.anonymous.videodownloaderapp/.MainActivity } baseActivity=ComponentInfo{com.anonymous.videodownloaderapp/com.anonymous.videodownloaderapp.MainActivity} topActivity=ComponentInfo{com.anonymous.videodownloaderapp/com.anonymous.videodownloaderapp.MainActivity} origActivity=null realActivity=ComponentInfo{com.anonymous.videodownloaderapp/com.anonymous.videodownloaderapp.MainActivity} numActivities=1 lastActiveTime=9868993 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@20e5b06} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 90 - 0, 0) topActivityInfo=ActivityInfo{c7cc5c7 com.anonymous.videodownloaderapp.MainActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(288, 705 - 792, 1785) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= false isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityLetterboxAppWidth=-1 topActivityLetterboxAppHeight=-1 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 208 }
08-10 10:47:39.971  2478  2498 D ShellSplitScreen: logExit: no-op, mLoggerSessionId is null
08-10 10:47:39.971  2615  2615 W SplitSelectStateCtor: Missing session instanceIds
08-10 10:47:39.971  2615  2615 D StatsLog: LAUNCHER_SPLIT_SELECTION_EXIT_INTERRUPTED
08-10 10:47:39.972  2478  2498 D WindowManagerShell: setLauncherKeepClearAreaHeight: visible=false, height=260
08-10 10:47:39.972  1707  1860 W ActivityManager: ProcessRecord{bd54096 0:com.anonymous.videodownloaderapp/u0a507}: ART verification disabled
08-10 10:47:39.974  1707  1921 W libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
08-10 10:47:39.975  2478  2505 D SplashScreenView: Build android.window.SplashScreenView{e2caf60 V.E...... ......ID 0,0-0,0}
08-10 10:47:39.975  2478  2505 D SplashScreenView: Icon: view: android.widget.ImageView{ab61c19 V.ED..... ......I. 0,0-0,0 #1020525 android:id/splashscreen_icon_view} drawable: com.android.wm.shell.startingsurface.SplashscreenIconDrawableFactory$ImmobileIconDrawable@6fc4de size: 400
08-10 10:47:39.975  2478  2505 D SplashScreenView: Branding: view: android.view.View{f46a6bf G.ED..... ......I. 0,0-0,0 #1020524 android:id/splashscreen_branding_view} drawable: null size w: 0 h: 0
08-10 10:47:39.977  1116 20583 I netd    : tetherGetStats() -> {[TetherStatsParcel{iface: wlan0, rxBytes: 268259300, rxPackets: 111317, txBytes: 49967790, txPackets: 63712, ifIndex: 0}]} <3.35ms>
08-10 10:47:39.978  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.978  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.979   563   563 I dsi_panel_set_cabc: Set CABC to (0)
08-10 10:47:39.980   563   563 I         : Set ACL to (0)
08-10 10:47:39.980   563   563 I         : Set DC to (1)
08-10 10:47:39.981  1119  1119 D Zygote  : Forked child process 20968
08-10 10:47:39.982  1707  1871 I ActivityManager: Start proc 20968:com.anonymous.videodownloaderapp/u0a507 for next-top-activity {com.anonymous.videodownloaderapp/com.anonymous.videodownloaderapp.MainActivity}
08-10 10:47:39.983  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.985  1707  2865 D CoreBackPreview: Window{9d4a658 u0 Splash Screen com.anonymous.videodownloaderapp}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@219d004, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
08-10 10:47:39.985  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.985  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.985  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.985  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.989  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.990  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  3994  4003 D ForegroundUtils: Foreground UID status:
08-10 10:47:39.991  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 1002 is in foreground: true
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10140 is in foreground: true
08-10 10:47:39.991  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10176 is in foreground: true
08-10 10:47:39.991  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10183 is in foreground: true
08-10 10:47:39.991  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10190 is in foreground: true
08-10 10:47:39.991  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10194 is in foreground: false
08-10 10:47:39.991  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10221 is in foreground: false
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10225 is in foreground: false
08-10 10:47:39.991  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10233 is in foreground: false
08-10 10:47:39.991  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10238 is in foreground: false
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10239 is in foreground: false
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10245 is in foreground: false
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10255 is in foreground: false
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 10469 is in foreground: false
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 1010137 is in foreground: true
08-10 10:47:39.991  3994  4003 D ForegroundUtils: UID: 1010190 is in foreground: true
08-10 10:47:39.993 20968 20968 I eodownloaderapp: Late-enabling -Xcheck:jni
08-10 10:47:39.998  2478  2821 V WindowManagerShell: onTransitionReady(transaction=7331509186225)
08-10 10:47:39.999  1707  1857 V WindowManager: Sent Transition (#208) createdAt=08-10 10:47:39.952 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=51 effectiveUid=10507 displayId=0 isRunning=true baseIntent=Intent { flg=0x10000000 cmp=com.anonymous.videodownloaderapp/.MainActivity } baseActivity=ComponentInfo{com.anonymous.videodownloaderapp/com.anonymous.videodownloaderapp.MainActivity} topActivity=ComponentInfo{com.anonymous.videodownloaderapp/com.anonymous.videodownloaderapp.MainActivity} origActivity=null realActivity=ComponentInfo{com.anonymous.videodownloaderapp/com.anonymous.videodownloaderapp.MainActivity} numActivities=1 lastActiveTime=9868993 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{ed647e9 Task{e20ff6c #51 type=standard A=10507:com.anonymous.videodownloaderapp}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 90 - 0, 0) topActivityInfo=ActivityInfo{25bcc6e com.anonymous.videodownloaderapp.MainActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(288, 705 - 792, 1785) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= false isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityLetterboxAppWidth=-1 topActivityLetterboxAppHeight=-1 isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 208 }
08-10 10:47:39.999  1707  1857 V WindowManager:     startWCT=WindowContainerTransaction { changes= {} hops= [] errorCallbackToken=null taskFragmentOrganizer=null }
08-10 10:47:39.999  1707  1857 V WindowManager:     info={id=208 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[
08-10 10:47:39.999  1707  1857 V WindowManager:         {WCT{RemoteToken{ed647e9 Task{e20ff6c #51 type=standard A=10507:com.anonymous.videodownloaderapp}}} m=OPEN f=NONE leash=Surface(name=Task=51)/@0x976f9ed sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
08-10 10:47:39.999  1707  1857 V WindowManager:         {WCT{RemoteToken{65e4f0d Task{2ea6ff9 #1 type=home}}} m=TO_BACK f=SHOW_WALLPAPER leash=Surface(name=Task=1)/@0xe810d15 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1}
08-10 10:47:39.999  1707  1857 V WindowManager:     ]}
08-10 10:47:39.999  2478  2498 V WindowManagerShell: onTransitionReady (#208) android.os.BinderProxy@50611e1: {id=208 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[
08-10 10:47:39.999  2478  2498 V WindowManagerShell:         {m=OPEN f=NONE leash=Surface(name=Task=51)/@0xcc7d58d sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
08-10 10:47:39.999  2478  2498 V WindowManagerShell:         {m=TO_BACK f=SHOW_WALLPAPER leash=Surface(name=Task=1)/@0xc351342 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1}
08-10 10:47:39.999  2478  2498 V WindowManagerShell:     ]}
08-10 10:47:39.999  2478  2498 V WindowManagerShell: Playing animation for (#208) android.os.BinderProxy@50611e1@0
08-10 10:47:40.000  2478  2498 V ShellRecents: RecentsTransitionHandler.startAnimation: no controller found
08-10 10:47:40.000  2478  2498 V WindowManagerShell: Transition doesn't have explicit remote, search filters for match for {id=208 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{m=OPEN f=NONE leash=Surface(name=Task=51)/@0xcc7d58d sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},{m=TO_BACK f=SHOW_WALLPAPER leash=Surface(name=Task=1)/@0xc351342 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1}]}
08-10 10:47:40.000  2478  2498 V WindowManagerShell:  Checking filter Pair{{types=[] flags=0x0] notFlags=0x100 checks=[{atype=home independent=true modes=[OPEN,TO_FRONT] flags=NONE mustBeTask=false order=TOP topActivity=ComponentInfo{com.android.launcher3/com.android.launcher3.uioverrides.QuickstepLauncher} launchCookie=null windowingMode=undefined},{atype=standard independent=true modes=[CLOSE,TO_BACK] flags=NONE mustBeTask=false order=ANY topActivity=null launchCookie=null windowingMode=undefined},{NOT atype=undefined independent=true modes=[] flags=NONE mustBeTask=true order=ANY topActivity=null launchCookie=null customAnim=true windowingMode=undefined}]} RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@43ce1a8, appThread = android.app.IApplicationThread$Stub$Proxy@a9d74c1, debugName = QuickstepLaunchHome }}
08-10 10:47:40.000  2478  2498 V WindowManagerShell:  Delegate animation for (#208) to null
08-10 10:47:40.000  2478  2498 V WindowManagerShell: start default transition animation, info = {id=208 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{m=OPEN f=NONE leash=Surface(name=Task=51)/@0xcc7d58d sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},{m=TO_BACK f=SHOW_WALLPAPER leash=Surface(name=Task=1)/@0xc351342 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1}]}
08-10 10:47:40.000  2478  2498 V WindowManagerShell: loadAnimation: anim=android.view.animation.AnimationSet@9e93d89 animAttr=0x13 type=OPEN isEntrance=false
08-10 10:47:40.000  2478  2498 V WindowManagerShell: loadAnimation: anim=android.view.animation.AnimationSet@e0245bc animAttr=0x12 type=OPEN isEntrance=true
08-10 10:47:40.001  2478  2498 V WindowManagerShell:  animated by com.android.wm.shell.transition.DefaultTransitionHandler@fe42646
08-10 10:47:40.005  1707  4309 W UserManagerService: Requested status bar icon for non-badged user 0
08-10 10:47:40.010 20968 20968 I eodownloaderapp: Using CollectorTypeCMC GC.
08-10 10:47:40.012 20589 20596 I adbd    : jdwp connection from 20968
08-10 10:47:40.015 20968 20968 D nativeloader: Load libframework-connectivity-tiramisu-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity-t.jar: ok
08-10 10:47:40.024  1707  1921 W libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
08-10 10:47:40.024  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:40.025  3994  4003 D ForegroundUtils: Foreground UID status:
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 1002 is in foreground: true
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10140 is in foreground: true
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10176 is in foreground: true
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10183 is in foreground: true
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10190 is in foreground: true
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10194 is in foreground: false
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10221 is in foreground: false
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10225 is in foreground: false
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10233 is in foreground: false
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10238 is in foreground: false
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10239 is in foreground: false
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10245 is in foreground: false
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10255 is in foreground: false
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10469 is in foreground: false
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 10507 is in foreground: true
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 1010137 is in foreground: true
08-10 10:47:40.025  3994  4003 D ForegroundUtils: UID: 1010190 is in foreground: true
08-10 10:47:40.030 20968 20968 D CompatChangeReporter: Compat change id reported: 242716250; UID 10507; state: ENABLED
08-10 10:47:40.053  1331  1331 I BpBinder: onLastStrongRef automatically unlinking death recipients: 
08-10 10:47:40.055 20968 20968 D ApplicationLoaders: Returning zygote-cached class loader: /system_ext/framework/androidx.window.extensions.jar
08-10 10:47:40.055 20968 20968 D ApplicationLoaders: Returning zygote-cached class loader: /system_ext/framework/androidx.window.sidecar.jar
08-10 10:47:40.056 20968 20968 W ziparchive: Unable to open '/data/app/~~ZccQexSOGWK6X7XlAT8ZWw==/com.anonymous.videodownloaderapp-gkU7ANlAVkUhyLCmz4R9Ig==/base.dm': No such file or directory
08-10 10:47:40.056 20968 20968 W ziparchive: Unable to open '/data/app/~~ZccQexSOGWK6X7XlAT8ZWw==/com.anonymous.videodownloaderapp-gkU7ANlAVkUhyLCmz4R9Ig==/base.dm': No such file or directory
08-10 10:47:40.072 17070 17070 I bmex    : onPause
08-10 10:47:40.200  1236  2320 I slpi    : STK3A5X ALS_OC: lux:102.480003, last_ps:1[1713], CH0:70, CH2:2, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:40.282  2478  2498 V WindowManagerShell: Transition animation finished (aborted=false), notifying core (#208) android.os.BinderProxy@50611e1@0
08-10 10:47:40.283  1707  1857 V WindowManager: Finish Transition (#208): created at 08-10 10:47:39.952 collect-started=0.105ms request-sent=13.151ms started=18.617ms ready=43.22ms sent=44.647ms finished=330.245ms
08-10 10:47:40.283 17070 17070 D VRI[0 fl=}]: visibilityChanged oldVisibility=true newVisibility=false
08-10 10:47:40.283  2615  2615 D VRI[QuickstepLauncher]: visibilityChanged oldVisibility=true newVisibility=false
08-10 10:47:40.284  2478  2498 V WindowManagerShell: Track 0 became idle
08-10 10:47:40.284  2478  2498 V WindowManagerShell: All active transition animations finished
08-10 10:47:40.284  1707  4309 W UserManagerService: Requested status bar icon for non-badged user 0
08-10 10:47:40.285  1707  4309 W UserManagerService: Requested status bar icon for non-badged user 0
08-10 10:47:40.304  1236  2320 I slpi    : STK3A5X ALS_OC: lux:52.222935, last_ps:1[1714], CH0:118, CH2:2, CH4:11, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:40.336 20968 20968 D nativeloader: Configuring clns-7 for other apk /data/app/~~ZccQexSOGWK6X7XlAT8ZWw==/com.anonymous.videodownloaderapp-gkU7ANlAVkUhyLCmz4R9Ig==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~ZccQexSOGWK6X7XlAT8ZWw==/com.anonymous.videodownloaderapp-gkU7ANlAVkUhyLCmz4R9Ig==/lib/arm64:/data/app/~~ZccQexSOGWK6X7XlAT8ZWw==/com.anonymous.videodownloaderapp-gkU7ANlAVkUhyLCmz4R9Ig==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.anonymous.videodownloaderapp
08-10 10:47:40.340 20968 20968 I eodownloaderapp: AssetManager2(0xb4000077861bd758) locale list changing from [] to [pt-BR]
08-10 10:47:40.343 20968 20968 V GraphicsEnvironment: Currently set values for:
08-10 10:47:40.343 20968 20968 V GraphicsEnvironment:   angle_gl_driver_selection_pkgs=[]
08-10 10:47:40.343 20968 20968 V GraphicsEnvironment:   angle_gl_driver_selection_values=[]
08-10 10:47:40.343 20968 20968 V GraphicsEnvironment: com.anonymous.videodownloaderapp is not listed in per-application setting
08-10 10:47:40.344 20968 20968 V GraphicsEnvironment: Neither updatable production driver nor prerelease driver is supported.
08-10 10:47:40.349 20968 20968 D WM-WrkMgrInitializer: Initializing WorkManager with default configuration.
08-10 10:47:40.358 20968 20968 D WM-PackageManagerHelper: Skipping component enablement for androidx.work.impl.background.systemjob.SystemJobService
08-10 10:47:40.358 20968 20968 D WM-Schedulers: Created SystemJobScheduler and enabled SystemJobService
08-10 10:47:40.379  1707  4309 D FileUtils: Rounded bytes from 7740846080 to 8000000000
08-10 10:47:40.396  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:40.396  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:40.404 20968 20968 I eodownloaderapp: AssetManager2(0xb4000077861b7678) locale list changing from [] to [pt-BR]
08-10 10:47:40.407  1236  2320 I slpi    : STK3A5X ALS_OC: lux:196.175995, last_ps:1[1716], CH0:134, CH2:3, CH4:12, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:40.421 20968 20968 D DesktopModeFlagsUtil: Toggle override initialized to: OVERRIDE_UNSET
08-10 10:47:40.537  1707  4309 D CoreBackPreview: Window{12230b8 u0 com.anonymous.videodownloaderapp/com.anonymous.videodownloaderapp.MainActivity}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@9cfb9f7, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
08-10 10:47:40.625 20968 20968 I eodownloaderapp: AssetManager2(0xb4000077861c5138) locale list changing from [] to [pt-BR]
08-10 10:47:40.625 20968 20968 I WebViewFactory: Loading com.android.webview version 138.0.7204.63 (code 720406301)
08-10 10:47:40.627 20968 20968 I eodownloaderapp: AssetManager2(0xb4000077861bf698) locale list changing from [] to [pt-BR]
08-10 10:47:40.628 20968 20968 I eodownloaderapp: AssetManager2(0xb4000077861c60d8) locale list changing from [] to [pt-BR]
08-10 10:47:40.629 20968 20968 I eodownloaderapp: AssetManager2(0xb4000077861c2ed8) locale list changing from [] to [pt-BR]
08-10 10:47:40.630 20968 20968 V ResourcesManager: The following library key has been added: ResourcesKey{ mHash=d5fbdc95 mResDir=null mSplitDirs=[] mOverlayDirs=[/product/overlay/TransparentNavigationBar/TransparentNavigationBarOverlay.apk,/product/overlay/NavigationBarModeGestural/NavigationBarModeGesturalOverlay.apk,/product/overlay/IconPackSamAndroid/IconPackSamAndroidOverlay.apk,/data/resource-cache/com.android.systemui-accent-Gp03.frro,/data/resource-cache/com.android.systemui-dynamic-KKcy.frro,/data/resource-cache/com.android.systemui-neutral-1B7u.frro] mLibDirs=[/product/app/webview/webview.apk,/system_ext/framework/androidx.window.extensions.jar] mDisplayId=0 mOverrideConfig=v35 mCompatInfo={400dpi always-compat} mLoaders=[]}
08-10 10:47:40.630 20968 20968 D ApplicationLoaders: Returning zygote-cached class loader: /system_ext/framework/androidx.window.extensions.jar
08-10 10:47:40.632 20968 20968 D nativeloader: InitLlndkLibrariesProduct: libEGL.so:libGLESv1_CM.so:libGLESv2.so:libGLESv3.so:libRS.so:libandroid_net.so:libapexsupport.so:libavf.so:libbinder_ndk.so:libc.so:libcgrouprc.so:libclang_rt.asan-aarch64-android.so:libclang_rt.asan-arm-android.so:libclang_rt.hwasan-aarch64-android.so:libcom.android.tethering.connectivity_native.so:libdl.so:libft2.so:liblog.so:libm.so:libmediandk.so:libnativewindow.so:libneuralnetworks.so:libselinux.so:libsync.so:libvendorsupport.so:libvndksupport.so:libvulkan.so
08-10 10:47:40.632 20968 20968 D nativeloader: Configuring product-clns-8 for unbundled product apk /product/app/webview/webview.apk. target_sdk_version=35, uses_libraries=, library_path=/product/app/webview/lib/arm64:/product/app/webview/webview.apk!/lib/arm64-v8a:/product/lib64:/system/product/lib64, permitted_path=/data:/mnt/expand:/product/lib64:/system/product/lib64
08-10 10:47:40.633 20968 20968 D nativeloader: InitVndkspLibrariesProduct: VNDK is deprecated with product
08-10 10:47:40.646 20968 20968 I cr_WVCFactoryProvider: version=138.0.7204.63 (720406301) minSdkVersion=26 isBundle=false multiprocess=true packageId=2
08-10 10:47:40.661 20968 20968 D nativeloader: Load /product/app/webview/webview.apk!/lib/arm64-v8a/libwebviewchromium.so using class loader ns product-clns-8 (caller=/product/app/webview/webview.apk): ok
08-10 10:47:40.662 20968 20968 D nativeloader: Load /system/lib64/libwebviewchromium_plat_support.so using class loader ns product-clns-8 (caller=/product/app/webview/webview.apk): ok
08-10 10:47:40.670 20968 20968 I cr_LibraryLoader: Successfully loaded native library
08-10 10:47:40.671 20968 20968 I cr_CachingUmaRecorder: Flushed 8 samples from 8 histograms, 0 samples were dropped.
08-10 10:47:40.673 20968 20968 I cr_CombinedPProvider: #registerProvider() provider:WV.v7@ccb60ab isPolicyCacheEnabled:false policyProvidersSize:0
08-10 10:47:40.673 20968 20968 I cr_PolicyProvider: #setManagerAndSource() 0
08-10 10:47:40.676  1707  4309 V GrammaticalInflectionUtils: AttributionSource: AttributionSource { uid = 99039, packageName = null, attributionTag = null, token = android.os.Binder@a30b359, deviceId = 0, next = null } does not have READ_SYSTEM_GRAMMATICAL_GENDER permission.
08-10 10:47:40.677 20968 20968 I eodownloaderapp: AssetManager2(0xb4000077861c3e78) locale list changing from [] to [pt-BR]
08-10 10:47:40.679 20968 20968 I eodownloaderapp: AssetManager2(0xb4000077861c3b58) locale list changing from [] to [pt-BR]
08-10 10:47:40.681  2600  2600 D Zygote  : Forked child process 21003
08-10 10:47:40.683  1707  1871 I ActivityManager: Start proc 21003:com.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0/u0i39 for  {com.anonymous.videodownloaderapp/org.chromium.content.app.SandboxedProcessService0:0}
08-10 10:47:40.687 21003 21003 I ocessService0:0: Using CollectorTypeCMC GC.
08-10 10:47:40.689 21003 21003 E ocessService0:0: Not starting debugger since process cannot load the jdwp agent.
08-10 10:47:40.690 20968 20968 I cr_CombinedPProvider: #linkNativeInternal() 1
08-10 10:47:40.691 21003 21003 D nativeloader: Load libframework-connectivity-tiramisu-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity-t.jar: ok
08-10 10:47:40.691 20968 20968 I cr_AppResProvider: #getApplicationRestrictionsFromUserManager() Bundle[EMPTY_PARCEL]
08-10 10:47:40.691 20968 20968 I cr_PolicyProvider: #notifySettingsAvailable() 0
08-10 10:47:40.691 20968 20968 I cr_CombinedPProvider: #onSettingsAvailable() 0
08-10 10:47:40.692 20968 20968 I cr_CombinedPProvider: #flushPolicies()
08-10 10:47:40.698  3994  4003 D ForegroundUtils: Foreground UID status:
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 1002 is in foreground: true
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10140 is in foreground: true
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10176 is in foreground: true
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10183 is in foreground: true
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10190 is in foreground: true
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10194 is in foreground: false
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10221 is in foreground: false
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10225 is in foreground: false
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10233 is in foreground: false
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10238 is in foreground: false
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10239 is in foreground: false
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10245 is in foreground: false
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10255 is in foreground: false
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10469 is in foreground: false
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 10507 is in foreground: true
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 1010137 is in foreground: true
08-10 10:47:40.698  3994  4003 D ForegroundUtils: UID: 1010190 is in foreground: true
08-10 10:47:40.705 21003 21003 D ApplicationLoaders: Returning zygote-cached class loader: /system_ext/framework/androidx.window.extensions.jar
08-10 10:47:40.705 21003 21003 D CompatChangeReporter: Compat change id reported: 202956589; UID 99039; state: ENABLED
08-10 10:47:40.703   844   844 W servicemanager: Isolated app with UID 99039 requested 'network_management', but the service is not allowed for isolated apps.
08-10 10:47:40.708 21003 21003 I ocessService0:0: AssetManager2(0xe8c82f54) locale list changing from [] to [pt-BR]
08-10 10:47:40.708   844   844 W servicemanager: Isolated app with UID 99039 requested 'connectivity', but the service is not allowed for isolated apps.
08-10 10:47:40.711 21003 21003 I cr_WebViewApkApp: version=138.0.7204.63 (720406301) minSdkVersion=26 isBundle=false processName=com.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0
08-10 10:47:40.713  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:40.712   844   844 E SELinux : avc:  denied  { find } for pid=21003 uid=99039 name=content_capture scontext=u:r:isolated_app:s0:c512,c768 tcontext=u:object_r:content_capture_service:s0 tclass=service_manager permissive=0
08-10 10:47:40.714  1331  1579 D DisplayModeController: setDesiredMode 4630947043778501762 {mode={fps=48.00 Hz, modePtr={id=4, vsyncRate=48.00 Hz, peakRefreshRate=48.00 Hz}}, emitEvent=false, force=false}
08-10 10:47:40.715 21003 21003 I cr_ChildProcessService: Creating new ChildProcessService pid=21003
08-10 10:47:40.721 20968 20968 W chromium: [WARNING:android_webview/browser/network_service/net_helpers.cc:143] HTTP Cache size is: 20971520
08-10 10:47:40.728  1257  1361 I SDM     : HWDeviceDRM::UpdateMixerAttributes: Mixer WxH 1080x2400-0 for Peripheral
08-10 10:47:40.729  1257  1361 I SDM     : HWCDisplay::SubmitDisplayConfig: Active configuration changed to: 4
08-10 10:47:40.731   563   563 I         : (mipi_mot_cmd_csot_1080p_dsc_667)+
08-10 10:47:40.736  1707  4309 D ActivityManager: sync unfroze 12432 com.android.webview:webview_service for 6
08-10 10:47:40.736  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:40.737  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:40.737  1707  4309 W ProcessStats: Tracking association SourceState{d2edbad com.anonymous.videodownloaderapp/10507 BTop #28977} whose proc state 2 is better than process ProcessState{95ce61f com.android.webview:webview_service/10162 pkg=com.android.webview} proc state 14 (0 skipped)
08-10 10:47:40.739 12432 21032 W cr_MetricsBridgeService: retained records has reached the max capacity, dropping record
08-10 10:47:40.752   563   563 I dsi_panel_set_cabc: Set CABC to (0)
08-10 10:47:40.752   563   563 I         : Set ACL to (0)
08-10 10:47:40.752   563   563 I         : Set DC to (1)
08-10 10:47:40.756  1707  4795 D ConnectivityService: requestNetwork for uid/pid:10507/20968 activeRequest: null callbackRequest: 1507 [NetworkRequest [ REQUEST id=1508, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10507 RequestorUid: 10507 RequestorPkg: com.anonymous.videodownloaderapp UnderlyingNetworks: Null] ]] callback flags: 0 order: 2147483647 isUidTracked: false declaredMethods: ALL
08-10 10:47:40.758  1707  2384 D ConnectivityService: NetReassign [1508 : null → 101] [c 0] [a 1] [i 1]
08-10 10:47:40.758  1707  2376 D WifiNetworkFactory: got request NetworkRequest [ REQUEST id=1508, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10507 RequestorUid: 10507 RequestorPkg: com.anonymous.videodownloaderapp UnderlyingNetworks: Null] ]
08-10 10:47:40.758  1707  2376 D UntrustedWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1508, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10507 RequestorUid: 10507 RequestorPkg: com.anonymous.videodownloaderapp UnderlyingNetworks: Null] ]
08-10 10:47:40.758  1707  2376 D OemPaidWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1508, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10507 RequestorUid: 10507 RequestorPkg: com.anonymous.videodownloaderapp UnderlyingNetworks: Null] ]
08-10 10:47:40.758  1707  2376 D MultiInternetWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1508, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10507 RequestorUid: 10507 RequestorPkg: com.anonymous.videodownloaderapp UnderlyingNetworks: Null] ]
08-10 10:47:40.760  1707  2384 D ConnectivityService: NetReassign [no changes] [c 0] [a 0] [i 1]
08-10 10:47:40.795 20968 21023 W cr_media: BLUETOOTH_CONNECT permission is missing.
08-10 10:47:40.796 20968 21023 W cr_media: getBluetoothAdapter() requires BLUETOOTH permission
08-10 10:47:40.796 20968 21023 W cr_media: registerBluetoothIntentsIfNeeded: Requires BLUETOOTH permission
08-10 10:47:40.801  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:40.805 20968 21014 W WebView : java.lang.Throwable: A WebView method was called on thread 'ThreadPoolForeg'. All WebView methods must be called on the same thread. (Expected Looper Looper (main, tid 2) {90c5e33} called on null, FYI main Looper is Looper (main, tid 2) {90c5e33})
08-10 10:47:40.805 20968 21014 W WebView : 	at android.webkit.WebView.checkThread(WebView.java:2629)
08-10 10:47:40.805 20968 21014 W WebView : 	at android.webkit.WebView.getUrl(WebView.java:1271)
08-10 10:47:40.805 20968 21014 W WebView : 	at com.anonymous.videodownloaderapp.features.browser.components.BrowserWebViewKt$BrowserWebView$5$1$1$5.shouldInterceptRequest(BrowserWebView.kt:174)
08-10 10:47:40.805 20968 21014 W WebView : 	at WV.x5.a(chromium-SystemWebView.apk-default-720406301:95)
08-10 10:47:40.805 20968 21014 W WebView : 	at org.chromium.android_webview.ShouldInterceptRequestMediator.shouldInterceptRequestFromNative(chromium-SystemWebView.apk-default-720406301:6)
08-10 10:47:40.805 20968 21014 D StrictMode: StrictMode policy violation: android.os.strictmode.WebViewMethodCalledOnWrongThreadViolation
08-10 10:47:40.805 20968 21014 D StrictMode: 	at android.webkit.WebView.checkThread(WebView.java:2629)
08-10 10:47:40.805 20968 21014 D StrictMode: 	at android.webkit.WebView.getUrl(WebView.java:1271)
08-10 10:47:40.805 20968 21014 D StrictMode: 	at com.anonymous.videodownloaderapp.features.browser.components.BrowserWebViewKt$BrowserWebView$5$1$1$5.shouldInterceptRequest(BrowserWebView.kt:174)
08-10 10:47:40.805 20968 21014 D StrictMode: 	at WV.x5.a(chromium-SystemWebView.apk-default-720406301:95)
08-10 10:47:40.805 20968 21014 D StrictMode: 	at org.chromium.android_webview.ShouldInterceptRequestMediator.shouldInterceptRequestFromNative(chromium-SystemWebView.apk-default-720406301:6)
08-10 10:47:40.805 21003 21022 I cr_LibraryLoader: Successfully loaded native library
08-10 10:47:40.805 20968 21014 E cr_shouldIntReqMed: Client raised exception in shouldInterceptRequest. Re-throwing on UI thread.
08-10 10:47:40.806 21003 21022 I cr_CachingUmaRecorder: Flushed 2 samples from 2 histograms, 0 samples were dropped.
08-10 10:47:40.807 20968 21036 D vulkan  : searching for layers in '/data/app/~~ZccQexSOGWK6X7XlAT8ZWw==/com.anonymous.videodownloaderapp-gkU7ANlAVkUhyLCmz4R9Ig==/lib/arm64'
08-10 10:47:40.808 20968 21036 D vulkan  : searching for layers in '/data/app/~~ZccQexSOGWK6X7XlAT8ZWw==/com.anonymous.videodownloaderapp-gkU7ANlAVkUhyLCmz4R9Ig==/base.apk!/lib/arm64-v8a'
08-10 10:47:40.816 21003 21022 W chromium: [WARNING:content/child/runtime_features.cc:556] Fenced frames cannot be enabled in this configuration. Use --enable-features=FencedFrames instead.
08-10 10:47:40.816 21003 21022 W chromium: [WARNING:content/child/runtime_features.cc:581] Topics cannot be enabled in this configuration. Use --enable-features=BrowsingTopics in addition.
08-10 10:47:40.816 21003 21022 W chromium: [WARNING:content/child/runtime_features.cc:602] SharedStorage cannot be enabled in this configuration. Use --enable-features=SharedStorageAPI in addition.
08-10 10:47:40.816 21003 21022 W chromium: [WARNING:content/child/runtime_features.cc:612] AttributionReporting cannot be enabled in this configuration. Use --enable-features=ConversionMeasurement in addition.
08-10 10:47:40.819 20968 21036 I AdrenoVK-0: ===== BEGIN DUMP OF OVERRIDDEN SETTINGS =====
08-10 10:47:40.819 20968 21036 I AdrenoVK-0: ===== END DUMP OF OVERRIDDEN SETTINGS =====
08-10 10:47:40.820 20968 21036 I AdrenoVK-0: QUALCOMM build          : ee3f1da111, Ie1e5aac4cc
08-10 10:47:40.820 20968 21036 I AdrenoVK-0: Build Date              : 01/11/24
08-10 10:47:40.820 20968 21036 I AdrenoVK-0: Shader Compiler Version : EV031.35.01.12
08-10 10:47:40.820 20968 21036 I AdrenoVK-0: Local Branch            : 
08-10 10:47:40.820 20968 21036 I AdrenoVK-0: Remote Branch           : refs/tags/AU_LINUX_ANDROID_LA.UM.9.14.11.00.00.571.148
08-10 10:47:40.820 20968 21036 I AdrenoVK-0: Remote Branch           : NONE
08-10 10:47:40.820 20968 21036 I AdrenoVK-0: Reconstruct Branch      : NOTHING
08-10 10:47:40.820 20968 21036 I AdrenoVK-0: Build Config            : S P 10.0.7 AArch64
08-10 10:47:40.820 20968 21036 I AdrenoVK-0: Driver Path             : /vendor/lib64/hw/vulkan.adreno.so
08-10 10:47:40.825 20968 21036 I AdrenoGLES-0: QUALCOMM build                   : ee3f1da111, Ie1e5aac4cc
08-10 10:47:40.825 20968 21036 I AdrenoGLES-0: Build Date                       : 01/11/24
08-10 10:47:40.825 20968 21036 I AdrenoGLES-0: OpenGL ES Shader Compiler Version: EV031.35.01.12
08-10 10:47:40.825 20968 21036 I AdrenoGLES-0: Local Branch                     : 
08-10 10:47:40.825 20968 21036 I AdrenoGLES-0: Remote Branch                    : refs/tags/AU_LINUX_ANDROID_LA.UM.9.14.11.00.00.571.148
08-10 10:47:40.825 20968 21036 I AdrenoGLES-0: Remote Branch                    : NONE
08-10 10:47:40.825 20968 21036 I AdrenoGLES-0: Reconstruct Branch               : NOTHING
08-10 10:47:40.825 20968 21036 I AdrenoGLES-0: Build Config                     : S P 10.0.7 AArch64
08-10 10:47:40.825 20968 21036 I AdrenoGLES-0: Driver Path                      : /vendor/lib64/egl/libGLESv2_adreno.so
08-10 10:47:40.829 20968 21036 I AdrenoGLES-0: PFP: 0x016dc094, ME: 0x00000000
08-10 10:47:40.833  2914  8379 I FontLog : Received query name=Google Sans&weight=700&besteffort=false, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
08-10 10:47:40.833  2914  8379 I FontLog : Query [name=Google Sans&weight=700&besteffort=false] resolved to {Google Sans, wdth 100.0, wght 700, ital 0.0, bestEffort false}, isVf false} [CONTEXT service_id=132 ]
08-10 10:47:40.834  2914  8379 I FontLog : Font PFD returned from cache for {Google Sans, wdth 100.0, wght 700, ital 0.0, bestEffort false}, isVf false} [CONTEXT service_id=132 ]
08-10 10:47:40.834  2914  8379 I FontLog : Fetch {Google Sans, wdth 100.0, wght 700, ital 0.0, bestEffort false}, isVf false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
08-10 10:47:40.835  1331  1579 D DisplayModeController: setDesiredMode 4630947043778501762 {mode={fps=60.00 Hz, modePtr={id=0, vsyncRate=60.00 Hz, peakRefreshRate=60.00 Hz}}, emitEvent=true, force=false}
08-10 10:47:40.836  2914  8379 I FontLog : Pulling font file for id = 307, cache size = 8 [CONTEXT service_id=132 ]
08-10 10:47:40.846  2914  8379 I FontLog : Received query name=Noto Color Emoji Compat&weight=400&besteffort=false, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
08-10 10:47:40.846  2914  8379 I FontLog : Query [name=Noto Color Emoji Compat&weight=400&besteffort=false] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false}, isVf false} [CONTEXT service_id=132 ]
08-10 10:47:40.848   844   844 I servicemanager: Caller(pid=20968,uid=10507,sid=u:r:untrusted_app:s0:c251,c257,c512,c768) Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest. No alternative instances declared in VINTF.
08-10 10:47:40.850  2914  8379 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false}, isVf false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
08-10 10:47:40.854  2914  8379 I FontLog : Pulling font file for id = 308, cache size = 9 [CONTEXT service_id=132 ]
08-10 10:47:40.857  1257  1361 I SDM     : HWDeviceDRM::UpdateMixerAttributes: Mixer WxH 1080x2400-0 for Peripheral
08-10 10:47:40.858  1257  1361 I SDM     : HWCDisplay::SubmitDisplayConfig: Active configuration changed to: 0
08-10 10:47:40.858   844   844 I servicemanager: Caller(pid=20968,uid=10507,sid=u:r:untrusted_app:s0:c251,c257,c512,c768) Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest. No alternative instances declared in VINTF.
08-10 10:47:40.859   563   563 I         : (mipi_mot_cmd_csot_1080p_dsc_667)+
08-10 10:47:40.861 20968 21046 I Gralloc4: Adding additional valid usage bits: 0x8202000
08-10 10:47:40.861  2914  8379 I FontLog : Received query name=Google Sans Flex&weight=400&besteffort=false, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
08-10 10:47:40.861  2914  8379 I FontLog : Query [name=Google Sans Flex&weight=400&besteffort=false] resolved to {Google Sans Flex, wdth 100.0, wght 400, ital 0.0, bestEffort false}, isVf false} [CONTEXT service_id=132 ]
08-10 10:47:40.862  2914  8379 I FontLog : Font PFD returned from cache for {Google Sans Flex, wdth 100.0, wght 400, ital 0.0, bestEffort false}, isVf false} [CONTEXT service_id=132 ]
08-10 10:47:40.862  2914  8379 I FontLog : Fetch {Google Sans Flex, wdth 100.0, wght 400, ital 0.0, bestEffort false}, isVf false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
08-10 10:47:40.865  2914  8379 I FontLog : Pulling font file for id = 309, cache size = 8 [CONTEXT service_id=132 ]
08-10 10:47:40.869  2914  8379 I FontLog : Received query name=Google Sans&weight=400&besteffort=false, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
08-10 10:47:40.869  2914  8379 I FontLog : Query [name=Google Sans&weight=400&besteffort=false] resolved to {Google Sans, wdth 100.0, wght 400, ital 0.0, bestEffort false}, isVf false} [CONTEXT service_id=132 ]
08-10 10:47:40.870  2914  8379 I FontLog : Font PFD returned from cache for {Google Sans, wdth 100.0, wght 400, ital 0.0, bestEffort false}, isVf false} [CONTEXT service_id=132 ]
08-10 10:47:40.870  2914  8379 I FontLog : Fetch {Google Sans, wdth 100.0, wght 400, ital 0.0, bestEffort false}, isVf false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
08-10 10:47:40.872  2914  8379 I FontLog : Pulling font file for id = 310, cache size = 9 [CONTEXT service_id=132 ]
08-10 10:47:40.875  2914  8379 I FontLog : Received query name=Google Sans&weight=500&besteffort=false, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
08-10 10:47:40.876  2914  8379 I FontLog : Query [name=Google Sans&weight=500&besteffort=false] resolved to {Google Sans, wdth 100.0, wght 500, ital 0.0, bestEffort false}, isVf false} [CONTEXT service_id=132 ]
08-10 10:47:40.876  2914  8379 I FontLog : Font PFD returned from cache for {Google Sans, wdth 100.0, wght 500, ital 0.0, bestEffort false}, isVf false} [CONTEXT service_id=132 ]
08-10 10:47:40.878  2914  8379 I FontLog : Fetch {Google Sans, wdth 100.0, wght 500, ital 0.0, bestEffort false}, isVf false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
08-10 10:47:40.876   563   563 I dsi_panel_set_cabc: Set CABC to (0)
08-10 10:47:40.876   563   563 I         : Set ACL to (0)
08-10 10:47:40.876   563   563 I         : Set DC to (1)
08-10 10:47:40.883  2914  8379 I FontLog : Pulling font file for id = 311, cache size = 7 [CONTEXT service_id=132 ]
08-10 10:47:40.888  2914 20959 W FontLog : Error updating last accessed time for {Google Sans, wdth 100.0, wght 500, ital 0.0, bestEffort false}, isVf false}. [CONTEXT service_id=132 ]
08-10 10:47:40.888  2914 20959 W FontLog : java.lang.IllegalStateException: Cannot perform this operation because the connection pool has been closed.
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteConnectionPool.throwIfClosedLocked(SQLiteConnectionPool.java:1108)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteConnectionPool.waitForConnection(SQLiteConnectionPool.java:721)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteConnectionPool.acquireConnection(SQLiteConnectionPool.java:404)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteSession.acquireConnection(SQLiteSession.java:931)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteSession.executeForChangedRowCount(SQLiteSession.java:789)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteStatement.executeUpdateDelete(SQLiteStatement.java:67)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteDatabase.executeSql(SQLiteDatabase.java:2235)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteDatabase.execSQL(SQLiteDatabase.java:2211)
08-10 10:47:40.888  2914 20959 W FontLog : 	at bqrw.run(:com.google.android.gms@253131035@25.31.31 (260400-791394948):57)
08-10 10:47:40.888  2914 20959 W FontLog : 	at bapc.c(:com.google.android.gms@253131035@25.31.31 (260400-791394948):50)
08-10 10:47:40.888  2914 20959 W FontLog : 	at bapc.run(:com.google.android.gms@253131035@25.31.31 (260400-791394948):70)
08-10 10:47:40.888  2914 20959 W FontLog : 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
08-10 10:47:40.888  2914 20959 W FontLog : 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
08-10 10:47:40.888  2914 20959 W FontLog : 	at bauu.run(:com.google.android.gms@253131035@25.31.31 (260400-791394948):8)
08-10 10:47:40.888  2914 20959 W FontLog : 	at java.lang.Thread.run(Thread.java:1119)
08-10 10:47:40.888  2914 20959 W FontLog : Caused by: java.lang.Exception: SQLiteConnectionPool.close()
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteConnectionPool.dispose(SQLiteConnectionPool.java:272)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteConnectionPool.close(SQLiteConnectionPool.java:252)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteDatabase.dispose(SQLiteDatabase.java:559)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteDatabase.onAllReferencesReleased(SQLiteDatabase.java:536)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteClosable.releaseReference(SQLiteClosable.java:92)
08-10 10:47:40.888  2914 20959 W FontLog : 	at android.database.sqlite.SQLiteDatabase.executeSql(SQLiteDatabase.java:2246)
08-10 10:47:40.888  2914 20959 W FontLog : 	... 8 more
08-10 10:47:40.889  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:40.889  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:40.889  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:40.890  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:40.890  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:40.902  1707  1857 W ziparchive: Unable to open '/data/app/~~ZccQexSOGWK6X7XlAT8ZWw==/com.anonymous.videodownloaderapp-gkU7ANlAVkUhyLCmz4R9Ig==/base.dm': No such file or directory
08-10 10:47:40.903  1707  1857 I ActivityTaskManager: Displayed com.anonymous.videodownloaderapp/.MainActivity for user 0: +950ms
08-10 10:47:40.905  1707  1921 W libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
08-10 10:47:40.909 20968 21054 I CameraManagerGlobal: Connecting to camera service
08-10 10:47:40.915 20968 21015 E chromium: [ERROR:third_party/crashpad/crashpad/util/file/directory_reader_posix.cc:43] opendir /data/user/0/com.anonymous.videodownloaderapp/cache/WebView/Crashpad/attachments/4ac6e283-65ba-4019-9250-b41b9890b81d: No such file or directory (2)
08-10 10:47:40.922 20968 21029 W VideoCapabilities: Unrecognized profile/level 0/3 for video/mpeg2
08-10 10:47:40.922 20968 21015 W chromium: [WARNING:third_party/crashpad/crashpad/handler/minidump_to_upload_parameters.cc:67] duplicate annotation name ptype, discarding value browser
08-10 10:47:40.924  1512  1639 I AttributionAndPermissionUtils: checkPermission (forDataDelivery 0 startDataDelivery 0): Permission hard denied for client attribution [uid 10507, pid 20968, packageName "<unknown>"]
08-10 10:47:40.928 20968 21029 W VideoCapabilities: Unsupported mime image/vnd.android.heic
08-10 10:47:40.957 20968 21029 W VideoCapabilities: Unrecognized profile/level 0/3 for video/mpeg2
08-10 10:47:40.957 20968 21029 W VideoCapabilities: Unrecognized profile/level 0/3 for video/mpeg2
08-10 10:47:40.961 20968 21029 W VideoCapabilities: Unsupported mime image/vnd.android.heic
08-10 10:47:40.962 20968 21029 W VideoCapabilities: Unsupported mime image/vnd.android.heic
08-10 10:47:40.974 20968 21015 D CompatChangeReporter: Compat change id reported: 263076149; UID 10507; state: ENABLED
08-10 10:47:40.992 20968 20968 I Choreographer: Skipped 31 frames!  The application may be doing too much work on its main thread.
08-10 10:47:41.028 12432 21058 W cr_MetricsBridgeService: retained records has reached the max capacity, dropping record
08-10 10:47:41.029 12432 21059 W cr_MetricsBridgeService: retained records has reached the max capacity, dropping record
08-10 10:47:41.029 12432 21059 W cr_MetricsBridgeService: retained records has reached the max capacity, dropping record
08-10 10:47:41.030 20968 20968 E cr_shouldIntReqMed: The following exception was raised by shouldInterceptRequest:
08-10 10:47:41.030 20968 20968 D AndroidRuntime: Shutting down VM
--------- beginning of crash
08-10 10:47:41.031 20968 20968 E AndroidRuntime: FATAL EXCEPTION: main
08-10 10:47:41.031 20968 20968 E AndroidRuntime: Process: com.anonymous.videodownloaderapp, PID: 20968
08-10 10:47:41.031 20968 20968 E AndroidRuntime: java.lang.RuntimeException: java.lang.Throwable: A WebView method was called on thread 'ThreadPoolForeg'. All WebView methods must be called on the same thread. (Expected Looper Looper (main, tid 2) {90c5e33} called on null, FYI main Looper is Looper (main, tid 2) {90c5e33})
08-10 10:47:41.031 20968 20968 E AndroidRuntime: 	at android.webkit.WebView.checkThread(WebView.java:2634)
08-10 10:47:41.031 20968 20968 E AndroidRuntime: 	at android.webkit.WebView.getUrl(WebView.java:1271)
08-10 10:47:41.031 20968 20968 E AndroidRuntime: 	at com.anonymous.videodownloaderapp.features.browser.components.BrowserWebViewKt$BrowserWebView$5$1$1$5.shouldInterceptRequest(BrowserWebView.kt:174)
08-10 10:47:41.031 20968 20968 E AndroidRuntime: 	at WV.x5.a(chromium-SystemWebView.apk-default-720406301:95)
08-10 10:47:41.031 20968 20968 E AndroidRuntime: 	at org.chromium.android_webview.ShouldInterceptRequestMediator.shouldInterceptRequestFromNative(chromium-SystemWebView.apk-default-720406301:6)
08-10 10:47:41.031 20968 20968 E AndroidRuntime: Caused by: java.lang.Throwable: A WebView method was called on thread 'ThreadPoolForeg'. All WebView methods must be called on the same thread. (Expected Looper Looper (main, tid 2) {90c5e33} called on null, FYI main Looper is Looper (main, tid 2) {90c5e33})
08-10 10:47:41.031 20968 20968 E AndroidRuntime: 	at android.webkit.WebView.checkThread(WebView.java:2629)
08-10 10:47:41.031 20968 20968 E AndroidRuntime: 	... 4 more
08-10 10:47:41.127 21060 21060 W libc    : Access denied finding property "ro.debuggable"
08-10 10:47:41.127 21060 21060 W libc    : Access denied finding property "ro.debuggable"
08-10 10:47:41.127 21060 21060 W libc    : Access denied finding property "ro.debuggable"
08-10 10:47:41.121 21060 21060 W app_process64: type=1400 audit(0.0:13558): avc:  denied  { read } for  name="u:object_r:userdebug_or_eng_prop:s0" dev="tmpfs" ino=13867 scontext=u:r:untrusted_app:s0:c251,c257,c512,c768 tcontext=u:object_r:userdebug_or_eng_prop:s0 tclass=file permissive=0 app=com.anonymous.videodownloaderapp
08-10 10:47:41.121 21060 21060 W app_process64: type=1400 audit(0.0:13559): avc:  denied  { read } for  name="u:object_r:userdebug_or_eng_prop:s0" dev="tmpfs" ino=13867 scontext=u:r:untrusted_app:s0:c251,c257,c512,c768 tcontext=u:object_r:userdebug_or_eng_prop:s0 tclass=file permissive=0 app=com.anonymous.videodownloaderapp
08-10 10:47:41.121 21060 21060 W app_process64: type=1400 audit(0.0:13560): avc:  denied  { read } for  name="u:object_r:userdebug_or_eng_prop:s0" dev="tmpfs" ino=13867 scontext=u:r:untrusted_app:s0:c251,c257,c512,c768 tcontext=u:object_r:userdebug_or_eng_prop:s0 tclass=file permissive=0 app=com.anonymous.videodownloaderapp
08-10 10:47:41.128 21060 21060 D AndroidRuntime: >>>>>> START com.android.internal.os.RuntimeInit uid 10507 <<<<<<
08-10 10:47:41.128 21060 21060 W libc    : Access denied finding property "ro.debuggable"
08-10 10:47:41.124 21060 21060 W app_process64: type=1400 audit(0.0:13561): avc:  denied  { read } for  name="u:object_r:userdebug_or_eng_prop:s0" dev="tmpfs" ino=13867 scontext=u:r:untrusted_app:s0:c251,c257,c512,c768 tcontext=u:object_r:userdebug_or_eng_prop:s0 tclass=file permissive=0 app=com.anonymous.videodownloaderapp
08-10 10:47:41.131 21060 21060 I app_process64: MMAP_TRYLOCK is not supported in uffd addr:0x72e6c19000 page-size:4096
08-10 10:47:41.132 21060 21060 I AndroidRuntime: Using default boot image
08-10 10:47:41.132 21060 21060 I AndroidRuntime: Leaving lock profiling enabled
08-10 10:47:41.132 21060 21060 W libc    : Access denied finding property "odsign.verification.success"
08-10 10:47:41.128 21060 21060 W app_process64: type=1400 audit(0.0:13562): avc:  denied  { read } for  name="u:object_r:odsign_prop:s0" dev="tmpfs" ino=13770 scontext=u:r:untrusted_app:s0:c251,c257,c512,c768 tcontext=u:object_r:odsign_prop:s0 tclass=file permissive=0 app=com.anonymous.videodownloaderapp
08-10 10:47:41.133 21060 21060 W libc    : Access denied finding property "ro.debuggable"
08-10 10:47:41.132 21060 21060 W audit   : audit_lost=12853 audit_rate_limit=5 audit_backlog_limit=64
08-10 10:47:41.132 21060 21060 E audit   : rate limit exceeded
08-10 10:47:41.135 21060 21060 W app_process64: ART APEX data files are untrusted.
08-10 10:47:41.135 21060 21060 I app_process64: Core platform API reporting enabled, enforcing=false
08-10 10:47:41.135 21060 21060 I app_process64: Using CollectorTypeCMC GC.
08-10 10:47:41.211 21060 21060 D nativeloader: InitDefaultPublicLibraries for_preload=1: libandroid.so:libaaudio.so:libamidi.so:libbinder_ndk.so:libc.so:libcamera2ndk.so:libdl.so:libEGL.so:libGLESv1_CM.so:libGLESv2.so:libGLESv3.so:libicu.so:libicui18n.so:libicuuc.so:libjnigraphics.so:liblog.so:libmediandk.so:libm.so:libnativehelper.so:libnativewindow.so:libOpenMAXAL.so:libOpenSLES.so:libRS.so:libstdc++.so:libsync.so:libvulkan.so:libwebviewchromium_plat_support.so:libz.so
08-10 10:47:41.227 21060 21060 D nativeloader: Load libicu_jni.so using APEX ns com_android_art for caller /apex/com.android.art/javalib/core-oj.jar: ok
08-10 10:47:41.227 21060 21060 D app_process64: u_setTimeZoneFilesDirectory("/apex/com.android.tzdata/etc/tz/versioned/8/icu") succeeded. 
08-10 10:47:41.227 21060 21060 D app_process64: I18n APEX ICU file found: /apex/com.android.i18n/etc/icu/icudt75l.dat
08-10 10:47:41.228 21060 21060 D nativeloader: Load libjavacore.so using APEX ns com_android_art for caller /apex/com.android.art/javalib/core-oj.jar: ok
08-10 10:47:41.229 21060 21060 D nativeloader: Load libopenjdk.so using APEX ns com_android_art for caller /apex/com.android.art/javalib/core-oj.jar: ok
08-10 10:47:41.232 21060 21060 W app_process64: ClassLoaderContext shared library size mismatch. Expected=1, found=0 (PCL[]{PCL[/system_ext/framework/androidx.window.extensions.jar*2431750899]} | PCL[])
08-10 10:47:41.232  1236  2320 I slpi    : STK3A5X ALS_OC: lux:121.512001, last_ps:1[1720], CH0:83, CH2:2, CH4:9, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:41.233 21060 21060 W app_process64: ClassLoaderContext shared library size mismatch. Expected=1, found=0 (PCL[]{PCL[/system_ext/framework/androidx.window.extensions.jar*2431750899]} | PCL[])
08-10 10:47:41.234 21060 21060 W app_process64: ClassLoaderContext classpath size mismatch. expected=0, found=1 (PCL[] | PCL[/product/app/webview/webview.apk*2593257869])
08-10 10:47:41.246 21060 21060 W libc    : Access denied finding property "ro.product.name_for_attestation"
08-10 10:47:41.246 21060 21060 W libc    : Access denied finding property "ro.product.device_for_attestation"
08-10 10:47:41.246 21060 21060 W libc    : Access denied finding property "ro.product.manufacturer_for_attestation"
08-10 10:47:41.246 21060 21060 W libc    : Access denied finding property "ro.product.brand_for_attestation"
08-10 10:47:41.246 21060 21060 W libc    : Access denied finding property "ro.product.model_for_attestation"
08-10 10:47:41.247 21060 21060 W libc    : Access denied finding property "ro.debuggable"
08-10 10:47:41.256 21060 21060 D nativeloader: Load libframework-connectivity-tiramisu-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity-t.jar: ok
08-10 10:47:41.256 21060 21060 D AndroidRuntime: Calling main entry org.chromium.components.crash.browser.CrashpadMain
08-10 10:47:41.258 21060 21060 D nativeloader: InitApexLibraries:
08-10 10:47:41.258 21060 21060 D nativeloader:   com_android_art: libnativehelper.so
08-10 10:47:41.258 21060 21060 D nativeloader:   com_android_i18n: libicui18n.so:libicuuc.so:libicu.so
08-10 10:47:41.258 21060 21060 D nativeloader:   com_android_neuralnetworks: libneuralnetworks.so
08-10 10:47:41.258 21060 21060 D nativeloader: InitDefaultPublicLibraries for_preload=0: libandroid.so:libaaudio.so:libamidi.so:libbinder_ndk.so:libc.so:libcamera2ndk.so:libclang_rt.hwasan-aarch64-android.so:libdl.so:libEGL.so:libGLESv1_CM.so:libGLESv2.so:libGLESv3.so:libjnigraphics.so:liblog.so:libmediandk.so:libm.so:libnativewindow.so:libOpenMAXAL.so:libOpenSLES.so:libRS.so:libstdc++.so:libsync.so:libvulkan.so:libwebviewchromium_plat_support.so:libz.so
08-10 10:47:41.258 21060 21060 D nativeloader: Configuring clns-1 for other apk . target_sdk_version=0, uses_libraries=ALL, library_path=/product/app/webview/lib/arm64-v8a:/product/app/webview/lib/arm64:/product/app/webview/webview.apk!/lib/arm64-v8a:/system/lib64:/system_ext/lib64:/product/app/webview/lib/arm, permitted_path=/data:/mnt/expand
08-10 10:47:41.258 21060 21060 D nativeloader: InitExtendedPublicLibraries: libupdateprof.qti.so:libQOC.qti.so:libdiag_system.qti.so:libqape.qti.so:libqesdk_ndk_platform.qti.so:liblistenjni.qti.so
08-10 10:47:41.258 21060 21060 D nativeloader: Extending system_exposed_libraries: libupdateprof.qti.so:libQOC.qti.so:libdiag_system.qti.so:libqape.qti.so:libqesdk_ndk_platform.qti.so:liblistenjni.qti.so
08-10 10:47:41.259 21060 21060 D nativeloader: InitVendorPublicLibraries: libqti-perfd-client.so:libadsprpc.so:libcdsprpc.so:libsdsprpc.so:libfastcvopt.so:liblistensoundmodel2.so:libOpenCL.so:libqmi_cci.so:libqmi_common_so.so:libqmiservices.so:libqmimotext.so:libiqi_bridge.so
08-10 10:47:41.259 21060 21060 D nativeloader: InitProductPublicLibraries: 
08-10 10:47:41.262 21060 21060 D nativeloader: Load /product/app/webview/webview.apk!/lib/arm64-v8a/libwebviewchromium.so using isolated ns clns-1 (caller=/product/app/webview/webview.apk): ok
08-10 10:47:41.274  2478  2504 W WindowOnBackDispatcher: sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda13@1af50ea
08-10 10:47:41.274  1707  4795 D CoreBackPreview: Window{9d4a658 u0 Splash Screen com.anonymous.videodownloaderapp EXITING}: Setting back callback null
08-10 10:47:41.277  2615  2615 D BaseDepthController: setSurface:
08-10 10:47:41.277  2615  2615 D BaseDepthController: 	mWaitingOnSurfaceValidity: false
08-10 10:47:41.277  2615  2615 D BaseDepthController: 	mSurface: null
08-10 10:47:41.277  2615  2615 D BaseDepthController: mSurface is null and mCurrentBlur is: 0
08-10 10:47:41.277  2615  2615 I AutofillManager: onInvisibleForAutofill(): expiringResponse
08-10 10:47:41.278  2615  2615 D StateManager: goToState - fromState: Normal, toState: Normal, partial trace:
08-10 10:47:41.278  2615  2615 D StateManager: 	at com.android.launcher3.statemanager.StateManager.moveToRestState(StateManager.java:465)
08-10 10:47:41.278  2615  2615 D StateManager: 	at com.android.launcher3.statemanager.StateManager.moveToRestState(StateManager.java:456)
08-10 10:47:41.278  2615  2615 D StateManager: 	at com.android.launcher3.statemanager.StatefulActivity.onStop(StatefulActivity.java:136)
08-10 10:47:41.279  1116 20583 I netd    : tetherGetStats() -> {[TetherStatsParcel{iface: wlan0, rxBytes: 268265527, rxPackets: 111466, txBytes: 50446405, txPackets: 64046, ifIndex: 0}]} <1.60ms>
08-10 10:47:41.279  2615  2615 D StatsLog: LAUNCHER_ONSTOP
08-10 10:47:41.279  1707  1766 I AppWidgetServiceImpl: setAppWidgetHidden() 0
08-10 10:47:41.279 17070 17070 I bmex    : onStop
08-10 10:47:41.286  1235  1235 I android.hardware.power-service-qti: Power setBoost: 0, duration: 0
08-10 10:47:41.290  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:41.292  3994  4003 D ForegroundUtils: Foreground UID status:
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 1002 is in foreground: true
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10140 is in foreground: true
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10176 is in foreground: true
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10183 is in foreground: false
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10190 is in foreground: true
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10194 is in foreground: false
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10221 is in foreground: false
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10225 is in foreground: false
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10233 is in foreground: false
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10238 is in foreground: false
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10239 is in foreground: false
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10245 is in foreground: false
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10255 is in foreground: false
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10469 is in foreground: false
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 10507 is in foreground: true
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 1010137 is in foreground: true
08-10 10:47:41.292  3994  4003 D ForegroundUtils: UID: 1010190 is in foreground: true
08-10 10:47:41.305 21060 21060 W chromium: [0810/104741.305066:WARNING:third_party/crashpad/crashpad/snapshot/linux/exception_snapshot_linux.cc:263] fpsimd not found
08-10 10:47:41.315 21060 21060 F crashpad: -----BEGIN CRASHPAD MINIDUMP-----
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912642, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912657, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912665, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912673, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912681, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912689, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912705, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912720, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912728, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912736, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912744, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912752, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912760, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912768, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912776, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912784, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912792, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912800, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912808, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912816, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.325  2478  2504 W FrameTracker: Missing SF jank callback for vsyncId: 912824, CUJ=J<SPLASHSCREEN_EXIT_ANIM>
08-10 10:47:41.334  1236  2320 I slpi    : STK3A5X ALS_OC: lux:101.015999, last_ps:1[1721], CH0:69, CH2:1, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:41.334 21060 21060 F crashpad: -----BEGIN CRASHPAD MINIDUMP-----
08-10 10:47:41.334 21060 21060 F crashpad: )iyJL'!CAs.x|GMGz&M~7Pycvc/71[z#P8B8CZLec]-Na]{E;ZCY=8<Px[3>P8*DT/)>OT5~M>5Q~b/+!?,,KoJ6<URv1kf`.:y;Xu-x@+6dEURvdfJH(6`g_2:DEt"GJv4s:"]5k6Mbnl7Fa,TF|n!!2"/H0xm"[n9"iEDbo<n,[n("]'MWV0Aq:&i]WLAwnu]J9xFU=w'!"bWLPbVlI#9MoBf.6b#D&&W\c]:K{S@p3r/x<-QNW7WHg|\r1c-8<!q3<x(#x>PkaA#,cWkc{m9]WDq!\x@xk}282Xa1[&e!lN=c_L[}Om(I;^?^A^Qy?0xL{V]w#j#ju;'Z0"z6DMygWm(tR>wt_(-_wtU3Y--#z6{bL1ogwgsg!h$R=O&PX0MGY-u)ey!pR5OX4:["9+uyfP>[|+OW?^?^<^@^zM6k5k8kp8'cYm{.K,m-]{;O1zL7mrJ"8l8lFKN7F\3Zx<c>i!C!vW"7-R"as*SBP#C(S0]{XL-7&X\/6:4Yc"kL
08-10 10:47:41.334 21060 21060 F crashpad: E7/,!hOm|<`q<*[n'!k>tU;e.#S*Ji*fnwBIx*9m$R^?Pw?AZ._opIE6mzYUQ9U9,FkV!1K.+a:w3[vY07%^eZaW}M4Y34-Y#/ha|_5z'XEJ17<<%clV'`K)]5\4-#HMzb*,f2b.a1=G5aL$|N79t(TAF%/rQa_B6xEus`N8WLu@So!Z!!Gu^Lx-QNqw@6Np3!oQ:JZ2i\AI0<w,:2O\htQ9h.7a$+.Gl7~3Hn*BKTS};\^!D>-ff`W!f'|lv^Z.Q@2~t]#Rz'KK9q;mrIi\w8mX-rb)r-8)li)*Y]9c_g~$9}eeDM_w>L=-$wt73SX'kf7B0<t/5+d.v<8Q?.zFST\bt9#]1+"$YMUs<o9GSlAq_k1!pt+fn@4!;?quo5;-t7\Bdwr#3r+]]a]!EWee^RFKr[V;$[b`E#9-jc]6])kVc.8}jc}FPNwn8g_9K7!"EAquv42?7M|ejaC])G++kV&?ULxp<c];(ZcFcw7w#=8?}pQ";`4nfLOnAev~$Df)
08-10 10:47:41.334 21060 21060 F crashpad: 'tTPpF~0[.G<i6KQP6.H~nvq(0x-_!WN$f*2S41I@v!!(n.o0j2x&(!La`u%<l(d_)L*u(66^acrW7ZAqoF8Ej``gnB`|>!b&%aExS~@9**+x10@Lx9w#0*FB"/9qu\\E-&hPQ|pF%opzB6Ol#M_ke<oXg7:6tw0:t'x*]#tl#]7xS&tkn7klj`~(5Zj@_X}!!n'Xw2N?.>#s/,-b0.f|1%!^"$p(=B.##2WB7CTp6Kk.fFl)!-1IKHq~2]u.*"Th*Y=uB.KF-ieK.|?5w.6Ezo\}di^3!Gk0?;Of"O4phT;+7wsB2?j'$MwHd=&6%i%|nxW7]]A-k;JhX-oH=\L#)Wwedz^yP=1_`gnnU'0j^IxU&|l6~0=4Yrj*Sq:ZzpFN81iQ?e611,n0*?j*&%Cp""L46n6B{!xn5WMP@A}o@Hfl6+I]swUYL_36v8_Q9Qjp@KsO0]s6W5x^f56xvm9;_w06JnI+flIG!,7qu{[,0Bj_3+AlK]s&WdLsm`l<Z"r
08-10 10:47:41.334 21060 21060 F crashpad: poVRh;BqEn3bU(CDuu]gn6fU^5.r`<Eu2IVs@S]s0<]LNN!xcI+n4TYj4P>Cujeqr:7<&Drw(KE-x=ST2aQT>kX=u+R?(xA%7J6]z8Iv6.zwL!9y$f~`4{Xww0:t4mOhAb;!`U"wztCZ"cYjth5,ZxAwN0q)o[IWW#;t'P@2jQ=<c_TL$!Q2>J#/`9-HST2a|n.o>HD!V<quD:qK;tR"`C_w#j'TjJL!2C[sjn~17S"~j78"[\*0ZvR#gZ$f`M}((brN@2p\Q+]snReLS'2Lmb.`GphA,;GWa3]sckZLu(kaJabzmFdAJz$!o0]s"m6x"NUr6u,94bD]pS$!-t+TZrp!)i)otYh7gvcBN|H}o+668OmgsC&G^AjR$#Af)Ae"66M9:m0Blg"lC-rfzA~4>"G**0,wH#UaUs.`'?5<"f;JTd,f:vr"mMDv`g.Tzf~3+Akg-o0JIxq2BLge]sP2*9rM#??q[%C.LvJLK)K4.#o@$fs\9U-0f\X#/^Us:49?
08-10 10:47:41.334 21060 21060 F crashpad: S{f3al%k]s<K8xH-?J-`2Ri7,{LXW}z+zw^$w(n^z>B3iwWjdpl_%Y,xk)l8si&|Un;1Afua#xuX,xg)T-I#i!BK.c"o&B2k(r2/KTHF=wEQdJY?dgZLnF\L&ouVbkvuevlPSK:gv0y$yIARrl\$|TaXC41k^#kneKzX@1gj<T%gWb~E]G!E{MnYmn&L^w`ZCnqO0ircTpXr0+,I[gIb$>_B2(m(4Y?4R7);sTO/dX5*6R5/4P8V%fKJ%0Ywt[lnBm:K5fO]tv'uzDV_~viFHr0iXq{9y*VLsH6Q_tZsd^v;[31Rosh2nJ,w=W'Mb2gr`YMwF'3<Ub4kKuPYMw&'IW$M&tgrpaMwP&CD)>RL]s`mp7^`Yuur@5QOaIG,bFIo|T0rlN{uTcy`qFL8<KB6WL{o>`eTe\R)*)w(o'$w~Dn2>He@~xmI\a?cWN9OMo(A*2j^ICp@{s(^K?iVVA^,XI%Huu@;z,.8$nnN{N>a0:T^767A@"-l<VjflX/S6b8q
08-10 10:47:41.334 21060 21060 F crashpad: n6Dvm$dYW0tkG7a|2m,)Mhmt;e(xp'^w80\0Ip7Q^5LO[>D*c3~`5by,_aHm\Msvh7G.3Q5K'nmibk4"OVrwSHVNIQ>}Co`UN7uM^9:gin<fm_\8GvT<.azm,_.ot8vj0YMju_7iAHd1E,:8scM0h(MvP.ZI_O|?%bFK5E&-PY(Nh5!G(oVuUP`T[>JC-9]Z}4M,j=d0/->~'qC/GEhBsP"p/IP~B9m;LkBtX%4X#+*CbGaW.,_}5Z\~W76d7:PZT_X+>]"61E~8zX{te/D,+c&Wem6YWnF$&$X`XkHEv,'bDA:ry1WNOGrhKKR[l8G5|1[CW(3D|mU>B@iT=Nk9<@+(k1w7htfwoV:gk>rJ>qjm*+PgCKM9Zf$Wxi)n6W%IuG-T<YE04[1P<}JKA.Y{!eYVS*u4+5di+4mqyV9^MK)?sdQ=xT_d1c}c=,+Wr(BGhB8+@snOc[[m-28HZuH8jI7h-*fT"l>Zkn^(sSN=-3+x^aX+xFx=o`|h9mSZpbTZ
08-10 10:47:41.334 21060 21060 F crashpad: cVEY3T$fW0fuOL;K[d>HRgBAhU0?+7j&!tg0ku6-o@f7DjZ,Z8\WrQ>|t8AJ/R|v{({of,8DevEYdn8k5?4[y*/SqQ>E[~ftO\$DY1+w</u`7"I,4^8WjM$fD6uw0ovjNTN1KKM\hOmV\`Yf*Lo:NJWpdHS;66PF|`IM<"h7}\%|n9@glf$nfd{3Ra;r*84)`3vf=\[>Fv7K:WU9kM4e.^9hhfa\rFn;3hSF\Jn:"emFkXa^3l>nfTK7y"k@SI8eI(<i"'{sbLPtk][Ajvs5ez@4W0:<4A#t&uZw/pt^%,Wn~i_mnF])samRMpQZAR9>Up{iYiNjwv7-jV&5wH(G)E^rZ+{s%n.DXw[52g|G-i0{8InYTe+u(etSKAhvzu|]ckV_vozlHj%?&wz5\2\LRJDAOrAdujT3]j]08sP`oX%sD-lhd;8nn+b_;r^xpV{/I/">3q%,CAdn3<O}y5PqP5%TegsQIJr2+[IK+xfs<v:^!C1b]lhU/WX5OY=Ku9N|
08-10 10:47:41.334 21060 21060 F crashpad: v<6TJ9?w>B8Do|mG\9I9!`wR$t,f`]Du,=W]IKgv/6yH~wuu+bR6mIX+P6o\RqY-x(ic,TJ9?wT2KKLE)`}]5-d*+lxTqQ5uL\IK)/hugeq*7qJ^~#1*!i;yDWxS39Sg*,9?Y>>sKQ2Q8v?4@a&C,7.>=c+Ekj>7_GV/&vJ)W-$^Jq#v1ghUv;MAk;pbYwQMFF&8Svskh[R[Jw$hw/otp=ALuuKTz3Q@\$"}vu{QIQ*+RgL`!>|Xmu_+BB5]X_+Y3@Iv(S,_s3mO|WlPHH@|]U76XV0j}czF\w]~j7i.KE}Ss<IP~u(x8s~Gj)`C)(Rj-R31*@X9teTw6n"@|W>ktvI@aR9WR?W]c9)r0iyq90fDG8clemxP^?{Lbu/4o-=KwjJF$.(JkXR.z#p931lukRm(06{i(NTSS^KXPcr'26upca\o.Fw<2UP-#/v?nT2QLd?1YSzvffntPi{E5-.KW&26e^kYX+j_\`)a4*jf26cky,aRIwY91@>dX+dnqSH-
08-10 10:47:41.334 21060 21060 F crashpad: `TFKu9NPW+R@,NTS"[rucFP/CKKPH0fu1=Mhqs2v<\gE\6Zn3(lm<rPQ"gHe'G@5zoHPNrqax5@J2+pT`YNiT-"S&476E;d7jv`BL9`lJ~pV[g=wI.<gEL+?"E_l^~*wRqdEMRNr;Z@R7NHWVbYrRO1rk(pW)a}V=Zw)MDE5FHB<iZ1i8.A9s8Bb+w:cdTpklKX_$0qk4ojMK)26eHz>6<rQ?WOjyq0*/)^<K@b-f3wkJtqw*Ztdkc3=PYF-ZF&ulle5gq*FAd39auJVUuNvMx7lfUAKip)R{FlvT=`ltA}|1pSwN*/f`]y1M@QGD64CJ'QbYts'v(xm8M,tlwXePUBKS0mKbhgD8Ry84[z<teTwtm`/~EdT=Kuakp|ex)Hj`2Eu|?=dZ+,6RP51$Y*0c[sQGrT>36KA:45=Fx4KSe`cXRH-9^5-^*ju=+]0GAc\g0gZDc,a8^F`-tG53LU:"eWsr!rj2u?JkXynXNt_Nr%kNm=0MCA>I5KP0-P)2tIf
08-10 10:47:41.334 21060 21060 F crashpad: jY8'<f16}0v,aR'61^5-vVOsK-'F~K`%\arMMKOvul^s00)NkXZu/gZ.}sG6o$2o`N@[o,ivr:u<07xo[$5<Vb8=CsV|Zf:yfQ@i>V.zms>wKnB]J9lpLeH;R^;\ZslnK81e0E~Ryk+RV\1Ihnt9'esQYG_n$Q))I'h5lK*1tSsQ"oBP.6a:&@V+6grOmsfkKn.s"e$7JrKdI[RPU+6g@PEKeY":-+kupzO\FKMe<?aRFQEK/~2Br\Mgr`<tm>ru1=W]qm>.&]jsn?AH(C|tQLyhn+To.<@9UL(qzb84-(PK+lr9gnjYXw;\5sDejRBH;,>pz.IG(HTwZ?mu,f`]v((0He3936gKs[=dH_@h;o+3vu5EKjxikt-`4A{/Z+ted?W+P_,TJ9>e6om^J`n1|5V(2635oSjFkX#+FPZ5jH8dbirQao{:36QT&cD;Swfemu,f`]XI+0He3936gKEleUEl{D|a:];=/){a,=W]IKIcn4|W4pd-0u=Xl\%4/Juv
08-10 10:47:41.334 21060 21060 F crashpad: rQ6Lk)4^PTJe>MH-xY\7Mhi=?Gl;iv8B(9R[mu.f`]%'J9(sb_mql:O[IK)/HeqQBS]/X+6g&Yt@-.{TX+~2Rr16os_2wtlNyK=4!fjRlM@,z.F%$b+'Hu)S%e=svc6T4C*-Y+D6Np!eWs~wOuwPi?[+^-D_(j_)%H(Fn+hufQ-`Fobv=v3%|&SH212+26;+rQ{h:CITBKAnLeBU8R$;QJIGILrv8omu,f`]NT50He3936gKU<f\<$kw\;huM3{P_ATpIWgw-IP0fu1=u<+6-J;o#,Zze]~AYmDVlM5Kt0$==uz=pc@I:>?Rvh?51pLCFN<1vpPN5Lbnnav>|rH4k]X+*FaDb#{hNeeB5]nd^3mvScd:fx'}D-`R/.`42r~@yIYg8^x]@Jz>P/Nwdd&eBG2+"n=^TK[52I|loJFPmki4t[*ssp)"5=T!-iKxvEeL~FYLuDaLTDaLUFOu&Lf_@!lbeD~>JQ>PLrye&6YsMg$KA}oC6^9;VZkPhU~XcI*s
08-10 10:47:41.334 21060 21060 F crashpad: ,+X~Vw=yE[i#}()x+bQDrv,S_R\gf>Rwfv06#-R=VXNnEX710E0vL3NG:=#I2b*sr-01Iv|sLj7+<+`)~3l^IwzHyK5l^jXadnh5Jp7E?;yV>eF7(qIlB>Veh8&@_=8q=XGHw4eE5WD,5~^De(|KVO1k:Dlt1[|5J>#ANu`0uojL9QiP1$|d[Z3CQc|@A9Ja1ao*~avD*$ACVZxv%-xz~R]ZBK;p|G'qw*(1,OT8PS!J(e--@U;\Hu``(mMLPNR7OYX+lgCK!Wo*0):s0`8NLTYdcF<(,L/CkXbu#6)^As>GWNy(>nHCX716GKJ"Vky$rKhY,m.-i*@sRq6@IEX+&aE*#RZB@GEDl:phlM|<]x-wrP$b@_\h&1RelC|I^Hn=~p|Qn+:2/=6V"2on(gk^d]Ev~-^6{oF2*w\6rBdaI2jB1Did.n(W^f\CI9Up1P]C0WVoH-Lg"N^6/3>-|?yv@3mu*58-^r{M*w00)N^6gK{76U]6eZ5Y[wM3{Ps\[tSV
08-10 10:47:41.334 21060 21060 F crashpad: Ds(V9>y2XbXR]0Z+lgqQ|V`/5-jIlBvqd\S[RuO,er~7]sN_*wIn:,sF{rzEs-TGlccuMs<p1epePMEAlOh\JenP5->WZ+4n1e`]n0Y+0HfovjS25brw)Vr2&v59y8kFnGqSN3/UZrjvX+.^05+uaRZfH(dmLebv9[8gKr&D-6'M5K}!66y.W5Rw!eR86Lgjg:&n'M:slnnZ'ku\U,ja)~~`?-"2sQyK,uVt)0tQ9W4ezZsS96i|BhqjavrjI9pk_d/e](S$`nb`\!QqW#+@f!%\S!20Dbcik6`iIb9j*/z.%t"n@9Nq}Q*)m!g38-AV&Va`7k`UwpyqS3.E'ePKw;vVDsIA%jfLL`%Ix5{uPG=Bfb6D>Li+\Js*.o%g\OZ_!v~n,We5<("t"n?DrsZJ"fyT[w?l$K43$C-b1e7gIaJZDg^YY!:LBkcAkEbAO0y-&&!9[#+X^!6/ZLd/*x$tTrPt(*0A*2f(H5*C"e&]\CCsu(YWRuo?/J4/Y_6:Pm'T
08-10 10:47:41.334 21060 21060 F crashpad: Da2n/I.EzBtKu)J9\+}m\-M"raD/luqu=X|wkN%\+t<@N]P[LepK!fju^s2LJ@FeFl|cF\0;5uzK/ZvM3Z@!+aI\X;*>OFh+(LR'qKvP]d`]0LIPEh2p"("ZC?93<W[w]lHj1DWqf-Ob|vuiJf~A'pbdcdJBh66cZ:/AV+v_iNx{%mqNBow4XtBF(_WH]rTC`U5TN+YTsQt3R[-xbu*x'F(t8r4J{WfU7k(fKA(FH?vdUImS/]_tWu82!t1y1KV2[we7Wrd=gHoTmpa*hgG5KC<L)}2vOfnIUe^3UFXJ)*Xww7iV|eJ*~`eAVlp?NJPZ%*O\:nl^%qRd-m:V?IMtR<S;(nzrHWnbWU_5|?mw312HjG$w@EZ&sdx\u>8F'6@sRq6I!Cjq4~Y+@ytKfUrQQg,T*N1>_1T1[kfLOTXCu<ZTF\QN#8gH[IjjvtB$"PW+ja&Blu#n<3Mhhn^.ODVU=T7F}80.^S0iGFzTEW&FdYFJKu"bqQAs[UBkI;t=TZ'x
08-10 10:47:41.334 21060 21060 F crashpad: ov[o)`ze>T3w1f=sd:g/L]"ay?pDtQ,;NODq#jN]TTS_/5[1ws~XN#-Gq7d;$Ex2fB;CD^f=0<2rCkdD.I;j;I{fwQif$ArQks-O>8ru-c<k8LpiAnLeRPZ+L9X5>Xbu-K0i:T26iv@=h7Q,s4r/PqRvJGxSPiDa5f5-jIEFY+tv]d|KxG+V4ALdpx$AExN/ruy"[M=`$\Qwf[~jPHxpgr?Op;<^]7~=OdR4=n3Yeuyr$H3(FOU`~mT+sQh7X+@in3qYgwGmG6rKiivt5Gd/b"GqNHkb6ag3W^?v#mRKQc7_76[54?!2L,,Cdv"8=Le)z.^p">h7Q,s4^ECqRv4lB4Wecu;j_3Z+tv]d`].(03"OxNZ)2J~m)B}'x[EMuV{OLq!e\)Tg[O8V-~?d=,_mJH[)Rz%Lbso@X\UiwDa4(y.x~uam}Qx-bwPm'vTE)wb)_I8`4oZwOl\t.w,_Eb=snOT6AyzkFv;/*wHLPq,:VOTg~lIVY`6fso5'-T*L)]xW
08-10 10:47:41.334 21060 21060 F crashpad: d4wJ7ZbY6vsj$H6JYF@PX+8]-@GXd4C%*dx7FBJH?COoR8|29t`D<g/BJH-)6AsO`?d+!6^\'/4W{F(}44^cp@}t;pWb=@|5h1Icu*W=FGbjpWsOxS%ZWr]s<SD>rr`afIsh!&iQO[%??hj{"|2.7fgibP>DVb-`!6#8NL9<8-B:elHnbBHf|7[(JF{X}D;rxT*gfthlcoXYK@m?"=56%f!ek/*Z=4aR;`YQh=oibabd,c&x<E\9"NzYOa=w;u-`F0=t@v9e(+p9Y_;sLn_,ueG?-M"wxLl@cTatGQ\?c.Hkvt1D7oHf!eWEmQcUpuM*taV4d@)w#qlq,s4H67UHEhs[e)}jbtW]Ppt\4LO{!$!ZVsdHZA"kQ0@s+/fuiIY+hqSsjO|'A2X+Vx0gcuamboIU26MTbug@=W3F&soTmp9*<1fTO\%s-PEKA7Z,4clCZu.Q4CD/y^";TwVCjB:lvPYjc.0Z{:gXtP62/E(e,TL>JpCZq(v1oc2c#:r+jSIl
08-10 10:47:41.334 21060 21060 F crashpad: 5*rH_B0wz^fI%ep5p[[GzQep6F]aTTDj[pLacFn20)'afPu>zj+Owa]qg.pJlcsQ~JoQ8'fDr,16{I@6V+T_fBhl,Q!t]G:g>\}BfntyM=8Q/L~3``hR$;HR2fB~Z>nl|^8Nw6(YP;xar9vTGW/WZ30LT-*Z@Lrr+UQV@/}whV9>GDk=jU_eJU+!vJ[w5Nv)$5-W"H=L}pDJbHCyDc36]P*U\o^h29rS.0va]BsQVHgNYDWn^RTSur\vZn)XBWts_mHoNrm;"0sQqXR`jdIf(%Z0GKUQsVZ+tvuuMsz=#2,yv]:PxSQwg)Y/v4TeR>#qN^|6HE*kU]LI#Z2rrk!eDANj|g(8Qb=sRA8neJ\6!2X+EmfC<7_PWn*++XHj3wA:J92AZnY6/o`6M6<3kGTsK+zw,P'wbiM82fNr[vh]'vrH@v1E\9"NzY_I>w+mfF<eXU"g2JL8ed`6Mv^sp0x6AWCoZ,^tsvp,/)@f%1Nh)T7Br/<pfj-/A0zwhINry)Un
08-10 10:47:41.334 21060 21060 F crashpad: .-,\]U[j7=J/Ob]0Kw):TRd[X/f.`Y<uYSzv@CHE1FztEKv>B^Y0|a+iH3`2#Jj{t8;3s_<1b>*uLQgvr2hK|4*XYcglpyowkqQwm!0lL\}K#wWc\tKsf>=46}}vgeD_}-<1|smu{eqjbG<oeMZ_Qv35%;?phY_5CABs2Q:p9>C@.c(/RXm,ke/QI^tSNOj,'f9u:2Cs0P";'62:7v\Z1JLruuSIhhu\(j@=DZn5VpR2:bKKjf~q+w)59MN;`Aas[_@-(h:W2dNDvC]k3qBb=jbAUlA!-w:aH*Cb!!1yWLN7N9B1OLB+0<:a@+gqPf\""!0g;@S/Z+PsrQC;rMR*u>R<sJOq2[0orQkyabIt\$zYC;=ww36IzX9>fOY?R[yKtUX]sO<InHG\Z&\_DpQZUL8vcMj4=lhqK^epBlLYlwel#Zj^@ATdY*[`K3CK!ca\OZ4*45dSCKe9Mhx9dvzbF`hj^6CSY?Y+V9vHXNdT|35@muO4Z566[u0V"2PMn+@7
08-10 10:47:41.334 21060 21060 F crashpad: +xhSWEqa>RfvHUn+>rRt1qAI[YFKys:ljmNukXxFTvJ\c[FK{p~hVsZ9\rViT/KK)?Er6n}QQg.Q:VoGr_>b%^`6}X"0|wyHbKHE\9"NzYe</LJ9``Yg%b8r~pa}1kn+tv[3!2B8|B3Fd<D-TiyQOGh9nu%22REWIB|lhB`-MffriFO9|bF3iza3%Gi8g=CmKgjnn5Nx8-!CA="\+G"xV]s_>T]hwD}6Bu#fYn?Hq0#pD$d9k(m_B`x@5c(w6-0q@2/:%:_C!m}\Au{/fT2aTw0K7B-;@.J[0#hlZ6<Vz~w0^r,>)n2f\{|;9hG/'Sy=eSLesD4BCM?yyTa9GsbNfDGw,&`CE;5L?NDuE,XwIF<o)`G=[E>F<2CKSlmJN^doBKAGhef0nbLqB3>`5qzv`C<g@&|0(u!3Dt`^{J!jN^>]MP~JWU14$CX*3o&xg@KHRq@$Vkxsr`Drtk>SagEw[E_8!u3l{K-hL1Vav8JH-cxHfjDhIVPc=@|5Hq~*=BK}
08-10 10:47:41.334 21060 21060 F crashpad: z4<Jf?n12b~wzht?(iXs"whs:`^APb>Co)DKGNz;Oa,+~"luYi>KKgLAB`qp:(`K2_If~9=LawxJ/uv]f4\(-S\r7V4i#t,#vd_o|'OV!Tnt,ATjPR^6?O1kn+tvgY`ipc(j|XJfJh=<~R:o>y:K4eC\#I=ZqvLwTo>Nu>V!Nn@E&^7/fvVwiq60Q_Pk36+ISJatS.%TZsL^TUH0>hvwVL$v\RlilIiO$;6JWEkXS]Y8R4wew(~uXkgX-0Z6rm~>dv0ank)@o4w:vHZ7?aJa2c09-ESvgh}Q/3CR`m0wtv]d26\I_w!@(g1PzvJtJ^RVSA.bH:i1gQ%YQu"bqQAoS?hvS/;g)s.Z(oEqCrGs@u?vyhWnsk=q~*c/u>Lt-T"X6]6nKA2O;nx[;wVs0btX8=V[~H@/?2saefl3@6-bx,u>LyTQ;6\CR[KV"h8u81i*`KR?<PZ_[K|$U)Ru-s],3o`jU3-hSqaGu&6]v\x%ZqzSRA]`$UO;=bsNZ7`i9W!C
08-10 10:47:41.334 21060 21060 F crashpad: rLi'/MoF^%Sm9FiuXFRDnOQN)x:_G\VE&pt2H7"3~5Khsk8K`]ObI}l$fW3,0zkM$\VsRr>L1K&t13m;HYo1C+ciRtH<JH>a]cz;KN:DzsG]#tQC;s\6OY2Wd/~)%D@HARdi_nY?z'M`pv!"Yg.>k9KydbU2g5NROjWgB(+dGr!fBD$eyKVHg>\u>AnYoIF?EfBr\g{K/4$CQU@:DEHaGdn+.dG,vt.J^6We<W.:86*_gapQ@lDV/>%^EwVdIV@uWn6i0I)vW@baNynoLQGV^e}Y.E01[g:J-HK_hd^g$_/+Ho~nx`w)Hee@;%vr:u!it,:6:1,fBejROjcu'h9HLtc4!>]><v[oov5\ulZwm{BkSHo>j3m<5g<#;rr`hFgqft"jH~C\31FG|6-PU/XT{~GL$!SrQj;`^:KPEYNr@o@ic,oIVc/AS_yrtdWlLv"!zmvCL>$O@4EvQgNkm<xiYg2aXt)@o4w:vH0]ROrRv,_Ey_|mRv^qkvh%0Ot7IVYw
08-10 10:47:41.334 21060 21060 F crashpad: _|%Grs`]]6Ts\pE[U>/eX}E./Mb6l{ehoAh>IB>X*.,7\K5\Tiyo6`$[_:hRJ{hhW;;Lh:?#|,YA@P]C>XbPj6X?mKr}t]mv~OGd,)Mh#:\gI9qMD]/6qTmpaBV1ftG[ueB~09u<~uMK-i82Z+tv!fjR@A56[SzvV`_Q21buqU0-&$H?LO"eTwq*&n>NzYc+76Dr}lIKWV~3Z+\5"Ud[$v#o`OZ>J9+mte.TsQ&4J9]SH5kXSr:U`r[+hu]TjO|'QVu^Dotn^1jY5-*m36=*Nf$_>CXsvcD[5-\r"oV:"e*V!C92gK]OYb6(06}bO@AwOXW6mg!hhFPv8j{i16M2U4;CPQOp_Fq1*.2Zr1V+83a1#o|]Y*:1Nv7NTSYu.Qr2mh|N:rf>BG\NfE77*\\;)m$b+{brL\Ws~w6nBPMJPZgFlAMWu"z2u2Z02clC$MHgnaznJH8dW+Vx$A{ONF56UxdTW6_5'hvTH-ZFY+tvuusfm`5n*Zjt:Q5-YS.0Z+J&
08-10 10:47:41.334 21060 21060 F crashpad: >L_B^@>d0mFs/j!e+unI9cQa/o41>l(?$vQ92zi(+}Op_F2]dsj3t4TtGfPR$;!eK~>0|_R-OjRq@c!~|O%u0j0\n^'w0OOZjcDM<0}=z$TbbTzLj0Qq.1Tjau?-U2ZYF_Hr&`nw#uGd%j\!@<0_ykQTNSVjdArUDkocXu`11bG6MlVA)<y(/_.H7?Ev'PBL-NF2/cfIAIJwKJd[cEKKS+RtaDWnd6xeK+[Q4]`<Wn;&pZjslnX_{u}QlE56#Y(cM=8A/L.K~2X^!tsmZ,Jn.Kkt0ovjHIg^H;zwj+4b8-RO623ld6-<wBH3q<nO=[']$8bu>#Bk/7pP,=>G(eK3Gw2(POe/>wQcHZX.VL86g_Nx+[I9TKqU(K4+@J"ZUTHuhGPx*=,?bfSRd[6@@6VT_6mI[+J`O9vr2ajgvWEdfwwni.-l,I`!AnN6/{JK,o<HEdCu-r8INH!ij,l+m7-oyqx%(GXv:mjFTDYt/)\rNQS@wfg)hK,o)l(\jS(mzrp`
08-10 10:47:41.334 21060 21060 F crashpad: .BXcy^!Khn:lZT~$kgdi[j>\%b.32*J9*.)kOCuqJTsQ^mBgCF9GFJew$%Cb,&Aul+WLA.dg,!Mw-uZ&WLunW}"!?Niwr!)e}_N,!!fer+^L`4kd&xT!]_*l2:!!,8;W@!Y=-u^(WL<,NL?!Oh,rnrrnrRiwlT=Uiw.U}utr^A$:\e^A&:\e6m#:\e6m%:\e4|[hyGLouc};yF~6Sj(7ADO&e3]v9$Ebrn;og#A-ZarxgTxGdsD"[:OAxL)e%`lsnqlL}a'y-G#Cgv%&m6{G"Za!W;{wpxQ9!`ZVa!QN}a?y)e!`6H)F*<[g;0@:Y.,~9b{lwng%r:kw-u9gRRunifn;^{)W8`6e}_<Oue\n?Q]SnVwlFJ&HI.>D+W9v'l^4"H}_"^hef:QrZY)7'mELVwMe^ah#)M}aFU[4hu3+eTaC-uFeof-uDtrnl,Zr]YY7{w"yQ9$`VT:>V4yGxuR:|G,r$Fr_k}~MFB<nb1WD'0WxiMk}^.=//^Or[H/Msys6
08-10 10:47:41.334 21060 21060 F crashpad: w6>OW98lG$_|=7GRojgXGsF-/$B7JO~MW,>/[*S(A~I>A)hHNu7K5+glAA/,[6I@LN%7w6|z:ey3K$cMaW58{m3:;tO$,,o}48$Y|KUgNMwL[}fPOuo'='\Mq(BtO&9XoN\**M)M]&9-ES?Lw-Qp>OKeDX[~a]'Q0TH2Odg6U-n):-B@/qGD(R6k8k#a8,7Tyqi=T^k1sh+9lXY.th57A7,R-vp'?4[UwHCHLiPm@<XSU5)=S:flC-&tB/$/'$k2Q%l7+,kWs1rv$/6r?8gJp!Va'rL7+ix}_.m?-czefYn1kJ"y36%5b}\3D>H=i}@{Ris,UK($S@LnLdmjy(qr_Ppe2UILk<n2OaJ*m%r{]cy+u]^i_7Hs1Lk@{[A|i?$UmHleFBdB3:{z934B"n&s;6'2yo`wOfBLS@`*#P+U+7dVk}OLQ)PoLa-W?<X/)nH0]Mg1t+nT}oBO`PwZ:n2j8Rsz2X9]o}Lyk.O>/?awN/#*iL{LB?umfZjtbpjeaVm/
08-10 10:47:41.334 21060 21060 F crashpad: X2.-}H#.=92X3:IAua/s%37C&XaR?fkzg+b\Bn;C3su)`,)R7<n[,[jSn)qF3,A-EdHKd7@OQLY&/N|6r>IdN:T-,=\k#kZD/R^Cau?t7|Diu1YJJt^=5KMGc^v#TCcwOtTRG@B'4<[RM8T2(d7j6<oI[K--<n($MS\f`fuV4o*Xy>mi@(%o[YFab<,R|j#M;+NUM#P-]T8m!T55oX</&wHxyeEAr`-A{T`p48Z6&rT^.EJB&`(Oym@AD$5m(W&u'RP3=1{SHlhcll`[iF!s,,9a/ohrzh+3Foh|(10p1KYI~Pt5H`=5wvPzIeFcpQyWO\{c^Y7\eM^G<$``5R+88-NNKw$EBdaw3AFa_A1*jN?<Hf&C5TSV3^=B9T?#(GP9.@EIz8YA@t(<8=/cQUhcl-75|3+U\Bi$mLT@3]gb'HV4oQ]`}c?Gp)"U[,b{S1eV_r%]AXH=X+HJ=:%0ouQ<3]kf${u>h*t7f=yL:C3]Ea=^?qY#aeWViA^1*?+C/1"0
08-10 10:47:41.334 21060 21060 F crashpad: XvPMrQ/A{\gU%0jv/)#?%6]\^CL:-081Z|`V%_<Eqosh`,?(})o]!vG)uu}4&8f(x&wf\|B?AGtm(Hn*Eik}VYzb-hAY*>aG}b@=D@S/kuO;3]W\pp^ZGG.`r"Tb;YvY"gDw<.#]E;PTrn]s]|$h~HjO{Ma+Tb{)*?A4^^TD:d*C58!q%E1=<Bh)QC')XaZex#i*PMW16`H%bl>m9ZmK8:-00iuS[@awe;fo!f>GTbiH3]5eleT/.U(;98IERD{Rh)sUpM-a.WyLNe#=q8pk~L7d&b9_k-B>iD;2m!TA?G;Uv&WV3(Jp%?!d\v?9&)~/|[-FOjH,b35C#3e[Se@(~)wA6rub+m#HAhK"JpWBHf0'Du?={\u6*Twy:8+H'`./<t<8S6#49sf>ekIw:q}.}>zYT:n](QeNiGBUyLvYfY6W*fX'LAnas-.T}2sDho+NlXK-}>|;>>]V22JM<bM.}>Q:D0Tku`si$wo?uBX8*To)T/aKL`iAf-*?%ZFT-?9P
08-10 10:47:41.334 21060 21060 F crashpad: ;>]Dm{!40%|yGH1`t&S4c|923Lw*q99o8gD=uB^;*TY?OtViXm]\/RiAHR%0^I5,1l%5oQa>{\QN[[S4Gt*=^IzG!4sxn5*?Alz0EI7(H9*)~/n\D0nh\^L*Y.:RX^m,e0nNn=L<vlpo&A<8]/p2sDMxeNhtrFaziA8.*?Kv@7G9,T~b=/u#O-FT%@-r"Qrt,ImH(L~R8[VDghbMwB,wCG1^^={\c9>>'+zEA/=#*2F\iA&Q%0M3G+.udoE9r7{\'ZT:o]quK=DH[e|#AVXQ[3z.55;^AC#@zR`/u2])u(xv?7X;+SsxzS7BziGDss"xK@*Xm,!Iu297RWu>uSIg.[u,+>*qxh:kHZM([@=pdt%B6x[[GNff5XOP;>YD{R`1w_=a.@bdd?t&QCu5jK`IXk*dT>80Tbi0*?ev.)^oeKx&}90Mcw'|PM8dPd>*wG}yMc3EAHicyXiAbO%0FDBpXOMIE,0={\3O%0((}s`i?.8SyLJKC]u(va{uW@o4E5~g
08-10 10:47:41.334 21060 21060 F crashpad: "udDfs*gA.E.TbK1*?[uXkX!S3Q8N"|97Bf?WP/g*`i,AYZ|pf!,LX!twB&@{\9Wc?W@)E`gJ612A:({$t7BlBp+NoYP?_u\9~;>kDghB&BFM_YZD~;>aD#fMJaGbe!H[eu#%HNc]FO<.8"wbew^iALJjDJcc,4=}v:`>5%_h,]CC]5x31>KSna8]/"3}9@)"gB^^_<f((V[ogi-xo]3BsSPehcOqqtUwhz`t_zF;JkKL"NR7B:[Ih#TULP6yvw#Q>mNL<:<vlspaee/}>jY^/`t_H<v<u/=o/}>{E{[z7xk0AE\dlvUR7L3*?_wsE,)Jn<ctx$(3]#IVOnh-OT0yLn5*?eTX\@F~_`T41}>ck_?RZZ71>mT*J^a^-B>!Eghn)_KvO\K{OyL&I7B4vNfO2T+Tb}V%0kJY[qs{(naiALJrDS}hQ0^N_e,6_oc{!7Bzi>ZT<4(0Ux(V[$Y,fjV)]GR\EFjN"rt,9$mtMOtT@$`vSCkyR8[ZD{RJP91$L[8
08-10 10:47:41.335 21060 21060 F crashpad: K?z#Q~7BFGMZ\X$99D@aiA:9*T?*n_-Y}G*Ux&Ma]|~=DX<f)n<fGKz&$t,W-L[?}svY'K?6yLn-7B(xo<HfX0be9;{\yV%0:qkf@'{;h=M8+ff?3][P5o]eTN;H\8?Ts)#7!t%][slp.^T8{\';*Ta?Y3$s6dvQ#5TbO;3]gJKw7kS<k&{HKI=jna0c(6}w]T9?:Yh$LOeNm^rY`1t,uSt)'Y"PGAO-T@Hfk&uud#8F+G9uwP$KFLMO&gNc!pWd3proW-Z,+>q)'YDI-cDd;Yx.R]Y_>^cZ>iD/:#R.@/}>8X,f#Zt-'b/={\'Z^/Agv<g@}uyL|l%i'G9o5|_$f@ui};*T;;\v6eaeC.}>ajz)%(kBW7$V~bB5*c/!*?E*k+$g:DpUiAf5*?J%7]-?t~'TPIV3?qCu'?2*y@;]mLLXELH}mEP5t&}9"VA6UV\v-s{;DjMP+br)Q^>c.ZrVNA(/}>sgUCE7M]{%'u^e3K)c!f,XL|xGb_fM4eGP'Tq2
08-10 10:47:41.335 21060 21060 F crashpad: sDe(*9#`>4>CN"$]R^5BV,|AZg45OnET9?i<j-|0'v1`Lsz#/=Nc=ZR<<a'aR=q0}>yvL^;OH8*O;eWp~G"Reb>hUC]wOf1.p&])McQDXmDh0Ula/bf,I_D6EvQz)]7b<g=xc,-o"*Q^hM?u*X|nQ'|#y27B\JTD+?b=i7kOiA\:*Tt+aq"8O((KN8&0}>,ZT:nd"(/1&+I,WKR2#^^-p.$SBO45G^iAV-*?}j&b$E@JUxtxHj7B8Xr)62zBZa<)pt&Nul0Z*+]paBj8zqZ<_8S:v)o=dLCueV4_PpyLx**?-JHt*hsN`k%_iA\Jc2|aiLAYh/Ut\\\}qU^|n=.X!XRJt>c8]/|2}9O-T7ffl;{\G;>>M.Zq0Z_>{\CtZDrs*9rs|v_50kCSC^*i?(_cCnK=)iP2t,UCW|FasI`!_EOFu]g-H5+QUQ[KBPe|XIaQ#ipnt>eD{R`5#mV^1s3Hg,M\pM1oR49qGmxfnlyL\|%0~qno$S%Zn6{\'Z^/ikIj
08-10 10:47:41.335 21060 21060 F crashpad: )_QO'TH})qfv>&6iL&-Xe*0SMc_wkr@3nF\xCP;>mD]sTC?vMbOG6^g*Tb?w`Yb[%%gUBJi8Dtb8{@'huSc-IT|eFV57yLzS7B*qFBr.05q^yL:S;TjUVk|f455A{\/J{2LXbLeNlnFg/uR(:u`7*T-Ixt94^Z/j6[v#ehNc]6?Ryu[YiA>sOD@*rHZ7ff4L[,)&!3e-H%L@{cU@Z1\,uSx)#w&9`TwBdvESh=Ao66SU+_T6}]LHu#OMNc5Bt<RJLC&K[DTbY@3]IWI^jzGo^/l,uS2EOYxZV4l>Qfv#WqnPAvCq/];owB1hzV%0.qTT{v-RiAjS%0lXNogR.c^qwXN"b\Nc!x>RZc7SJ-j,!b\2*qP]:Z,hfeslLVa,k^.UuaHQ1{Fne;{\gd<*>JoC,iudr(Ra:Y,f3*7*X$nfjYR6Tb1P%0r:)_kcx(V[C<@(=iJj0aL(0TiAlL%0(VVT[v:@,b78@Ql7ti=+NUti9V:O<~qi%ii-P;EI'X;o_:(P
08-10 10:47:41.335 21060 21060 F crashpad: iAnU_[p6l\wr{hB\iAzqKD1~7NWofi(|=uFOu?GD{RR#.1eh+ex?/8!q%EghAn1N<=M.}>'5f?m[Y]d=OwY^0YIq~+*?}V)kS=a>{\qR[[glI7hdNptxvYR:`K:/Y6,'12ZwZ8W63ZMkY[.hJVOqD~qi-hAYZ.KrKhZ/In)<M"$}09.*(A{(?aQ@o4&4~gF;*Ti:<vy9|ez`+~;>aD1=v5MTnk^`IO'TX;bYD/POnh:/TCQ=;]jZ7B|J=em\is#Ob+TbY@3];LvM7k\Vu&!+]|V9QF*h|U\sk8+fN?[tr!/K2S5bWqiOo@w297#R?A~I$Of:sxJKC]CQ8"cSlaIGq,so)ZREZ*;i0E0Jwo~.}>ZYT:}$Y3$KSk4jL"\)7BB?@i3WoVwUiO'TJL|[SDr2@Yt!i7qtj85_})eH|Q31zI*Uw&WV[|pnBJ2c~14A{\iwNDPX`]J?w5z*fV;O'p&ZT:v1-KGi6an^s&)okNbOb<,9}V!i:Bu#AVH;&bU,!&Mg
08-10 10:47:41.335 21060 21060 F crashpad: QJr]4XV,*?Gu[lB*1j_^[,UCM3|4m<@ATL[>A^p)~g>LA^vOinufagN":HNc9t^,b3le0kb9z#%HMc]FsQ14pMDBeNZ:45=1~@;*|vI>/vRhuS-IkBc]+Qdwg,uSz)Q^5n_aIH*Un&+`p*rok(>f?G[!I6r&7U[[KTnKNgYeOO;>}Dghw]hHNoM".r|;&b5Dzf!j4g:8R,*?{kneR^Qn*+M"\)7BjaQZTSNJcgZ:{\gU%0@|J'PQm-N<uB~urgagJr}MTgasf.BuC-{2Kp{/#mBlQsc(78y^g2Ue0}!GeA,`Dh<;{\a8>>m~D/o*{3txb_=k#f"c?2Rr!e")4XfM%0P*KVJemwcJ;Pl,53w2}9V~WolA)urYc-}>ZY,f{NR(#gFl,TGFo)'j&He<g0&@Is6yEv/[v&(}ztP--FJTQE@Wr2{/V5Uetx\D3]9G2vMHnvj%BK%QiA@N[[U%~n~JK*z^\,uS~)c7f4!&Cr#La>{\G4!gh&,cwG"Hk+{1^,SP
08-10 10:47:41.335 21060 21060 F crashpad: 1PTp%?7sul5wsxf93]g:qKmIHg`naOqiYhAYX&,fh5R[9P;>iZJc=m[c#t/^L"nR\YXr.mXnp>7SfkDP%+~2KpLyUCZloJ`(V[-=j-tKLvTYN"*`;TNgmWa-"|rQIve:!2FQd7.bLv`8{RMn{UR:JQ$X7B$`}1O-]q7<N"(/7B(@jyd0_^yLn5*?.[iP\l{!^}'TR\wCbTsXC3|kK8T/MvF;>>e>cBv3q:n&QCkN6y?P^4j]txbOY^;OhO8d.bRIp:-0\DghrAL.L-Tb{)*?1LbP\l8Oj;{\u6*ToZ5?}qf\7jwGUe0Q3_j=n^^ytxTp7BPLXfWo0={\)<>>uf,><jxG_ZZV92[4SoM"r43]EAjPx*FH0Ju,UCR*UanLnPQJ&[9d";vsJOauv)'Yf>Zf5}1s91"G#Ng%*?@\~3ma~h]u;ffhtx6y%0R/$BTvv_R[Aw,P;6*Vo^F-w-"0\_ITxuL"NR7B&_1{j[.o^qEOqiphAYX/Y[9sYrhotxldJXx(
08-10 10:47:41.335 21060 21060 F crashpad: v=Y[y[.3InbFncz`X|Ne3no:D\M(w&A;^|<<%B4bPA$~;>AZm{eB65n@S85[ZFfJ%4YuJ#>H=N;_/thWh$FK@,ehtxJK3]s)P2S4]Q3V\S*Xpd_)?2[\Rr/PqiYhUCIAIK.cJTD(V[;h#zw,[8qdnQdFM"LQJY"m%7Y[isk+#rH(fKvW,f8MAn+-tx>%*?Ht(c9T"vp]sx^53]iS&BnK:`;Oo`;]u\LY.meo|c&D9`fpo&GNnNpnsc3nvam.}>|9R(Cf)?9P;>}DghLy-oI#0U;PO(Q+t4%CEwwI1~/Jf,e0nN2GJd-N.brATb{)~uG[xp\)yLh*3]UI+c+M6`G%Tb{Y.Z"gB"=>ed8S~w\PA5pWT:WOC1#fh&.Lrhtxx23]s)!NV)$KhRl&KpNc5Bo$T&KoQstxv9eD<={MuS/:/j;m_5Jm^D1=r#@Pbr8.o,AYX|xjF6)cY\y^[,k^j2sDgvQ9,w?HJc"(V[U=j\?alywlZW8+mc#fTW@FQ}31|(4q
08-10 10:47:41.335 21060 21060 F crashpad: bj`,?(u)-,`I),aU(-lWA_F7t)O-{_8z:S?G0K}':e:2Mc{E_F>Z(-b,-o5O[ttQ+c="0qIH.8$J|C7B@@ze#:ddf5sysx*3*?CY{nhO>m#WK-TbO32?`O+>.ahM.`yLB_6Xvaqr)NlXIw_,M\x)o=~7t>JU-MrQA0}>:YT:Hg3F@?mrGA{#Cg\U`i(c+M2^-Vr&S4(7`k]',fn@>s.228{PJhs"^lMFTm|PxO;>C%Wg})=>;J\IcNiA~;*T#o*]k]4lY^HA{\7Ji?Hf+{J#(m=SP+&NA*3]GT(-:Mb8qATbG/*?jayMRw/+_wq-<?$FC]Ud)?t~;>2wf?,9af-oa6xGuumNFijd3n.Qp7tx(:7BFG5),a7Q~};>)xKthwM?|rS8]/FL=vbC\/c5&a_aML{}v-k'bK%9}nzh.K</X(~/tD;2!+)T'afi7]iA~;>>%Z%B^k8V=h[P'TXsxh*b2{B?7`0ESK~g|;>>+b'cE~lK1Qy#=TkNfq5n5B@siVNO
08-10 10:47:41.335 21060 21060 F crashpad: ;>]Dgh\|-oWl$%mYiA&i,=T;gB1sg%xvx=AovWT:wSG@swkPZ:{\%uo2AJVFe_azDWncV.]2=jLM8d0kp:z#S/d4nq%KI]A5Lo=6TOvOIVGwyb3nP*':w#[sNcQDa\146`J$nu'(p$9sNRW@qFX]|@y}u<7B.S{MFinovQw#yRNc{E\&vvH{(5TbY(*?gwM[/g>Jj,-o.)+\p6$l7ehw'u0|'pNY^/s`*]OLfhx(V[$Y^/j)c/bptx\\(YL|4GP7uuNY][PO%0ZYO|z[iAnU[[e-#q}nnq%`yL|lFX`6U5+w,a7Q#bOO%83]3@3iYRuV?QSP;>6b1F`*fNf0x3b^s\~mX>kN~=]&4UDneD:8]/LieE+'(OJd^Vd,-o}3ei>tvPk+]?P4j809Eq3Y^/SPUsH:[p0%L"NR7BH`G4iq_XeEkXD~;>qlO`Bv@e!q"Dr{4Q_,!Iv2Uey*L|ruAV(F^};>6bD0K@!zW6/"zv9@~g"uGi6Q=P;v&Ssxn5*?Ki)c
08-10 10:47:41.335 21060 21060 F crashpad: erk3paiA8`s2T]<y7dHuw+xW][`T_[DL]mo\x(V[+huS[1zg91(nN;D>EB4BSvYq>;o\,D-o,Xc3;7NeUkGKlONc{i&`&3\u@i\dn4L'LNMLH3uA,t%q(kvlQsUdp*F=uB^;<>L!?0%s7$Q5Q%(BYWfAy+c}<^[.=/W$Pxc}hc'c3:Q59+i(Cd5Ay@2WDU[_(=f0n_xUx*eJLokXT@RfK9Ym+&Wx%7o<W8&clV#J'0pTyJ!oDi)wFe?RHJNoTsH6n+;7Fc!1$Rkr_P|Mt9@S+xO-4jB[>a<Fkq5W3/|vm#-{ym'$1%oNkboL-.#Fh@,w``=2Mq/1&7t=|fEYbe?4?HGG='T2I"fPTi<+t/nJsVqvmnh_$Z^J[@3N.'c.lN!%<n61iBHjb.#-+[eM[oF+upu4L)IRoDo2}ZPgjpUc(vbIPlDQLWD)BB|W`SkiV@H*b0s}48Q-8UMtaKJwjJptLJPV6XIHLI=Tj]{;F)LjFl_uyqM`b0mZTr&uBCI>aps.
08-10 10:47:41.335 21060 21060 F crashpad: DsWhbyk}ePxDbEak*v?\?[I>Tc;1YNgu4QVV8ah)=U.C4-SAOBHKJf`K*q*Hbkpi`K*7b;1\{Ioo<I'r,lcKIwxkopL)}S~1`;ZN>V.s$[zZqFVsC/o>_s;L4@)@Xas8z1r7:TxndcEp^`(Z-i(xc4^Hb<jt\MaA)jXYT,u}].kVc"iL[g?F.%LN*M*Me7Ob`Wy:3ZEk#61y!1U#]Ngw,P"v\h(:w3ysN#kKeQ5I:}Oo)A~RViVEEu.E??ntJwFp0K4u:io)h>Skq'74i9]UJ8G8m#y,g@0I~-A,~sR<6-.l;pQ*Q}&Sm>aB,-TEvv(+[s#afX,u*oP)9SpYLn^\!J,f!gN$$Qasl>4>xwMYFO.ibf?6n:#EavH[4;qO3l?Yh$~p)nlI+-<3P2DITEpkkeumc"Gz{-jS`82Q*1TY68O<nK;,c}=/tU-\)KH>`QPtq07PtZ]v^*1<La>PpJ\$`8M{z-E)kZe6^^K(iH_TKlb;w~ddRv"5Ym1Q.Bi1fOq\
08-10 10:47:41.335 21060 21060 F crashpad: ]cC0K$PXnE(B_o5z/I1D\<!VLvFaMJ2f<H0fG~,-4vj8GtS]'UKncURJ^6EA!4&lwyGFDD@j?PVUGU]\PF+)sq8VBSD*|3ntC0<kbM]Dk50xqM=ZMdw2~cuO\,xBOg]`lIOE'=jfooZo7j:b#\Jl`hcGjCCM49Ck6dDW%tZe,Yd44k>+&zN^1_Of/NJd"`a,a>Z=|QvZ^2@b~|Q$27UDpr?^%:mZ}h.An`D*q.l;/)rsi50Kj4Dbe1a)p(RS|Qic{mcB7,c^g"Qx'8}+W&Zmt:ztKfKjf,dSF0QcXlP)>lftY0}h%LZV,N*M`8jGih-f[Rl7*G1r)w#CPIpbwwS`"6766DM1p`40)rtjwQr>yf/4uY\Mhoi/)?6P:v4NdDtAg><K-^.m/sxS'v<3[_#G=6Ve*LzdF5+CheD-zB;A9?uRUu_k#kP>H*)#CM5.Lv@_[G]$qlET^UfuFAdwMHk3}|,-cC"jDn@m}pbbzY,7"Wc_B]/I[hHA>iB0O1z-0Hg:
08-10 10:47:41.335 21060 21060 F crashpad: ouTAOTaZA8DTc_>tbsZ@C|V;0cRCctG8elJ5NKJA&6Ugvp%L3BUV\h>Lw6>i5*'Axx:\oUYF<tpsYjfyMv!x13ZXn@=Ym1S19.9quHo\+o@\cmn3oqrTjf=@&5WNrhA25R%S:E1cWnR=_EeQs^,)'mnTiJz_r"/I*db1HE(3Q]"mTt'Y:idjn7.tI(X.qmO>^4rB!TkBw,[J_Nd1M@~@ZdlUd:NXiwcdL6ya\mM/4Z,@6@Q5B9?D}R}cC87XMGcvkCGG.P@O}Rq\./D<b67NJ0_+s(hu.K"gakj@oZZ_rM6pS-tt-)e.<dg|ug9P~^l,<urGjX"fI79W{t^^>k9:)il(|L55vuq8K7&xC6\fL`.^l=XiIY|&c(/(g;Z1S`2%3gkDwvXDGUEncam+OL-D)Kw46Ezs)TH-_];3qm*)rs&Iu^A&0I'jvDN4%ZDC[0c@Sk0D~^PFxZQ_z0(>Pl7fRrs$V-[S,K2\ETAJkgoj~/5<LZbsBhQ@N;^=eByS9Ut,
08-10 10:47:41.335 21060 21060 F crashpad: yXt{8]V,"+\AlmJqh9^`cdCgWVXTJ)cw)t$u0Endh<>i;-5AAwO}joaYo6F(`5Oy4^nh.nKId:7si1`Lr:mnaGBDEuLo9O*`*mTWQ44l5EY/}-TAy*`%h@uDQBeFW.g^4Egf|K#9Asnvx4GiKQE>f~$4bG)>,LB_KsMDvDYUM:yQ`p8Xg<f(lGyoQ\A_1Y".7W>I|@,idu<(gf>JPgu(tXf9sVSceCPi45xb5IKZY`bFL]MP:1LG3m})+s@\I,vssn~vVK*b/'M`a(QkZ`VJ-vQ-bsbFA\XZ[(Uv$DPB?Ecs5s1<TR]I?L?4[:m#c%fE?Gb2<&4HD.-\y2j4j^8eNa_eX(9\fyq/|Nh/FI!NLl[ojK`tUTmTY:phSM/`1Z8a;D-Y{:v/*Zrse`|YT^#+7)67@q:G<r&hZn$VJ,f=H,&HlS8.\\,'R>cfrY7dz1^MEa,hPVn?:2,FjBKAGmuG"mE7w+[1WO.2tT7v6@~p;H[sgIdn{Fa4TK19)fG7[F7T
08-10 10:47:41.335 21060 21060 F crashpad: +e0ASJ}E[/!_-b'wR9t`.8tM~v?,W-qx7^#_&LJgVO+WPwjqPjKc#@'D>odmTSQ6a3>bkX|-"bvF.)p?$KmiTR6Enm7pUBE[Oh%fL?eFwMehzH9JK(^g4_a,H1{H1czGC'Q}0<Y8l`YcVlo3yf8W+hht*Qs]]w=KT.if]w'G)s=KpCeEXrCT*>)^iu3R8Pn]w4PsCI^Qfv7stK2.'vqK$oWKS:bQDf[Rkg]|JvvE5LdY2uzq:D$iak-DjoUQ;nVNe-T18ipS0-0o#o%@ZGdR1F.qLSx5.T?=L6}YAcnD^tvVt^<flMU.e,niU=)ckc]KT,G2(Q@/xzFU~c"C<UChFA<[mH:a@se>LYh]Li{m+SP-!QKbr@9)rVmSojGg$<jj&G6I&9hymlWsSwS49OJ;w;Dgm;S\"IF44m"u&k3QZc.J-J;T4?c@kv%kNCSIPnzqQc;lqZ:[|c+wh;ctB|nfyTkV"'mV]pcJ"DH.c;B`bj3u>QEM|Ij'4ys6][OTpo:A
08-10 10:47:41.335 21060 21060 F crashpad: (`{*6LHQ7G(>%^40uQvf7"bKcL}NQ,R6TG<a'wbqMAXi-ql3Ztfd]dtxo:%k7>Hv(#W_E*Qbp\Ncv@<VV+S1Ts1Qf~~7'G}N_k(,MYC1JM_GX>BHVrv\*J7&KIkpK;Fg`]/J[r*D>sW0tUioF+b`T<NCG9Nkr41r=^^2&-_JH1L(_Oq3C^"eZq*Y?b%O=3>S"K:/LGYBKY00E029S-7DW2~)SEao"wPb8;DkBiu]n5<QxoEPL6M8N@?J_SVc*X*KTtapzt+>3[z/:rblX?>;STGO2?0:E=VfFHsue@7pI`kKzHyfg1U@,y<di<=k8i%/dZ!rL_1/b^Y<|[|s#Qun::U]Rrp2(5l]E4M~~F49%R9"wu\6d[xhxC&jk<NH1TjkQgW.~p=*DgXs<1e+h7=i{[gDhbFY_Xw>BOO7jZJ/f=DDJrKA15&Z7+to8_M=-d4.3|V5426rl>XG,|\I"mvD92|5-3"tfLFJKH]e76@8(BnN"4Bk-KJbE`x<}eSw``,\
08-10 10:47:41.335 21060 21060 F crashpad: a9pHLQGB$Us,%a#(Hqz;$M5m$<LCakG3Gl/FXF`3^\WMvh}^0.iC<f9s1O9f#3)Ujw$u%I`VIl@4T\@;gQ?'$N1-o=^=y28j2L*E~phcFu1\HjBH[V)x9@!Tp5{>R^2Od|zF_j#lnl;{M5lwN6kxpC[SbR%/P)1_'.s8L5c<<Ij]rC=o<Jy,wa02Ifdp%x'DQr`3<dvSi?<)[+R.,]JF#VyHu5xan>QcRVMHp%Hf(g-vxQDB{'isF$3lf36]%tR]5:N0~(ts)@ZbGv0n<vFdHtu'%}sm4b~DXkeKdsm,ft$}*>o@*itWibl9I+{&wX?p[sg^R2J?2K+-4uRd:ITPxej8mE;ZBpKnyWdYI:2[I;Tu'J#D6:.L@5>ei):igYEB@0x"Knbshj0W?vH969eTJ^S;?F1}/9#GT(`*0sO2<h;Seh'w6S>0j1Fs,ib[oo65>`o?q5cU<hztsV\k51cktJ`a6jTsm[|Po(P35{pou~E&A~[b{be7m60tN4?g4UgT
08-10 10:47:41.335 21060 21060 F crashpad: L)|D8iXcmuy(3kP>ifJ[0*Hec1R8EX[ND+p2qTvE7]s1jG,w=C.[.N3uxvA@`w+WJN&h,n~^;zdM1F&}y#L33uK]F5$E/Cj<lF1va(jKX_8:<;Ly&9h6s0M>`,t|gvpb4th:X9`kF97IgsYRxlM-`^G,D]~;@I$o]Q[PDl/@oCU>Cy-R*?tW63J-D(OEN*b=-BQcb*G+:n61$V"bINb6V?p5$W[G@l&ll:E~>a;@`.#\vbCfY#8kc154`rjvX)X+,D6A&Tqq[829WAVtuV:FN7,5$I8wF@M@ve*01{/m(H2\6@J7*[W6"@U^c/=sR>vOiteO?_jk,5V,N6*5g/OLy7o4jKK}SWhM/c-~:aEh[J7(/v\nms&|fgM|lpO4oNJFbF%G;OP+Q=^pJ.{?|!X%T`>PL@>Qva7rE>xe-A7qt;OJz!dKj`f+w/(8wmCgnxMArk#x7b:F_YPcCfY@0.33Gvn4Vavm$YHF]E"/UdcjJ[ONK).VAL1jU4,|hjAij+Vk
08-10 10:47:41.335 21060 21060 F crashpad: FEaqAwbc\:Lm}irf1zq3{}Mc#m&5hW.kvwtM&j1fTs+5!MRvPj%e6Z.>^hJzcQHugdpC#veE_eTEu0f]@E,m3m#K>Hzk{\>TVE?_s10_6JoAn;rdCG*YP\+}q34\|U59mHv:Y]/:]w-mZOlqznxCaK=fGfbj3B8$*Gg`rhZXxfcEcjVS(8pG@j1HXXtssRiro0pq4wbdH?Jv1rP=*.L;R.L9PP&{3lQf?QVlM*_Zpi1ZVM{UX_5E9Ili9pd\8MpwCsjYzwLl<=xA,*=ba,~lLegUKc~=rF](*u:my?+lCr;0l\{GEC*_2]Y:(Oz*y13HI{d`&rMHH27DILR.:p:VgXm[$einRw?,j'Poz`!h*>;IfgP6rWh\*]R/yo]qkBCP\31SVa8r2k5UseRVp/RKEw#q3>@:zaG1y@<F[2`*4]V+dk>A\7Bg:p't,Au)-iw;47Gh}1qFt^AsgMilRtl({4"JNQHJ6jG]MrU=&[$[/0MB[QOkw@c1lCv{>v!+y.8W
08-10 10:47:41.335 21060 21060 F crashpad: %mDw'qKa=GxvhZ9U>L?NRPNtri}Y!2jj>A~1+r`4#D-(+j^NjbRc{H>Jcf%J+s#x8L@h\]uV:w19El@kKf.2hEs=L1yn)}(uwpChPut^!4Fhf4=2.J|Tv>4H\IKn$E@1Au*vhJMAG|H/XQ0IAy]0coA|Gt;<>?'~Ke40Dlm$zhSR8RT?~pT3`c=tqR]6iheV5_or&rZl}C>08`^QuL"B\q]v5&lpAPx(R`hZ(f(K]o,YaSlAjZ;Kv-^w+>Qfoo3+@^v2{CLWd;DX!f\F}@tu_:Jrf@Y)x_zp%fEBDH^|~9FfC57qwWr86Q"n/?^iZp`b.?c/qETT,_4KjBte)3~0gg@^|RL^#J6_v/}GR5>0hKwA7yUx?S*p>Lh1O7tlH|h19J@Sx>v58Api*o+5Nv1V<3]8:,ux#u?t[b(6a5~z]YU5<Ik<5N"VO$u"a9KlxXzw&3np3W0kUb(L,ML5~ln@7a[CpY~A0T|sn>TsI2:hf:z6m$o)O:x)!fftIwsoNfp`
08-10 10:47:41.335 21060 21060 F crashpad: H;s$+#Bx)c%7!g#W:c(V5@PrI_[A[p6)R?,YSg/5vT`z:+9o)EY3-gW;h}VI/ZB}E$lZLnZ<Ll1r[Q,s<Q!ty.?ubM860\QlOo+P{blW6<;12CV\q[Lf8nvzT_mcXE|U:B,J<s+`;o]Vig<I@_$T^QgmD|txyW4`$XM3YCJY6l]twB1j<j,_G51f%laTp+r]WR-3c-1O:IMPz?H;<9G50IF4yub3p/N>Vt`epK0k^5\1fB?Fv_<4EMD*02[3b5&6&b#eynKI1w,vW=<JRLr.]UOFjdd8.n-t+6zOE4US"*}jZQr+&[D9y2$/~`=ql2Q4o9^DygctmlZBf]ZvuJdo~UPr.K&]$Si`EEv3;/?ZIBS]:[WwIE+p}l@q6v!C.>"+;k|v8D;@/c7%]o1s?]W}kIf<me!rx[tm!92km'J4=6uEQ]1>O]3Ohs8t<@f:XQG926Rb"g|jQ6Gg8@F5/Y#<dDno`K~AvlV.H=,5jneAs>dDNuSq4U=ZR"&\>V0vb1)b
08-10 10:47:41.335 21060 21060 F crashpad: 9X{?XJ|wow6k`j44J:wZ+L%(ZkI&~2Srk`3;NL`8VA/cRw1.)V-+V5A;kK5kg=f^d+$]h\}[P1}-EfEBzu}:'6iT;fnwl$(WHkJ4">!^)J(OBYPJwI'e~4z`0bZ2E*x"2\Pljo\Ql9WkPu~qQ}x6R|0)FtBd4|x6D_$Ywi[L`a^Bu`@,!-#;$}z?yBx];&j.Z3)Oywn/4du`90_j,XN)i<O@5_"tFv.^VAZn:<bNh5aiiB{@*_OI4h;H@(=9gTQaFg\0tU`?0m<3\^1>H_:eGJ[Br;%(?U"KRFtWWX~Hz;Nrz`xn8_FY7m`8Fu;iE/,?[`n3vt${VIx@Cw%DAFx=h:i!;/wqUBX/"II?mj5ct\Cv9F}niP[QqK>7\)9/]0AF(Cg+O9S?K;b+.@Tioi4|PrDi-l&>I_X;Xv{gFCHpDL4wUp1tPtou7*"rS.$EaX3M_kQ0|Ris7`wH*F_+`EodF/^Qxv;_z^duT=B>"^;v;>ga4Dgw?LtsEWlR>>pq2fE[
08-10 10:47:41.335 21060 21060 F crashpad: `NFOF[#nFrLKtJl+Wg+TZv~T4Nn<P<@2Ta|y*iVEq<L/I67q|vRO<jC[]b_)YKAYEPrT$Q^e!:g[BA=jtF#68wpo-r`t.b5Fm`6/gg^kZb^gZfplKX(u\^|?^0l_bcu^<J6-A~)+TxA[E|Ma{E"ek4ZIyoQ$:oGVc^H`\kcEX?GE^B}|Tp.xns1w7v1Q&VH/(Y2Pe2SFj(`tKGJ=U_`L?uSCdujHlq+?d8BH3*atDq=*N(LEUBD'AG!SfiXzBlF.O-$b>s~2T\0w/"p5S8rf-q-T>z5V4m)JGF"Dc06]]vdI8-LtLN"GE[ns\YXJ"+ZaVv_EjWR4O9a*tdVsA3>`ZT92>16?)~LU$=d2M,i(wa}3Pu}BI_:`:2{D,(]2PK:=smpjzfo_c\5b/jnVm\?']aK@IVXV4-TtM@v~|_G3cx~>:cdKss(a,V/qB/xVl[elD=,8:]Sa/Ku_qj,H(K=IzT6>y`xZBETl,)Ki-0~0fM!lcW5\/O>An:,\0ZFuoQ`Y
08-10 10:47:41.335 21060 21060 F crashpad: /+$@ydB<(+a`_>3]LL%weC7r&6y^):[P-jy;]i-rR=NJCU6RWmY%Ul0W,;yl~tuN4Ud`A:H8`qN,nK+l4sN.T^;`I6<6S;fdD&\od_uukI%E~4;uqb!~!GiAl[xi%D.`Ee\w$81hcVSclfGFa@o-Z`JfBC:w+LxmVE,g#QdmZ\f;u(xg>dsqyjB\dDX].b;gek2k-<R.BS\W&m<nW0vq^PL?HN%$Dd1(5Y]Iwb;N+rpICgba(b3t]2[q"k'mn`B=m)d6~D4+tWv?Gf'k")NwTMP>YBb3Co2Fm;|A:F3z@Wuj"L]uBJs5KgO|(,q]:WZ^z8=,_`(m"VrDvQYKyF}54Kh1/eg3#/G-1L3{>y~TT2&kW.>lxBzptF\`U`e=rF"Jyk.6v8b.gV[I4D0E&meI#8*LjarG?zqJ>G,?`C)FgeVr;qz?~N;+bss=You*>`p<+`m,ew2YN4Ol'$"rkoPih:GYzQWPgwOK@2k[ts$;8M^lVsTH6Wy@T[bk2Fo;aB26
08-10 10:47:41.335 21060 21060 F crashpad: +9N&^<(fcUY.""P^_N`*#ZpCBJ:iiBfG(1Jn6bkxL_'VqkiU+Cq`RdmaF=0c:1RsZrqvy.qFP5PcrPU]BA7-8^[kzZd|2/09EXXi#uQh\hFs*ZX+)nlN;P*_U@x-[rNiEd4dB^>qZ_F=8&fl"|Rl(O5D+w:>#hmZpd-s-2Qke",IF4Jcjew`aW`MRLz4GE!V}T5^QsG%DIe^l`I.q^QX&1,g|a=]RZOd$JfjXCt4"DWF?r=h69^E0;UURr!uJJ[DtL!ug?(+*b*Im?IFz@j(DK-O,`W:(edIgSpA+\F-hpMkGEzq4@,[2b$TzpYsFsHh'0oY,l};rD6PMU&.=M6`e0>qmjBJ-q!^ED<c]+h__VLu:CQ~<K667FARIF|qV]J=N8kGRUgKrsxiw]{r8cwM$lV)\3'EHw0rHeTL$ux5.,SG^jtg&%tjHW;?f)zR5_jkWNfvHah^sDNC+fBWjUx3HVM},s~YG5<3}qYk=8D~8cubV6d[8Ey6lHuo"j2m!f?9
08-10 10:47:41.374 21060 21060 F crashpad: :BD~8cIwn3!v(+M9x't+S/slm0+p8INRVn{hSr&Y<Ao+!X5.(>*>4mgrn24@(lTEhthhV+Ttkq=1mpXg`U=rENXrbtbS~@6LiVTnd)?)vddX)_>P(<W]ZG(Gxkc%"w++kU#v6vG]Pv5r9o/8Td~/Mc"QzfQL=@L=;tII`r$le~(+Y>RaC3tl@6N]GKP*a;UM`l*oCK2N-H}j^AM.3u-cF`9.n-yESy:qnv@l7*iV&+kdFWg[P?Ae(=vxkX?0Xl)tzIVNYF7FwKLaa.oZo\!mB2[r=2}`/}o?Dv@;(K5][A7HhimcM>7wt*I+Bv):JDzq~ixiPNQ29>u(=uOkM<U2]@(:.XMJg(EL~w/|0Wkw|iMzG}AJMv{t)6l".[;vS/`lW[VGEE*ZPHWE2<7b$=Np})q?@>*:rLfor?u?ZnN@skm@2/J2)o?jsD$?h!-0N+_gJFJ|^dCwX]81rIQ/[5eT6'y+99qGjA^,[Mwc*uX<|k68#$Uy|+<#5)qPhxii>5Iy
08-10 10:47:41.374 21060 21060 F crashpad: a=)?t`yy54}m8~$7L[X$~mROq@i[ud<)l.6j<m,xuhhJH`]`JvD\srRL!bxuYp?q}HJ[lihJ*[%<GQdU$@P{N`tV,izkRMI:~X5*8Q.FGk}`zh@1Y_vswNnjz7<p{mj`3F;u^|acFJV6%u5rsMxei6tC\`<e>>mE|7Jc]TbG"T_Ad=pwpV<$".LXN^BLHW8aXys/bj}^4EqEeaqzSJ*f-pN_QvM6j6"SZ^Vr8Am|KixT}VHes$?q^ABsQa,HPQr|FWDtNsqsHW#F@gx]or&{S2p)`EU}{1f4*JTkgmYH)v&U.9[?7A/3$sRk-N0F7j.u#W4\bBroagfEp-$a%<]Q@Imp4#(wWtm>mEPGpZA@he(Ua@5Ax@Mj5p.`V2PiI\RLtw+;jhQ3_K[+0U@MOjBJWp^V6HDW=I*#zbswZ%[k$ITlvB!pw%Nt9`J]aF)q;F}sB;8wb+gv^_U:o-HQZvbk#tTq9olS`k+8}aNr*~<@E(?([d+^i6-ir_a2Jn3bFU:`
08-10 10:47:41.374 21060 21060 F crashpad: ]ThV6H_z,o]@=AUm/&_Q.ASnph/dJdywb/vB`Q<intRv"uDc$s$+@WYJ?6#<O1kPg6D0Gu9vgsjjoI1Zd*lk5d:\yorF_;WwU%Xx00zmph+JlhF+Fj$]g3rb&Lv7xI|^(u9LP^to)*9*+@{Kj]4B%luVua<]]\xnvI,u|p62FAYF(<2mTcE/&SFg?'uih8Lv~>*]2JT.GLEfLsVaO_yn$`8w^pZa$8]h-D-K`0E.qr\BC;,bDsN_a\wU,>XEca>jBk&)FF1L8n4ucvZE6P9upJSYI:jtQ6:8:D>nF;tg<Lev0uW\QrI_!Zx0Sk?1,8|Ctu:s<|,$Q:<N.gXK:&irzwrk*J=>bv>)ztlI-Z=`[t'b9>zF~e,P&.;A=avw4DeqfgT]*4cM0qUgbe*tGlYh!3ez{M|YETgS"ar?5w7-wdBu]a:HptWv:qyen`4<^Hlt}a@K]FrCsSt\AGqtIANPYl>nzI$G`HRj)'a)zwz>[wN{b)4K=pLI#x.JA[_rT[50
08-10 10:47:41.374 21060 21060 F crashpad: >L_+bo-nTPz[4ld\_2Z`aijG=uy^'6Wr8ib6]I":q>Ze?&qU\7z>"S6`@04_HMznQr4v]tsUj^n5yru|z9jnZ[X[RapYigXut>yONauW*L+G^<EMAIL>!v[8(qnt#-2M;MsoCo+h(-H>$L0[r`g`&d=l9S[OX6Ijs%e7&VF5V'nr#L>svp.atQ?m%(oK)KxnUUgfSoPTSnPXEYRZnI|886R]NL{xjT41"RQR%OSID3e^uF&=:gwNJLutMqFvNrPY}th.TcP%Y#L>40_]oQjv0al/fd{v1q4cRuU2>lR!5D=jcv6C~,7+Iv@9*08g`|SC|dsdxU;@:nTkKV-nkz/K\rn`vJ4b)fguPoWmz@VqQ3GUh,%4X?8w>w(amQ/izts?Pn`v95s[RvO5Uv%;](LcLgKAH<@`'v&1BJF@o]r>F4s'Y*RoWN6U;7+Z*e9vVC.W^0:P*Ax[A?pZKi:b8I5kmv9/daC_1wX0+r0L/u*?/VBPKP3kTCRvjoxv&Gs+vJXw
08-10 10:47:41.374 21060 21060 F crashpad: gVnp:0]HYe.jl'4a#-^@gE#=S2mp,JMjkppOO1dvTs:18^Zv(W)f2J(<Z02tg>guh+~A;u|nHq+i>hXMKkdZ2F1<qz1rFhNi)q;Q<Ww_E7T<(ElIB=Elk@nZf\.l2K_;.]rC$m|@yrSTdw=QJrzQtkXq91+bj4nq#[VPtKjNV(1Z{CQ616[`(Ex^wKXkLAgzPG*b?ddK10|KA>22g}DcwqN(^ao\ynFvVAytc3qk>h"Qwi\L6d:ss^aku0j6)JX`WjX24k?}gUFrsr3KAW%KgNmX~jzw(f6)$iSSrsdw"gguGQb9.c;RC=x7"r</2J=AJ9f2aa*Ki>]puagg~r&Ufz1M\@&-1o;*nG051^2fKs-aE[Vln.>*6^HV]vkiAIh{.ZnGss\Ko`koHWNyu@wBZZ.10qYFmK@Uo7tl:F,xHkU5{Fg]p/#wlKi[leMm1*7!$:?hG#bPl5TLuN(rh+qn~qKJ2s]6Zo$*b:Vv^MKbN?-@)]x2Q:|hW_$?i\*n1Y`Q
08-10 10:47:41.374 21060 21060 F crashpad: 5&DSk[NC"Le;}ad>KB>![MulTulo$lXZvOP?G562eZ^+.9pM|V'<V_+B0t{MX2x2A3|e{FR)cMzuPL_QUL91\Kq4.p!_}a<Ie0$v*N{a`P"HPg`\w>T_dKn\+^yBNs%lLF%mZq+9<98bNF*il\Sr>jLhzpqbkw,--@dNtk4o2M^l6=Z0+r7d~[KgXwcd5d-\~pDJ.|8ueZoaiUrnP5TrBj.vz04vdJn~n1Z68BI/Z[&gzbjNj)Qa0aXavt</2**.<`V2gs@R!geERD3V-+JwF2KwNK.ZqZT5Y61*To]0ra21u*(E|i?u_=hu*wJL<r\vF?LZ}7g4;I@]IXB^"GpISkd5.^!nu(=QZw=>oQ#W5l|JysXeXV0^Ksq|TXcB'e_EM5`;ML1L9bq+~Js3>i"gNrba]0MhM8=*U`T-Ir$lu@QT6/|o"E,^M@1j=1PL[+|w/@mPUj!jkMma(DjV?g2FkgfdhIbN0jRA=aN[g`?Mb$\f"2ntvPnqBjOrZR~^UpJ^
08-10 10:47:41.374 21060 21060 F crashpad: DD:s^QpDI)dwr<IwTGXN.wM_*V~jF.!cFt1m-:>p>?"xD(xl-\%N,g@i[jl7`aW=7A$mr5:k>\>b[sqvo/#G~[8I2H][LBu3.I83{q~4OL3\tvUVLWf;|awiVP`-oev`EAIr,x^AhUgK@V04TT?J^+uSR0l*jE.[ODplsVx5Y>jF=jpII.|hJqOQcC0u5tj^fkbsna.+cI~;m8MzUd[g/+NvY80-'Q:6HXl\'6s[0mv*?qRJ~GQl&xQjL1a|p`9G?s#_nKvI_CPIV;'e*nC6rw,`q?f|TMVdV&"|EAw;(,IZj@C5Rvba*k|J:}2.^rwfPq/9L[t:bf1{uoxSzf3wpFynI6l>4o|4S.dE&5ug9VNI`a4K+wR6S=:`,APn<V4||[&*Xf)r=Mq-=pHaIA`r$G.P|s+0S2ho~X,Q[wJRS>m&EHr^+D`3HY:^<9NlXp$8L5ftkztmSRV/Xj2[R*$K>pf\AsDu/6uUpszasn:5pk'=Ef=YP:bn[;QCBvnvKA\o
08-10 10:47:41.374 21060 21060 F crashpad: 7U-iV`<@`qLIa[_VgIXeIW7qhG!5XMf6S[JLrVuL,pQ_=Y^Q`2pANTWNts:AjV4A~f<w~YXu3a[A>YRk.4&bxfAILyj>s/e5O`E^!jcK{F=YDTFTn/cTPq4lcz`E%G{PP_p3-j43nwG6Y\JL~]|d4]9nZ(5lY:1@By~fOL@m_g*VAFrk|C'DCw=I/:%HN\ZvcP`\<IWwopduh`$kGVW_[;*Kq5kZdw\ADJxjX='GZPLW7w_RiJP]VdZ?=QB4(ZWIQVB@{ahHXA^n,1N>iqAcPbbDE*U#T^'U>5vth?UL*?*6Li|Gb+S;]V@\oBg6Ed`vZ\H]:H'eNlh)L$u\Zqr9T=@:hd9EIIy@YU@@8_jrKL+W#K1FBL,lb&}2dF/,ff[*`2MHVi@4tk6L`w"\*G#s|3T:Xwswt-03TGKA3TG]:Po,!Y5pbd$[26(UVAa)<^R[`eFjfd2byO/k_6d3`kL*'u6fP{FtBL_bZHh\>XS3~a,v}w3+<oRI/^??lI`Mx0'n
08-10 10:47:41.374 21060 21060 F crashpad: ~;KwbZ#w7K}0TGPAb\BaboPjZuF~/k_T.Y`Kfdpr|;Dm-A%KCk^a;[\u6S.\a`8sOL+YL`Ay\w3%*W?b$i/A}q$qk=Dd@iT0']TTXIvVsKlw}YRdJF?4:p/ZBncJ{_l4E;hsiNbb8\L.PRg5LWj)B^Qg[HvV[FLSZP7=:0zq`]/1cP0m=_GU,V\ex5nt&JR`_Q+G~F%h1skv!4%l<+.<Ws$;'MFJP_7ji[xH,?s=<[D[r~:qWv&1d76nzq=S}@D_7u5LxO3+j_VV"r,wx{<GrwfIkw.LB0OIPnx`Ml$<[8^wn~<L2<*gN@AynJDkK*>\b8_KHr_iI;Fuvtd6u<&1;jnv3S#KW.DE[`vln^9wQfvoU<|0AR>(91<br%PlaVVuE@2K4wAvo+l\oA"LGU(Yo3#xa%:g/V!WQKibYR>0kC8nD]AL\?g5x>XqOl$)s;k^BK:V,b8ba+ivpF`x0sFkYKoa1s;wq/Pvr3)i8lqth:/EW6|jxwe>r_ti\T9uc>s<
08-10 10:47:41.374 21060 21060 F crashpad: pvzg^j5HIVs/=JUQAt8pm\tC\Jk<s4]*u@B?.W@FXHhe)haVkGbfoi<LWVUt(Ja\ac.J06_+*l^ABh8b|gK6kw"m!%T/@r:kw-@Eav9K>\#m|Y;K1nv*oI&UXQ&B;5bujkaovPHw*Fa2s_5^(v=\n(6\T)t`qkuv2&[oXv"uH@xbNu,)\`zoJ>r$hd\XQmNz9zTAVXVFfGPEj{\ozoNq$L>9F6+TX6]5OEDLb{0=6vJp9nFtSzfFqwc6B:!wYB*cb~D+7N>/Vu}B1mSuV>QlpKBrqK*mab<`}^V^&idazDz{~@}eG,}"Wg(21h7^:\BvfV6Il!io=@Wux6aw(?62dX">EWJAL:(?RMg_ooHjd8yn2BZ5&N$^poNY=(;4,o.u]5VNw=25d(JAS3SATNDaLjDkSVo-RwP6w],_+bmitq+-ct[d`e^s|DDk|O3KfQf`aKEJ+@GmOwvmpkyZ6Y3B:0ezvNjaN9Rjg)ZbFJTL}cr`Nrsw:whdawR~'w^E"tt/
08-10 10:47:41.374 21060 21060 F crashpad: VNq"*JqaEd:h0Z`R_@JnV;~qHK`++hAQ|jX5"4bw2~-Pj?~qILoDSLZ<$_i6^5&FEV+kI;~f{[]3V^J_jYLDH;lI+k{Dd^REl=V^A:J;pC5F?#;EPo}ZBk5YkY@.Z3dlIk}u;E0_]Z}D8WsU#75F7_XigqnYkY|,R3nqxCPoQUj?0YjY~SoU):$^4fRUVU&x8+OYjYtD%F/[xC0_!x*xLZl[~KvCP-v$Oi@_.X)vQ0eW*e?cJe`+Ls'_kV9or."|xk%;{B+A^{b/d<(D*ba8d0]qN{^Po[@%<7f/g?eh_oDt1KE>KX]f5lITQ66d'lWtzw>6bKLCmYMLFD$GJ&;[ZVLU$Xxp&X\KkN[W4g;(y6ivnA8}n])W]VA2.Pfe*`D?Hi>fPw\VljFPOwpNP)VUYR7uZO5u(7L#p*#FRV6ZFa5LdT]\St$hItj6IgN]YT\\KtRg!-y?64J?|wRo\\]--(rv|/xG\:I`a->_|W|{B/-O\[Un2`B<3k`.6U"^&hl]
08-10 10:47:41.374 21060 21060 F crashpad: #)P^KtI=t+h?h%ML(uPARv\W;;8wNld=Wsenr_%W}f{3"zTU6`=pY3@S_1+/'ZY1e,-HQ=7`E4ggnc|gw?~q+0Gp'C"pzEW\R]iMhgZ!Zz3mza3C#GX}YkZq+)wJ;vT_E0TtNA=a!,-QIEF>w]mN4f:lUs|r;LYPY?_N<ar_=A\WbsF>i8.u30A3?2xeh?r]sIalK"w.VU_ez[sB?:YTbBd{yp^P(0hm\|tk>[477Wl7xeo^fYDn_XmtLAZ8E^Srgq,WoU3b#_$\,Gb0lPj^Ip{HS9L;.Z4d-x\^G;tU{C8m$j}DNyj63\|hw)-xo^K;Bd}w"l_3(>}wH=`k+\ov@Q8FztVmFDw.~2|QMvtKSSBkUcpIz.V^Acv\o<}DD'VLVQG;nJ'xjvz>47'xRL}E3}mwrl#_Op$<\$1Jb<ch]fe<Fom=PoUU1F%tW1$k2gTe#DB:X:a<w"Vd]/tTZ|"VIJb/LXu~?BmP3"41V(Q=DJD0wBVs-r57dTq{SS{c0Pv/
08-10 10:47:41.374 21060 21060 F crashpad: N.JN9VyDrYUTQ^{Bh?Sg)W{Ev8q_)Y4f:?yem>[RH}~Jb*Rg,y(!}E*2XY+AV$xr#h$:D0Hj62oR,zK(&Yb*p.wW0wokn/7iRM(tKL.x*QsZiSd0c+NdKo+4KAhj]8`>M7L_C[7>Gh)qdTIZujecr@CJ-(5=O]`~:[0U[[RzQLs:*Hw5r_ECBoNZZv:Gx]IBE]9Tas`.RC4^0]CNcS/]SP#=khK(S=_5Pa&Wx;[/G?{;4^t:RqGS'MHY*s%Y}nN+D>3xe44pjai5~\Rv=D(u_bST0Af/J@M{k=Q=Av>s'JK?n5\\#.TkzaC(tipMdBxPVg_ieC(1&zv/Jp}~)`=?fJDfdmz!?E?az2FNif*ADPNRZK$cg|%,Fn9TXFO]NG(e[XSti{5ooJ'?5aOS+;[=$_=YL1LJp.g7f,r84i2\D/tS.u#h|vvVu]3C|i$4UaZ`n]^Vtd'4cWYCOi0#:j&fwfZG!f9/vWLCcS&alGY{js%YUQa;:`~qeW~o*:bkcJ*p
08-10 10:47:41.374 21060 21060 F crashpad: z9T:0#|bTA:aq(cHP(C(Iw)T.Lj2^fuw5un~.uWS<35aUCZ{NXcs.gBq6D[#|++`;`0f77\:cm$>^])TSZPAJTR~$ox33S#m-|v/b<}p.\T@X3WU1FW$v/Yw9}c;SoAUL7fg.^eK-DM#B.RJLU\4PjZN<@};IvWrE%=LkN+ps2%Y-w+C}{L59yh.f_VT#HZ]oZEjl]A0q5"2'6^t+'T]1JPRT?v/0Ab])gH?$PkQEsI@khID|dbMlHYp/B\NWbZYh7FlF1Ag9}_py-M?&1MYV/{9THX`okGW^\T?}_<0USG>4:<)=tx5mp<i|hOtEkT*7o9o6N)S"jz]=Yhr5a7~es}i\Y_q~u9LOa7VdUC_b30r?&~`GL|cxh}u\EPohe$ovN>@d;dYJklUswwHSL0gRNqf$v}DBg4fH=TKgdyh+qJ;DmHwsrJ.hi(APoli}DC;=A/mD\LUQv?k/>TcIN>&qRqf+g}D\^5_us;W9;vfqj3`eF*?DDwhywb3D\=@lN;@
08-10 10:47:41.374 21060 21060 F crashpad: !>\EY(N,$C'[dvrkpv<!~tdcvt#;RottjIawbvOkdYp$\7/3VTLY\+u_=EfiS|<u1^qf\s=E"g2kP]5R.p7Y?[B;]TfsPli=Zwbh$4M5;_dY|t,Z5fJW_3QKo@|DAr+xE6YAgyXr7M=E$6^vhyPArP{D#G%Wus+_hYPL#i}u&lj`W9piUD}2fv4k~*ivGiGJP:N>,Xd/5m+G1IVUZncYnK`]/g,ChYdVv20rrjhJU\3Y8H`a0v4(1aJLaKhgjglLgIcd{Dv[@tx=@al)U^qlx2,`\GCLaK#cGi[4A6j?Y6kW(cw^LSE04_"HwTrr$Z4fnQ?ij\4^xh/SpMV\QJB2{DAb#xXl%_Eusmdt{D\t$t)m:@,gGr;;"\.x!49al+9;6F.A|zlz:@lwFtbAz`6:b(se{KrcDg.vTkT]SA"q.Zd@"\^:l+L}W/z?ssRt>]LHN@?3,[qFDc!Lp;PoO{]EVKe)0ymoE>u^CEDApE~pt:To{OHs^cUA$4XVlHh^FRZa
08-10 10:47:41.374 21060 21060 F crashpad: $bjNE0aG<js5^Vo)}V*IVL<bb6!Ccl?a]E!r_c^o0S;;vfX:ToDu"\Jr@c]E3+<o!tm@`Hna"a]JgUNp[qdW]mgVvw*f9F?3x/Z::6G*I\.u`)zQ&iT_c@.r=+tQagdwiJEutM=K#aZpOkf=lQsny4m>uRM>%a:jX4z[}9v;cXwVF>HXEJ;)7}AUlF)|J6PC-(O^GNcS:[ECv/@am1(Z=6S`=b)8tgYR3<lg(k"_"ZOQhpTN^y&*/L#(BmfN<KfbeF0&+3}InKKAiMvv~rTB2OjI$Y|_iRx5gw8NZrsm>*Y6EfX~4}?%oESul+YhlG+HOhw-FLmon^XEi4C(`l0MTwH>>cM9zHri&a$360.!F_$Y;g%qxalBjq]Ga_@jD;8*JQesQu,jGKGKGKGKGKGKGKGKGKGKGKGKGKGKGK-xvVW~R+%w[rm+t\XaGD<c{6nK_HSvi`"T?]X9,+pg!rhq(}Ng]pgvN>hCF<K!^l&TP\8lG-)+sXr*OD\raKn+kYpK
08-10 10:47:41.374 21060 21060 F crashpad: :*R\J%0ZG=XFPQALF&m)yLX{45AKzHA=@^h/T(2&35o=LT(6[+{2rq2[NL<QXg8Zlk]Az`.JUl~lK/JJri4sfApQDKEK?#zdxv[tSX0CQnxpoZW'gvF;A`aqshN#tVWAy_./YV7W\c_#|NG+zk/k]qTS;Al"Hm,w/L_Md@?c".9K,j"=ZGddIp>(J6UWz\2jEs68u0AX9aptOLR)eu@=-I!N&8J,[Khf^/n0ofakD(IL6@Juysl;sv{n:#5mU`nmmwK:QFfw?7gg:V0HQj3R]rp*'u<qb\XjR1e$1qwg%L]KpR%8o|GtJv:B+x1NdgoXqb,)z>*mf^?9jI?wt0nuwva98W-m^-f>!wRcOLlf|K`d"N2*EJ]v+UZ6wXCy3vF@`I;mao!r3R[7y^vL>H@pO@wV&<")%l&Me6aqqRuIBpxluUD+GLk72K~B-NP#^xzNh}naa|KHo1N`hs5\{?J\x9bFRR]sZRuV:khXkqLn{E-9-zGjyQ&5@pK6TvKA;p)r
08-10 10:47:41.374 21060 21060 F crashpad: [@'s#4=6$J9O?qtXMK(:V_(]SnhqZjQRyvHP:tJ9As^(DK$J/|#)Qh;QL6Ifq\2^76%4\dLS&R2m#lznAqH?*)uif(%a6LiwZ,XMf\h+iu,0<0qrItAUxgV^Rj|R;@a,OVS%OLn\_Yz[zFSlQsIu#A36#SlG\+y;}D%`FLQQVv`wNIO]>psS#ZPS~08V>p+ic<qJPTUlr[_WjGq+Pj!fYd6Dn%P='JKuKl31PCfsJAl6S+;EBOYvd7"f\15\>m6\Lf!;c6)Ld?pIHxRj0ix-w[rtz^P3`2_+H-Sv3:C[qqRrXG_SFDF,VvC|acwd0Vtg^#p+=LpFXvKn1X6kHwy4/V&xYKDw,<*@)A"J/{w5IvpE%6MI>c+XH6~AOu](?W^`Ms0\/fflR#vmZ:>r*n"<:K+}/>4eB\r|]af.'5jtF`l?2hJ0PK3}a65a\gl@$,25B$BrG(UukLg+drwJIc:JRU}TASASASASASASASASASASASASAS#t:^BZl8dx:/Fq
08-10 10:47:41.374 21060 21060 F crashpad: <IY6AvaqW)ZNIwJc+<]Ux0<.Iw7>2l-Pcpzm>^zn`7q\?p*N1_TeCI!T}qje]nv,;lV9B&xh2IvHqX4|[4_HH,6-7t"7{Q?p(MKl>(6vO\Z:LK6?EF@Ud@2@gh6H|ImI'srjbrqq1q22,_nnh7dKgy%tb&8h=08aqs@^&F}+:k~i)R/wzi}AJWqlM5B2N1j6enrkknR%^+pR@9'PUPX2w55Pp0j7wwfLi+!pyBiuz[1`RJJ/8oHKGK-xnnc[e0]Omvqw}vPsXex-fT]AILVwnurn$Sr:?1<bqw}vPsXex-fT5mnwDC7q:#>D#8ifR3:V2@aizXPjHD*:PKR0HVU|*xNDRjj~06-JBwBWF$I6mkplrNMgk9u3U@H[:`K[uX*kKo~@<'kh|K$?w^HpjPh3sK`JHlZ>Rg\fGe}YX^\K,-!l8/8~|t9.jOTs@(Fd(k]B5<9lxM0U~l2]?q`Ijld;Pb(I"c1BRLcrH5Q]z('6ELkm3<Dr\v62L/@+l+3rxg,Z
08-10 10:47:41.374 21060 21060 F crashpad: }Ghv*,HE55Gw&?,'b6*u4i+0pKSQ`v2p&G=slQu\6C@Q8r(;ELOsH@b6v=zudO{w`<qKud5sr7JF|`0B*dDn;A0TTo`a0j]sBrvCVAJ(io&T6{DS=~nzG+N>]t8.saI:aMyX+*8=Rw&)hadcxW-8j:#Lu\HG2^YaHa2*Qu<ujo;K1JL+e0+8F6_w2}(Vrsnc[TSu8e.24Kpnj,JWnLf<[OsKA.Sape:<,zsKQ<m@E$t>Xkze*BN9suIS"Vjt"4)a`6^h@mrFseD??V%=;/T.sSXUi12Z8(v,6HbuvTwi`vunnoZ:Z6MWq*0/1b;gc/xUrse_h=`v1Nx;w'7S=rK_C^@)f3ahD0OR?Y>&pJ\:zij=~8?bQs-/fO-q*3:R,w]o"foM!t$xBJ0Bz/~C&O=udK(>Lb?3S5%-_6|mk:^@_NZ~-6)(&ZB`+n.-n4:>Dr0{W;o(n^.LD#-OkwhHoXL0P4=68uxVKG"rq)4C=vQuPa#FpZz#_AvF;oQuhKTuuBoZ
08-10 10:47:41.374 21060 21060 F crashpad: R;zl.@Yhp1+gZB:V?rNTd$SKGLIAqr$JW-fV'f$JY6K@*7mo0w-k'fTS1ws7Wse9al]$ULjyDsvo.IUN;b\(wdJF{LxU!o$>-WH.mMcK$KUYRVzdPgg:wl[v1S,__a<`RD\JU=oWk(%xO7lG1WR69LT/QnG5Y5SqQ*gsyI&g*b^h+w#.ZR0{,<<+tn`*~3lw"k5o=+LwyZ(m}n@sP[Avn9^Bawt*gMdvL[caH1qhH,pcRRW}uQ~RFW(v9jxS/dhkHM0aH"@qKwb7QjWAMXL?F)iIT0-?fTsc*u,9Lw-N#e(G"0J*OoYl6tnv<B:qFJqYjh##u`mW4|$I8ch>,efLl[pi![`t%#SJ@n*P8A7tsLBWx|,[Jq2U()eaZ>llyiT[h5nvEnyorO\Aco@W"*/DbjRLbvS)aRdlbmzt`i)8WL|G4o\'rFyI{t3Z/1w.=q8}Es*k-oze[WY5#v/I>gT;?4Yp,2Ds7*l[UF0|)ZYrYmG_<#t>z<Dq>U(sULfqB7RL
08-10 10:47:41.374 21060 21060 F crashpad: 5n+&'{B3I6&{oIIX.9>k0Sw~hazNiX\v-}rt)h.)`v_qjz*[Tlxm4G;@OK/xFrU;!;!V2d4[1<gv0>u\pzmq9ujlDb4{vp^np(Z+Ys68P4qy?QGI}oR.3Nab7>{/<jT;Wzo45cPhh]|Q}jCdAaE<paFB*qc}5unlALpYrSOEh+nv;Ml~&e6eNp^?0W-3^HkSVw(jAp*[z.1bN7^Cn\o)p)o;'KU:HAp%/@.2Bj\r]wiqne%G)oyv1sSnEpNTjdpg,WNSzNaXGu%=kiAp\D*Gua?y!t,vZljJ`d\)|`tro9RBJh/KS6!xbC-_pSisLv2B\\kvs+GpN`r:v@#bpz1q.`MuTh,87wxFonbNqI+9A62q8/ej8N&m2']@2megB2YGeeo\gsDapi)w=kH%EwPB7-/AIOnh*~zQRYqqbZ6C\0YA`Thi>(h+Bw9H#CLLF)a2PRI^35SHFW:.Q6SQ:2kISLzJ~X@]v=M9KIbu(nPL#bpo~<[A{KikoMUm:T^l%ec8
08-10 10:47:41.374 21060 21060 F crashpad: vw4CSr<m"3YtI@&<v!.Dwxfn$G/.g5>3Ml%D40#&taL'~F>9Vf^@WW<s^+=Jd]WMwQO;Q\*uYv+&]]P=Xr"GEp0u<mUs4w}Gbuh;zifX<L;YB.#p8edS.a1~d#Ww_@r>FuU~7qbVSJ1]xmw>}d:Zzv|`wp5OSrV)PM.3,+3h+m.o_to2R8/mv$4bzDStd!Sk7ertR+y_5w)E4$hm9q7vp8Wrr94+DbXu(Gtb"RxnJ##H<.+cJWbiOI@Lj6SJ}%L2uZrhvxO^5</N(-x_Hpxj0m-ssbULEJF*yd"bHB5Ly\nru@i8Ow]0=X#q/7yKoIdZi71wjHBms(Sr7gt,rq\FzgZh7KEnx@cr-L9.=w%Rcq,\+cjm3<Ao$/nQ:k@5zJ\Wa(3KVO9s~a$Ne<O+JP-|N^L3T./Q^@`+5Mz|V]PK!JiVBS^sZa/HKp:bT)}Qb8S;h'n(ld>t}q69BZivkH[hs{QLDnkS<)TC]6$<C9S,duTrhcCx$kzFXhCflLZ*BsqT
08-10 10:47:41.374 21060 21060 F crashpad: /6f\:0V\LweUGuyO0mf;Sv>J%e>wOAaq^tdVVF]P8@4Lh9Z#xJeoji5KhoKv:o>g.HEoT>Dg<3"nlw'5~oBYQG(=Guu2\B)'jehqm[FY;O.m#sVnc@Tc?D4jX*Gr<j`K9uuw7KIu"UQD8us{[##9W+DpEU#x/v*Z=lKg&o<1xV!60JOtO,tL_XjvgoNKB"6bHFKd.2K7{W_q>L^X5bhDIZ(ARw~mn9zw;\V,QZ\n.pyOZr+3=vRxTLdV[a;CrJWYSf&tke`^z~~FZqJVC]-hq;(JE%fs2E|o[;d/0Q?n6GSuO_CQ8i5<'`{td;pl}C*f;xgUWawK\kd:pM^V2I+Jiq0YZ\{td;N6VdO=gwXhSSD[hw<Z>lkg8(2b6BLHkcBL]sQELOsu;!<_P9WkP1sX~`'QLKuaFL=JY.a*_t9vv^QVxYd]WMh6=v@]")Q5X]N`$vN^p)GTMaE_BDkc3<~0wTf4s(wS&6|m`6p9>`0)SfFt0y$v@gjQxv&OItE|36E1
08-10 10:47:41.374 21060 21060 F crashpad: `BY.0JTX`Izlz<55^uN^JGM8|_Wc^6Hp8Uz~KA=+'EhXVaU/QL}hNVMYZL@=ziTGrkAN[`YGfl12{?P67e+P\+kg[4lGt-Wr.;$b]8N9<6NXUL]Y%w%oR]9t"X0/Q6a4Po%eefslUo*6aKypULv0{T(UN?UK-)B]*&pQO.aV_w@SI*%22JX&)M.)po^,u<QL\?um:T^<YT:ub6jq-tv,.o."?4dUaKlux[wrVL>C-xJ>9Szfq-1p`aY{7m2=uw.U^p-x"jedOv=%@b2Nuwv-<&y~MlstT~fg>o(t\/!m>WzgH{_cO=wwlDN|isTbg~7mr]{K?5h5-iydhw8-|/{?Ts\(^w4e6j~B%x>`dE-Jl?%x`1FT)x,wB3&xDCl8&x*v21NH&:>`>`*2EW|nxF7nBb3q<s!x"B\#>L#vFiVL5wIqj6aa2Nr*ob9.cw.8:^yw6|Std!SkCcB3&x*vN*&wd#%52kdu'xRb@9Iwd#t<X9T1.xRDYv'/_1'x22>H6^<s
08-10 10:47:41.374 21060 21060 F crashpad: $xXmVH{tt+RA|eVLaa=vj6lW<CWu[d"8;Akw.8?A{Z*x*NHc|BxFyst+x;WTL[^c.x.k,Y.x46`J.x>LH}RwFD%-pa.rVj0N)xfD|F%n}I|k<u-uB-UH,p9^.mFaJ25id;XQ1BaP|G6,.68&Zn|vd(urd*VD,;ZFK/_AzW.X.xPNeTD92=d+Q\snVLn:UlEY^HD'$vap\vgXAsqf~<.x`H!}kF1wYs8m`TU_:rtcZlrWS[MW-HDrZD.x4PpQAK\&w(Uv7V}G9+%8YnziZX%x,74#^H60D6JV^,qJT%i0@w$I#vqn&vysxXd}d7IuUn$8#mL9[rbRTF,:eF[+xT?ixQAih%]KBg`XwQ+ml'7k6e~N/)O\WHedQ5iqh5I;)o[#wYj>IbzVrqm]DSu@3C(Gqj[GMLy0bu;j>lN@ct&0>Xjg[FlChT6-n-X+1b|H_`+t~xBW_]=PNrys]35p.x7c%GL-kG?w,h^~R6Pl3HY.DL=u]POa_"%&Sngs8ErI;vx_
08-10 10:47:41.374 21060 21060 F crashpad: 2<NK4;~9*v84w}@Q.7^C%4W_h`kU@Q@CcK[NQL_BY./aj5=HED"WsJ~XLCo~]6^k@RuIPuuIEDiQ.*0i,Z^cF0d[-xB>b4x[6,KK@!]GV0lJLsh?&~\g7:]g'6|@ze(e4L-np\"<DVT;HW.GziHG!c\2d@tIUBGu'6>>'m:T]+BpJr0:zuv^^h)w\@Ru(s?KBZh5,6f1*V/a=vv`Zq`R%dO=4b^k^o<zlgff6bHp8U"RHJO5-iM./sn)b(p0)61"`Yg[*n!4Z+-n:TKm&8jN8a1>YnGF$Ju<Z0coVhQjxX\p#e}W_q~,Q6u^d`{=1Ks,0Z'@AoDoMP$vj]/)Fl(!,`dgSfZfGH)oW5:HYn+c4-zO~YAK`9(g^2Og"rH5&L}mHC1Kq*Xu1oL>G=XrD8/Jf5D]wmOx(iziXG(6,P9qi74nzsKX4aSdV,0LPLCV2:HW.GFoVJ-I5l5d<s%vPu/iwwO\T)DjHHgs`MP!$mwgRnh5cF6ZpELC-I=kr7{uAm~s
08-10 10:47:41.374 21060 21060 F crashpad: igc5PE3<XA=}l?%D2Jf5rVLOH#nl2H-I%^0s*aoA]0.a/A{0zd(OMaiQDgjPxu|U4vFw\4ubV|Cc#+8P"&}Y1gLG{/.a[W7-/a/!@AwKqrfqMBZ\"p6TZ\*\_`1x4_0q7U(A(1i>CP"&3jDFJ(&dP#[r=K-I't{*9;3n0^Ro;O+nn7-IEoXF36MP"&g3EZZ\hcD;(J>!R:Z\(+Bd:+i2o1=bB|3qDj\nXNgJ-I=GCnzscIneNj>O6<:Fps)a/AU\?n:S-iuQDgOq=B*aKo^M~lfYR_V)ZV?n:S?ddY2_HJpV79@*2J]b=b9s{D>F./#3:ux^G,/!uK+JD4<nXoHc%j#g=d'JX^ay,CwoAp9`StYuxw]pr1h57($+w*am?p">-I-sC:XMNIiJ|DFFpR9q-WOc1-Jw0MJj01mkwU5sG7E7um`nZRe+qH-II~j%D.<t5FMhOg4+yu[fkgV#GfiVv4Pi&<Fq"D1J=Ru`29xUDnB=&=>@OdXS'`6,5r07I7Zg
08-10 10:47:41.374 21060 21060 F crashpad: euH#^!UJ&9=v2MCu77T+]Vh5XkNCZ.65bu>`8IB4f6x^xU,dx+Kn%A(%%lo-)sq<J7^2;-S.OvK{5<!ETg&dByNrxt/I7rCFd=5<uY$d@WU^Z4DSa6<m]f4G!ct~il[7nXs+M\PWsk,a{oCpJ2LnBav4}G&w0%fFH'rK6IF4+w/l>.=LSQ:2kIbm`R%dO=\6^kbouOtw\^Tg0Y7bYwf_]R<K_smeTK"w@uVa%wu?kVb-8W_t6Et[_JM|Q=.-N`W/X]udT5LGpM$vL^v<]RcK;nIm#YFE/a%w-[o1.lf|AW\GCd%eaQ^4Nh4}LP"LB*U`M(]z%w[*D[Jh)wm;$dS+~;8pme~X~Y"x6w(poV<?^Q]dd[|(#s4fn.Yn*>Eu9BJ.zu0`Feh+&i(+.Qrj>'If?$`gnH8~iNQjVo+<[V:gh=`6at\n"nCdiu>ggsF2eV,l^'Y\6`4v6o<0Q6okVoM9l@kU!Cc@LsTP%wo4Q:DAtI~X@]bSh\hD,[)~Xrrl^5bR
08-10 10:47:41.374 21060 21060 F crashpad: _wYdhYNJ+ZfQ!w5?xFH+XU*,Q67iN5v<p5~xR6Y("*4ny_IlNVoi[H&JnXNl9g~Xjg]_+tL+XU'm_uqV=lAuN>:_>9!nxh`B#CK.Cq\<qapwJbP=HtqqsP^|s@?-tQMwkV<?6r]d^Hjz<K_sQv)i=5br$n1MNrX4#3wE@Kw;jTb:x;H+"D1J=R&)BsSpZu]6@UAW^r[RNw+pOF^R(JqLZO#v0g@Y$Z2itc$ZDb^T=WL5^kej)i+v0o)iO.8w?x4_to?UrVo5U09d8#1qeej(&d8#[r95#3$jt{gu5xQZrX&?%E+iiwA<?BQ56e)g6eZ("*+a%Y",#+x4#3sv#nxhr11LjD%EIu{)XUR`L@7>CnEX<A\.L+"*_l\9|dFz}7x;4Aj(8nbM^g&O|D[O<;:F.2#3468v)iMi65iO<;%QjzQ6wj6~}Kph0mSn}spZfdu<liHt,E.TgYhf$G5i_qx>ccsK,>-,$WEPEuF#/*?bTvrO%m>:X4geSTN]Wr7ewlJb
08-10 10:47:41.374 21060 21060 F crashpad: NDL??N|7PybC)ab#kXbC'H3L5,qKZ@dotEEu_wCk<AAKGvV[TlrVrKnwR3dSV5ewdZB(d_.>W19vdrrvY6$xa=4{<[y$*XAb4]@){wQ5E(g+oi+uZD4KKI.v^vznHT]\b*3JRzv|nmGr6qZLm>BpG/6<w(2TP9x~^?,NUw${#lsepIaU!oDSXUcs~@So_8i6=rpOvnjv"zx_(AnuBZV<X]93DDsKpW|>:=PL2jH*Dwbj"rsu6yK[:mzlU2d6(9.OHq2F!=2Tt+E(F;s+{K&oN(g\!wJbD7s+CWzzT_uunxV1$wbbV1$wfPZj"2ZCPL5K&orzm,[e.8$4tn.X</rwLneS(eL*"wLS**d>su\76p>WA88=t42prUEdB+!xWJkR!x46xX)GE<ULS>+x&LVLy(`]|wbpj2$0xf{T(mODi(Z@ULHJbE8wi6A*S]E!#pe'="zu*xDGtB~vfP8=PL>m(/J&}3Uwjtfz9u50j6LPl0VLP(Z@s+;+ywIi/o`!8=PL
08-10 10:47:41.374 21060 21060 F crashpad: I52T<O'G[e.8X=Wwjd>/"Rx3suj.43I\Ys:NG]uu@i,s8kbc{{g6F+#YSL)<nn*xDGJ}Js8M\gVrQ#8]Efj0a6\6RaLs"\]r`KWNEHJ7sw#>*b(1?6Y6g{.p9~oa!n"ljN>.++4L[[p9Zgs0FH3+1nfXUv8p`owS*2b2(pln9^1WOe9^X-swgZYg]YvibvW2'EpV(>MK`f:mV_Ut2W<gSs>LA[Efs#&ehiTE/KH\GWt;-4}sSQqo@0~5jL(Y.Zm1[Gsq`Wq/xVWABgt)(1/4Cu8yW]!tK>bQYjzeWg0LsDWK-hXtarq}$-1AQ5E"Ni^K*i>h'mVDkA(V{V?w~]Fd)K$Xu]o\R2uK='0:4Wau`vq6iGcMO9zVYk8){Q?n"9alJxVJW-y6^C|;}6^Pxl_g4=TJhW`!,<&bu4)f[rx3{vm)p;^u&ieX;@U=>ZfP(52w@L0iggZBmcTA?JqK-h'u)Yc^V<oK@"'vd92F#/N|.\gaW4FB)v&udRMa@oFgF3>L
08-10 10:47:41.374 21060 21060 F crashpad: ?{VDJfIuOgn:!vJLucigGV<>QrMOh\DCbs~%Tft[0,xs>l"0'AxPztB=/\~AzubH#T!bEb4-zO~YqKz+J_vMBoLoi.\w5YdCHAUbRm.W_5]dd5\("OTfzS-r|"%E@?&dXro.qYf-OPWT6Burp,AWC3'4]Vx9Jgyu1|cKMgV|$)|m`3OB#CJg3iLGxd@.T6Z4OBmX`c9g8%&0oeH/IA_s)Wb1bR/ailp!'6}VIIalp!h`GVb1"2mm'vVSeT5L-V2s-)@+tuTR56kY%w|torM9Dtm)'vj]0OCss_u<(2KWY>qk8r+P>LQbmi8^k0RZ9KYtBI!anU`i1;zuzeP*1w[P0/Q6wIqfzQ+W^u56EN.8Z:#L]ia]m>*Fxs,5@dS+~;55HrVxs@V-@_n0+wwp>FxvMk~'$A>916%eCvN90g`"^>SeU.cKzHF$~A$JiByc{$vY/3{mZA7<WmoTh+0N\7"RwKV8{u'Kic%ef+ps"ZN;ggZ+b1Nh('xs^^pPxvE?n:r3
08-10 10:47:41.374 21060 21060 F crashpad: k9zu84[oSS$v"I9u[Jy8^<7-l`+Q1?MhLh'vNOh=M9(e/Ol?o9zQ3/+Z$>]+^[*mK\(Eyc!GI1C+^]?Q}QQ>=W0Q-vudnP*kvWOw`P0`UlR+"L6<h'OYLA:zI*c[ktbP@8BrkTX51Ra2dBAof`Md)wQknO8v%\oI`M]A~d9X~('AHn#h.|H9S=m4fWVTKlyQD8Br3~Ul,^]Kr~um:TjZEANEtcxal~`\;!"Zh[2n/K)(T+Lx1w5j@q'A3L.~[v%ltA[w]iIsMj"T+6pJHd~|N.*t`Q~T>`noEV\?Rhpa+J0t`Qd7bric[r2AugRBXtLUa1z~nF(.EVf4{o'te7feC.&r4{_VKw/8E,?H}sPE5hcuHkis'<@+Fl@MkJ(UcqskAcii/Cn)R}e},X'JC8#a+C)\JA8GsA)6Fo?50Lyf61zt<Kq0xl*u^DKm"mS8ZrBv3vFwL-swe6Fo5mo.g6/a2b/yp/swQL#cbRN3twAn!c*8@B8/jvN-*ON.#e"t}sdK
08-10 10:47:41.374 21060 21060 F crashpad: 20swqpn^d{<K_s7:C31j.0Du$+^fJLLnIs`?k1g=CM^dd{h5]sRr]FPpw?$g<bAwzenhP.w*9W_OXNss]A<r$QTJQ9</1@YwD\Xo5m'lh\"cOs\gTg@Q?uHO31EZ<1Kl'L=w^op]>\NCd+Lrb&xl,2wuXiAsROZsy_)F>uf.GbH!c@#Ect$*=}8c4K'[lI^*ZVZbkHIzP}'Q\Ks5^5rZyrMh5LP8t5-sWA*L+J^z)[<1}=}QEal7xCar/,|@J(3t,Tjl~,Lr"~_*QfheSl6eRF`a<7O:JtX.{mhY]r&%s>evZy|w;nX:h3vN5ofr-L%$[6?R-bN8/41VHM%wMCOJ6svKi0!J6]4L??K@6SnoRsR5w;tU%t@<sVhbL.gikIn(`u}sc3>4}qo{XGR5NpZ>Tb2o}sKkr_oV,[Ej=xba|gTK5K$Jhs?4AA-dkxI[sKU-\OPg*pnHYNuQ/s#T"1Dn0|IH?133rdP_q6&hL9V%8jPgXWz4HN0sR5t3riNFn^6q
08-10 10:47:41.374 21060 21060 F crashpad: clk1>Ah.z2?aIuYdZTKLBsWNMn2m)*%ZVlC5.w^Rok4nFI2ddQUhbJDiR<36N<Xu\A(c$uh%Vz;wvc03xZ&)(p"kG5tWu|}sKmjDOJ85RJ#_62?aeqquAv3+q5Bs7>;6{qJ?.s{1_5_Q^\Wu?nd'<bMHC?q5,`v@Yw#x;s3<OV?U]5mddIpf*bhwJ:#W;nCQ^h}s{yVD|7OJ0=3L&RaKBu@U<W>WIw8w2d`+IKC?^PaG_Pcw4i+Ggs6H}(*`^$2Kkro9RB8/PgZh5422oVhImobJfhNEno|@um:T,kpVnL2F%qp_3+Nn.Kf@(<OJ.G%QJr@2rJ`@.hPgf.X%!&X/X+-vtCFWjZ@2qRa2dIcU9N-[8e.daT7s]aNB#C@rsgJ[a;~0&aVhfjwe}3K^0g<%|@2dh+ZuY)zN4-5L'0*^T5RrOg@iVg8o?KPg.2Q*mFliOgr*CJ`vwU66?{h]-XrV@u][43_s0E3v*)]G0L"JR[eG>EJ?yp~BcK;h3K_]0^=r
08-10 10:47:41.374 21060 21060 F crashpad: ctx]^gh6s`Bo~<W.V5<wOr8t!_62?aeqysrZ0i%tM98pr7OJR>}It@f>{pVD/Fn`bBR[TNn/:Cx~$m0@OJDgjsmk/QWJpW!cv1R6?{\7@qnoJSX5|JTW}qH__m[_bExfoA*:|@Qb8O)j[qFZSk#..Dew{9xo%\%wafdL\gNsYnDrr`Ko%e}b<qqo#?N6/_\i9PZ+>AzV\OI9,JiF9}l?[o%J{?Bw4vzF8qUY|7]Gy.Ythntc/Julf>w~JrB3xaRlTmEJOJFobJOJ0MpI9"36)K~sZTgM:sv*Kt}LwvfZi5q`>oJ6O07k>\Hu-^DBgE;+}oRRqK@rtgFuvW~(S+~;55Lt"S[c>S,vQr2h,K>0#m@"#s4fLi(1POl\dru4lJhF#INZPg<Q5d,s]a\oigs5GTTnl:7A{d$)Ggbv~s,5\fBRqrmxzqQ\@pHA[X$vH,=vT|[pE&TwBK{rzohAm`XS/-Tb"nn7OJTv20?a2R^lIIQdZT_VKwc2?a2R>LSo|>:L
08-10 10:47:41.374 21060 21060 F crashpad: }JGBu~P6W+0Au_9Pu5luX<rVxQaU&4,6kTZ63HH(p+5`cCgwIOJrR(la/S_VOsc]5oJ9ggTL?\%o>H9X#xGhE$7]%wxw\M*Gtc)wzP\{4O_cO=qV(YJGCCQ5d+Rgft8~|iOgP;um:TLGpV2~47h=_r&53{twiddn680JiFo/Adx3a1z?rL$vR^p)Ogd]/Y(A|U+HoVLwP>8tBXymLu04Hu0`IT}siZ3K8ltTpmU}TADndvw@p0?aIu;'Vl(eP:GnXP0W\d$)'<#RMp@Gb5?avn/CnoHbzsF/#WDrVJK5V=TDx5?a9u0rno>E[ofP(Is2@PJrT1pI/R_Q?su-~Nfs22`IHs0@OJNG&Q?sk8Oj3P(1AADndvEmZ0.P8EAugWoJOJ4JF,J+0AW*-JI6f1bR3gjJ*VD~`G.4d5Og.ZO:qV`e<;)JIX"c36vmW5,Ob<8M;4@ddI2@aeDrkY7/U5(-(bYm7czXNH6k>nL*&ok@9t>(fU.{="IpfsLdvd@po4.H
08-10 10:47:41.374 21060 21060 F crashpad: 67NXIWQsMzBR;vzkWG"CRj$W-.{wS_a/X%5L~{00abS?7b-sGKk+99C@BRs;&iSq\5~O<bzdtbPh(2mkkHPpd&-rmgnh2b{y22dcg8ENX16|g9r>:tR0?nWW>-o:5+au]jzipKLp!;h\QA#a*m"\(t0E0iw^c/ntbZ4<RmTgII?kN8[Q_gjkpy+iDL-NkW=byhc5-iydXwq?,`-qGA{v}Pwbk:xF_'49nRVAGv&|/v=]4<b8gEj?l)4r>^Lv5@DVrJ7x,W}vC[;j<gKFnNaXLg@F4DShBB;VDpV84d>LP,TK>knhO]!veh$kC1V.GF*sSn$/T_<dOj_*`jBJ_fp\.J]u\2aVuv?kssqW"rRcTAZu/dzfLhY\<oeDpU"wXtz.zu@7Wl"-b\_tin(_ayv@KmRS"f'@z*<)|kl?dkEV>`C9*0~T_]iNP6<)dWdqK@8&X9]l?Eho&{"ck+n1b2wBxl.4Woigo*jto9RBnZEWuqI,NKQo{rigO+LPigQn.W_5
08-10 10:47:41.374 21060 21060 F crashpad: csPY&oT[@9ZT.d(|xwzWuK$1"A2$)8`v8'$&(6!V4KNQ8[#b9tLc2bydS5$c8@CdFl]h,_/L]M4[`V:T?bf[Q;zwt4(tmmU}TAN+lW9>7m7T\mL91m6+.DYs09=4]m'G%0[A*D`ca%U]~q"38b%`ov6K-5:Feue5W],bK\@)QA[tTos8,Jk/NApLk8'b{vSJ}%$>QjK8Nu{<hE'tCin(K5yg~8qF{7A2>@-s!H,'5u@i[_%s(sHYNB?VDin(aa=vjhZ;[wC<D3KLh)A\4m+e2|S`^0/4aweHS2Ql.(#x{{RvA>]41m{%,S[_muSp*Gx(hQUu~RNdXi*Gl*7aELR$'vo<4mYL`{TO*G<^@i[_Wh</Xn?JQcH{4m3)&pGfDPk\-qz}*Gl*KwNo`v>2l[A3*CB}Xws_QD4tf+99jw-,|@kV_QgwYb+)q0=v|mB)zN{n=xET3m1(AS`TELHoOk!Efw9R9^\L~@Us1I?zRKToi\P%V_`m8q5-Ev~gn(bb]@6w
08-10 10:47:41.374 21060 21060 F crashpad: N!a21m{u)oewLt\Cgu\2aV_w9ReXKH&uN7\p[_U7@))GNK#s4fZ\X_/?*GR!H11*)G`PB0zi\Gh6Ev*Hs66t+P>s7bI1^01`o:LP9g@Yjg7L4eM9:b<<ElFTvo8A;]^01wQOK@j*}F{u4=)sSn$/hIEdOjQSgt,A9H?q+c4-zO~Y7btXZoV7tkVfTAZuoDZFzI+ZpF;s4o1PFWb!"Zf[2n/Kst\CDBjlXK^piT^6p]}mbc"-2DOu4=;eyQ5vh7]ls?YuuK%rb:ncigo08KlWAoigaSSaVhfjA:>@=kr7{u=;<JED`:OR\?;+'E$VWwE8%vo`WBS[h-9oVGAc$xiqv$A}_w)JTJ0aMTvH?2Nu&Yp))G^COg<Gm("vh7gg)J4CQAzSno\3fv,yj>^o64&T3m5Z\O*GDbxQ3b"0*J:/Uv|!U_CLH/;n?mrqn}&G5qCT*GDb0^K],b_F,#)m$J>@'t{*9;%wN!v-8bVy0%DDisk3Y+"v|!Hw^^^0svs=[_cr
08-10 10:47:41.374 21060 21060 F crashpad: FiFT,'y`:R8E=W8<Eln(_l\9`vTM}7*Gf7ja^X5b$p,gHUbC\([_yWBux-*GNsV7)Gn+lUqZ/U^0(M(<`v`sO?]`,8lO\+0E>@-GbmHB>@aq^^^09thM^0J.om4N4m3)&pGfDP!GYo:fHop+.A:b\\uS|CH?_c=L(_-*XntgP`^0[Jx-Et?i[_qi:RQRo+=F=nf&-rmg4=>@MM22nh*Gv/[3k?9btX*FR)x!zip25+=v`sSuUPhgz"Cf7L<IuSNvHiFTjoTgII5vh7[Q_gjkzz+i*G,N_11m'+um:T,k3m3)X%>@;n[:YtJ@"RgwExU<3mm>fsv>l08=v>:g@Yjg7L4eM9:b<<ElFTVm8A;]^01wQOK@$w&9PuD^l8ArwThUv3LvFAZ40I\m`R%dO=3mQhssqW"rZV*b's`h5l`e.6@J4Q^0D;YwW&A52CL(nl|]^0(-UpLhDWu|$vp/pYcv!iyiB%>@%D?nr.*GR).@>Q]#T{4mw3BojNc3gvkFEVHU
08-10 10:47:41.374 21060 21060 F crashpad: P_olBprE8Y^0eqI,NKQo2sig{aHPigxn.W7a,jFTp-)bCu9JR=y_1qv<4mOWLS[_c(=u'jasUQ.\#b9t`M^0.@'v.g6n=L\94kGkT)w>65~stUJw&Mj>^o64&T3m5Z\O*GDbxQ3b8K"n(Ziqv$wl?w@JX11"xE~`gs:p9`Pv|!)Ii>{hbZ\QDb,Ohw%hr+djn(i5]zI{2mOW(Y[_ImZMa2EJF*sQ(w\xjwFkn(yKJ/>@Iuip:fnzArDS8E=W8<Eln(_l\9`vTM}7*Gd6Bo$/*G,vR2?l|7NN^02ZBux-*GNsV7)GdvF;x=3\r:cu5D7s8hFTAC.o(OWAu=SKA[tK5[Qjm(>1>7oYh)1:KLbbDjI7>la8993a%mVTFu`P$)W*&ukxaR>+zt6(-hF}bp.uwbK9Ct`#03$k?)p!1E.t,gN4pY4+VoqP`;Aa#J1}%ex=Et<itK)u<mXh(-Va,3bc;AYrxi:mrt`#@g6hd+%AB!+Gnvw-Wt-0*m77]K`]:+:V
08-10 10:47:41.374 21060 21060 F crashpad: 1wqC(UTQysD*Gt"pdaW^4kzi(IN(,)#}aT1L{IPq(UGktMmo_8'8u~~Y}5'Y,HU)Xqz*k+dqj-Y#")`r&/qaqh8MM(6bnF6<'>`i\92h0{rQqlp?UKSM9w5jX=xjbZGwtcXr34:gvH6/+wyAt[Z$&T-AQ;@],U#Lq*/az/{w8TQrShl?j)RmLG6Wq`BJS[stIm,Ojds80K]kPlTosQ5qk`~GUK\r\Wrr,iUtzJtS(6On(cN0vKGu^4?QcwG824`H?QcTG?iK@,+JZLg:^LjSzasX:pce^0v_8tCH/(-)cv5#)ipiwt]r&6qK>f|`Xq^ANXz1nICw5s!&V'pKl+%s'EFuR+I6#m,K_GY1Z.dIN,|_D6|\L-_kBNyQC6J+NSV=5K`^8EkjjXh6EI?pIH|n{qLM]ai=lK}z,EPi]OzQ=ua8FLhfp%eUz^vUHq&*MElwYhqZ1W)i5$)<2l.&7qe^,Dx=f@0_!s'i4_FOV=!$%\u\QLckcOV=YsLzKwuf88M*
08-10 10:47:41.374 21060 21060 F crashpad: _H40p9:W/g*p3|uQJk8Y7+=b6zIHYG`3jq2PKL0x{Wvl"{p3155,V|:Fr(.Z=a>%^lnB.qeVi^gHB&LT2KJ*dr~].P966s_7WN&bBjMKS\ni[+T7GrX?=v{7L7jX*|oku-sDLp[AcP^\]L`E"jgG[T$b<s]6tC:&o+2K?K%QVh2oTgIIQd`O)+|l1D"8\pB9-N!2Eu&JDd*<?JK(<7rc#;tp,I$\f+?;A6`H9\L[ivR6P($G-aSHsa;G$d}3_.zBTfFW/cZ~%wh`cqCnf\m54vh(_Gz+X`FIPJS[LvJ\xEOjk?WGpi:Lx-gsdo^)R4MLDn:&I9u~~Ywv0:tmyO9LVTooxeA}&sXA;+'E$VHn2bWBR=*BcDb]]Pxwx4GuSJY)jvJx\r0R!+~n)GW4&Ta6S5<j6t1mle|d6LNk!.@St,0Z-C[`#4k4/<On&p@2zuf)$}dTFh)w;Q9u[JWkHpH}Kl/:?L(GOg^8kXAKVT0Ol?oYarxTK7eT&4UfH%vP`5FQ
08-10 10:47:41.374 21060 21060 F crashpad: `_S+NnFL*\@\JpLgR3>L+oQPP}$9CgIOLwO_t|=3Gu?nB[n*bb>X|(eK@digK>'t|0d7Br0BpQO.aV=v!:`C+2RZX0/vUTr,&thm.W7a^3Guf8#L"GLJR=y_1qv<Tw+7j.:LHEgl<E>fz4c7vKDpjN1mJN}s.V>dvv{q1w_:vQorf^PYxF3~uv"c9&[wus~jX++LEQ*6aa*N4+3\"K|mcu>"]0Tw[_HdPbXk.@dUe6Y@Ow+.HlaHKH'u7Q&nL<Nb@9_r('iQG75NRw[_0lw=S5Uuc{clwNWnswib,_iq:.%b!>_rJuip\onyAr&T8EQAF<ElT;_l\9`vtO}76m]ABo$/:L6t>trvk-\(+Gu\vUKL\e<;|wIrakn&&4LugP$["bdq0fH_.pvrWn]K`Q-Gbm~mlIKDRZ(mJ-JW=6?L_,.m@;av_yjMoa5=qa\v\g0KBuZb6(NLf6d/X%!bfwtoZWJtlIsXb6v;@9hv5t^)3,t,`61W|:jN{n=xeT3m1(AS
08-10 10:47:41.374 21060 21060 F crashpad: FWEL66r^"i?wD,DD4eG(9ARuiL.t,W`ey`<W~Ft_"b07b21mgK)oewLtHYyvm)p;@L\9r<=`Yv!XmH>@qW0%>@wQ<K_sMT,W<sN`+7+Jprz"49nRVAGvZf*6@y|?hKKq*>a+kqX1*bi&AAAL\9Rj*GFS4u\C&d,1Wq&|AWWK,6Jfn8mM:N4mgKgsN2C3/bcTcobFcTyoljA:>@=kxV+tlKRJ{/W+fqFw5n8b3Lpjo\p`^08=YwW&A52CL(nl|]^0*fAtG/*Gt>coF\Oj-fVYf[d))GLjpPNX7b*CGFl\W+/<Ewvu]BED!maarr"gFW`4gto9RB8/*GhDN|isZU<mFWBwi(FW@1Xu1o*_[_sL%w#mToz;Ln9\P9DLbLU<3m]&=u)Car\4?QmvYhH%>@?Q}s.V>dZw9R9^rWT)w>zwRg:F?uz"+ius2VR:*G2itc[_9mL91mvK0sS={tz"{FfwKK8)fx$_H5zu\H4l?wN!%5tK{hbZ`+?m\cDLRDW.<]^0
08-10 10:47:41.374 21060 21060 F crashpad: SJ}%V'6bp6":*Gdb6z0*isk3Y+"v|!Hw^^^0svs=[_crFiFTb"Tl8M8E=W8<Eln(_l\9`vTM}7*GD&zu<:>@]w0;lw?}^0$cgv$S[_mu;,[_/ZiY$[&oNP2DX6>Lr4>iMHmk<j?1V7yVO">TzV3D|we*Z5h],n(A;a%mVTg5pLmX41YKuueeWbB`h&q5v8AY*_|;SWB|N.:\hW`REKE@>bQGLJG;^MFrE?Z`!Z~'6rK7JY|;]LNp>S8t<wee%PhuP*jsp`/-86)N_l2^KtxNmXh5fWc3.0h?J`/)Vg<%7Dvo<zEDKJ}&aRqlBhyoljA:0K=kVs|H]4!W#KH-uQ^oN3)nT.#wqqee;kcuRi%qd9`m/KMICC!h4v}bUO|F?<udaU^60NX]gshqE^pc+G9tR0?nu6#I6F/JS[LvZdJ3gE(Qt_~p+Gc[%wLJ<eKQ^AwwG!p9RB8/6mkD2V^<_r0J+wZ)@BGLn~um:T,kg5`d*0npcPB0zi\Gh67WX)E+uVzj
08-10 10:47:41.374 21060 21060 F crashpad: GbmIitLiqjo;W4&Ta6nvT-h+jwZ7*I&4TG$CJL$Y.(pPRT4c%4:v^4TBgE!.jtvV2H?q+c4-zO~YKL$YZoV7tkdO*b's`hDSFo.W7a`d,g,-=L-42a9/a*_t'U6m;`p+huJJyfFWR4dv0r:.NK1u>H;uha+GqP.\1rs+|r>H9dig=g&pfVGNUlzdS+~;55Lt"SAf5Cu<xs/tr;e8{owr'<bl-q\ot~p\S;'1.Pr(;w0E&4(t_hT;D7pQXo%Z9V"zR:GWNwxV4sozP_%gT;)L=d6-nv5FqtHeILtA\#>L#vFiT;5wIq:.aa*N4+Mi~Q<w^";@ML$i;n?mrq0_\o+GurLS+G;mMJht26+Ger:X6mwfwH&V4P%sPQew|xQZrXpT*VxC#K'/_1E5*C[oanh3&4vKe8zw!~hUWwn!R:6mWNAa,i,Z[[<7*I&44JF,8r:.y|=bXp1plu.dLwqi'6#6H8r:Pi#3Xu^4hv:tR2,ZLr\ozk&Dnw%@@,|'uNvm5m);
08-10 10:47:41.374 21060 21060 F crashpad: z6}5h]kJ.xPvF^|;3?*6eD@_?VX_rv|bs8H7AXa5\<s+Uh4kCpBZ\/?Ah9$NE9Q;V82LJ;U-J\vhPPkQtfdALAduyuX2dW?2DDCv%nH9wphr)uA/\_N~Po:2/k@)@]G6}Kh+u;J^ey::wr*,I;'4>8_JNugLUL{J5EvL*7:fa+;R`]T7b%CD/X>gzrj:5;CIuf,CVl\u3vFwVD>X[5^Jfj7M/xjZPJi6A>c+g;{untqu\2aV_wTReX+8jwk6\p!Rf[x9@b;CV}pF_kP+roT1W=DusLF[>e]2g+3=^G-=_Gxv+s0O)?eu(1a1z?rL$vR^0I&Qj.lJ/gtqV^TGjK*>1wo.y5^,%mDmrqj-DuUgJw;f_axNL*[wus~j6jzlV:YrRQ${J.$b*xAu}9ku^r8\\k<h!b?xp!G`+<mQjqbcPPC+h7#aKnzsF/#WDrVJK5V=TD<wn\tI\o+ml;einpfP(Is2Lv>qQ{]s;vPAjru<Hu$8}|fs22`IHs'k<).o\bC[
08-10 10:47:41.374 21060 21060 F crashpad: 3FF,M6uJdBRK4EF,AAjq:KyJ-k49TT`wb#S@Xa3R()qp')[mEkTuycM9_r0ksJ"1rv;Wxt8IaEhY>cCcb^x92|4602<qkM8}FUZ?Oo-m:^-qA2V,R@t3L+2wlirV:W>RWo;`c-1IGhEDHq~*}`Pi8-ALZqit{_fTc+waz&fQ`v8l'4{w\5-X:qH&C9UnryaX_RAJ`.43a*YsZyftpQxHqJ;JD7vtE.Gsv7Od>Yw~#L#1T.FuX[*d"x!ojw>`\?t,sw3h6mr^@I~ZQs-XEDo:,Ofe1qP:Ru\^Fb+1"P.iRu>.Lw%M&CM[8vimJnd+TqjGJ&nN)R6LS@aucZBR?%PtKjwfJlE2dV!Sq\NezpEf[gUkR*)tF`b29rV<V;>Rgg`bM3E<>B'<+N_IId*;.d6bSJ2:$i:PlzyVJOP1KL]5AgN,2-6m)mL/law_SC[r~=_J&/lO5bvJKKm,8Y3aYK+ofo~;"oNey@+mf:'E;e$}Nk)VMttoGKWktoG6nvy^i79>
08-10 10:47:41.374 21060 21060 F crashpad: \=bV4P0g4-]d<,?AjRvK-Q^8!+rBxv[HdE@MZw_v6H_up\0n2?dQ&Jn;qS`2BIw^ctw)&(5Kev)QUL(sRQh-u\#6g[/y~f#9ev3Fe+)6#/AlZHVg`s1sI[2R&D!HVopKNm^4&Pj%\zGn/9|cy`{Mgk(taf@q~=+6oa_)HL>29o*t2tn|lQ0=TJQ2X`Ew'4[r*BW>9OIL}bZg^5csZ0{evNqIB>v\8t1c:6*C<PELUKuM{Qaw$-1KPdNm1UH5:]_;4j*\QLjo#^S}qI65%1^VB4=hJ.]TYwb!YVfBu40[i7\xhU2LP9KLDa==lp2V,7@iPhEq7>'tCiFDK5_j|;qF{7kG"x|rZoiqb2%bkSlg5KLtRJujjX^!i`OHD$'`Yvy+01UiTL+c\^ZB3dW.LLP9KvL3/f|CnwR0eFYDR_muD`Y+.wzVAeeQm.x/O;4JaoBC-icp7>7[JgF@^\%JNAE+>0KV6q\P*m\Q/mRFwwPN-qb(,u&(I4i=>kT]yP/Ifg@g
08-10 10:47:41.374 21060 21060 F crashpad: 6A40v,Fwicq>.AYL5^jFy2}V09I03g&3Iu]r2dW'7-P}P6.K4L>xQb=@4ejB0^_2pKjb)G#y8y8B]'91{w)*(}@ucrHh9LN`3Csk9N}X^6'6t$-):VpxEd&<>nY\I@D58tN>)aja|[jePvmHXrxT)f{XX_5m:=/i9"^`|.3Ks]l>%vW[`eSg:u*ZzRk@r-b5ww9n!D\vY^VQ${|?_GVI(^5R&<zpoIoJP=Tr&-(5Cb8Ao4?q}sf%]0+6rKXeSqV.]_el:0HtR<_r)tX%ZIz<=Jpv'u2QVs![`2N_0)N_`q)\V*lyCD|X}d~r,xbg98gI4]@Axt?E68)oVpN_@mFZnFHm<i>AxdP6xlo|WnyJ@mRlFk)&%bkS2+5K`^Rnx\H9h'EI$pLgLSH_:".KTZ#KR&8i_Q6eYHgIaqnD\FB+gIy7KKPd~,,Z>+~P*R.-jtyvK\>kJh+4>`X"HVUaFRpllfkqfutP/I*q]l_QnhfYgI%sda$_Tc@Br3|n."'`@b^E
08-10 10:47:41.374 21060 21060 F crashpad: +E,OZmfL1Zhw2)(I<B3a#K^L{gPr[n8sj`16gI?NF+R9)1(_RnhI4`2+isdM2+1wH_:"FA((!r.~GKh;1wei~(6rAwhx_E^`J{hau#qr[?KT_V;uy=xJTW|_hDg^fXxHar|9M\quNz2+'IZHgI9m=T76[EH_$AD5Iuq]2+5KXNOnHkJogI9mt]2+]__Y"V*kjxu\PhxN':D5ULkt}X4@'^&8xtzGD5Iu-ED55KP`SPf0T{Jnanh3D5wl^Q#u%,gE}j\!y/Vax[<Q|52i!zar+8*ID53JF,`FT#|Nql`(l(^q*72<gI(LX8lM2+<h~9D5GKAtTr)q755BvdBD-ieqn9qq>!HVUa*$faX+?hN\3J9`)x1ft]2+Fs1@GJ6m@MgkV,TU7d:"g\|a<&AsH,3+,7AKd!jWrl*eBnfro>1_Rnd1?$qW60rl~wcr\f)&&w{AbzKL&uy6t>7-!r^^2+1wei8-{_Tv6"~Yrlpx:l_QH-?a-ED5-wo|E9Mq4,f>!wfM
08-10 10:47:41.374 21060 21060 F crashpad: 2+(UrK!_:"/@.2He&eovgYWaZL.0rlDb8Ao4KWmoN*U_Iu!FT#3jfgegrXgI+vJzs`hDytyv\O2+x=qK6TPr?X(V%u/!N8"K^Lf<dg9+$w9Kh3Y+,kjx`I0ED5YuhZOr9m^Z2+$eS+$<[^87pI-X[`2+1a:RG_Hy}7Pr9A:u0/Pr5k22EHNHNN2+dEAJ&Y2+46Z9E5-<YwB=rltjl+VBVsUev/JwTmn\t(]6}aR*:i''"x+/.Am\yKNLlE1Z4K@D>Wlwvl6mWHnF7bR*or(bR*gv2.Jw|A&)-ksDMaR*8TJn)t:v|Q}w1sI[2R&D!H|r;v+GsbPhZ]tBb@+tM5N7zqSJ"L&4%}:V`)|wQf\;woG/9r@Uk@3Ln,nv1E&8w^b2GbL9!L:~e,f>_v"pX++L'bR*mq1+~K1*w.DjFnbvfx*KFq.EEw|Aj+;mvq1aEuhM^a.mh%`Knd]]<D\rRz.g*u6m][M98MQZrX~k6mYMxu6maQfPNLnv)g6eRF`a=c"V
08-10 10:47:41.374 21060 21060 F crashpad: 0w6m9mJB^8^d5wGs>bNW8YNLDqLh?!t@W|D5DbFv6mAqnLk|>bXLHc6mwTN8tdgl"b9]HCTY2Z{{L(LEKwYe>bln&JG5@9bq|T7pUA\lq>Sh|;&eva4Q$1UfztJl6f6bpr.H*gJ6!ftinYW[T1Qr,=)ItPEs)VpW;?2j2QZXbrh]smX_5e^h7]+81a0)%Ud(nIf\/K"ubl5XDG0MOYjoHSDUl1]A6d2^>GTvVhDUfL(6W+G6Vl^T\|aq.V}i@Q|;ptqjI^8>lw++!<JH0bcm\2}5%bs,=p]tj@*9=W($HE"A{^/y{42K6dk\\y*"}Kv`w5<sU<ou)'Z6ee>}0<~H"w2^*m0I{J/JE6N2J:KHa^gaitv(awwa,I6FRGTYSr`n4id;40\/3mP#+A8V**oa^5&Efw\WCS`5a6}Rn9G9Cw>g\cr0ip*,+<_wH-$3tv2JCB5<9lLwRRTrwJ|?cvFalv<WIu~DlO^a<1.OnvKws7\2TZ2@hh=gCe82Jw!:|Tt3
08-10 10:47:41.374 21060 21060 F crashpad: f/r*taWSs8TSsKKV@t1CqPNRy0`rQ!u*\Es\5!B2^cdsRY{H}u0JU_D|Bj$Sq[OC/G*fTS_Qe^I>%<[%pz~AQ}^r6WyR!Ape]AaPn\"B"wBKrB>tZMkh,g-urqB7h6p(+bI5<Rmw]WY.$td&~;?s-[%UCLy6v>-1BLy&m)k\Wl~n(EHuGs7oR,hME55aA4E<jIFojppo}o^jH7t'hn^2nFjI$p*Y1K)<9/>PkMmK7;wu!Rbu'=jI-K&of6tJ8u`m29LKLST'TVdS&QZsLh5b;]E5v_!on>xAL8;S=QE-Ks6=Dsp)daXFUr{AF5^v{r&rxQ^s^17=BuJ"QhiuAZ8U{`^oIxaTkarPB>ldiS,OIj|lBm0,8}Jj|lhp2|-IjI_liui.43a*kcf<'vgSNjxa\/hlb($oOXFK0}nTNduu!z3;kapu;,6LJ6zu<{labdiI{e\cn:|l|eulv=KG?f&8\2TZzDsDH[{``g^qT=XunJ4ghUO3YlsqT9^7oqfdlEDR
08-10 10:47:41.374 21060 21060 F crashpad: z>"Am5:>`BVpUu+kka<3xKEw!2laJdH/t;t(?f&8<@KK8ip=iVU5S/".JKB^v0t;Mgiu!Jn@iVua:Rv08}e%jI;JbG]1G6COHAsefXYN}pqMRO$bF<ZqkP]sd$FUIKTsV7Kw1jgsJ~{w}jV@P+x.IKz^jwNcQ[25JqTlAiiui.*.sH=DNdJsz?BA3[Qd]GndZl.xB!-HBi|m8v*GhvtW6}oSki9l20pz"z{`x&DWwp)Iq<5uUI!cOExoL/xKEw!2laJd6fUr;v}jhQ1-7ZbZE5,J%oo,KKB^v0V\O@eav0t;#m4eRT,Oe%jI;JbGiWUr]fHi>LugX1Zz~0OKVlA~N:3TL_q5t9Bu1W_t^rb^.&64PgFp!R_2w-`r@(`oGVjfrVvT$olEma$v,I|\Sw~XC9]r<N)s-i"ObW2XV5dSA";n50qJKahi}spBR)+hEHle.F'v=G&Q~l7^JjPgZj*bP+Q^sgmh}s6kXk._"f!RMGcd}s6k(s`'_XrVrro)KmrD
08-10 10:47:41.374 21060 21060 F crashpad: 'v7~JA*X=AgprXZlBKEu#E/UO9W*I/T5JVwtl6\00bfa(Q6L),b*Vt*Rz2ULq.fPeg8Z:f~wzL01[\xw[nzgnxm>6j4ZI&D4(>d#bguE"LfDKL-+vd^L;6hpp91:!oftN.MKzu<{RLSRAKZZ&%-K6w8)ltAr,n29lK8<3;gpcD=t{:?"?nyX<&=I%V=s2)^NxU:wnTcNLGtK)_P!$1jKL6s7[uk6<@%x7<8=,)Od8[uBGkFxIjNAwLLcB&9u{q",Xr`n@f&8QzE55aA4?)/A+n`T_wPWYNnu5mR?OLqy3;kapu;,s+;X%wdYgKZ]hIn1DAGI0ds+5cT^vfU66R-br"36[L*:jgXwyevBhvHt\v<sjq`LxsDqPIie|M#e6m/A*T"ZrAU.[O?n[ld?9wEV\RQkrf>]aRk:D#HrPnJXlqh9""d>p"-K5wnwsbB'j\ij=(|W]@Bc;1rKk8V?CuN7,I@rG4S=kKvt6f-b<gn1<K[O%qAiaB|trYcr,>\Aw5.K
08-10 10:47:41.375 21060 21060 F crashpad: f\&1pMPLY`[>dherti&TKtLLrv9P8jikr_9W8Sdcs`$+QAaq`^n8xtuV`-!WYvXWdt&Z}m<^$#f\hwQh0Y9bwl6rF,[2)eHkXM|;*QkVqtPm1-~5X:=`*k$y{o3i\CT=Am2|-I<@1a,vcWmiKJVir^1Wn4T=;v`3]ApY4n9/d<PA=vyR*nAL5T$tBj_ZXE>)$S#m{ERK-jci*.Ui"GFsB!cSW_Gt:O-ZW__6?vcW/I<@rdO_aKB<^`]0)AZKh?&(m@R'a5eWv$SP1&!GdrJ\>P'1ta&I}ldkmRs+?^XC<@XgU6phz"w~Ev)j&ZD1:fW_GtVif8wlUK6&!q@*+ZpK(M0y]TlwqTXaCux!"<]r"z|l[vL!+Xlw#]O*ELNuY.LwlL*Z\Akr-!v/"G`p\Zv$/<iv.B`J~\>`Gt:wy>e[<7u|\A(T<sy>uS**^\{kf$bJs]]0aw]TR:sf,ud7Yw!=Z>)<uZ0q>Od6HqW_/y21"k5\@eU66R=j*z+8qIipTMCu
08-10 10:47:41.375 21060 21060 F crashpad: bpt]]0]KHv7A-ft]]0]KHv-,&DXKv66pW_Iu<j^8ZT"mm:}lxU/U]015<tfmMHX_CYpT]0+6n?2j[s7"&ZW_Kn>7n(ua?v{#g%<@jxR_bf>lHFuK<^p=FTcqFi*z+i"G6k>jRNIyW_Cr6qr,4<_KTumH6|Mgu+@Y]0q5LtN!2:"GHX)&a6dW]0$myL01{l[v)cw3I6*]m>`opNV.mVY^aK>ZJF<W7M{{5\60=LWZl/T.Rir^FB5<7+|WZfiIYdp#&<$15iR2:WQabvvr!i[v)cuVZ9*w30<H.aK^>2:B#T&'ou.;_GTh-k%EcLrC+WFFbovE2||]!TJm>DL%;DzwSYpidg]s*_hQiKUu*u$e/Jdbk:$1-M):#17Z:f81C<dsV.wKU]Q4oux~k^Tcx~-O`MM(cv{kr&Jvk7u|V.)T<s{OuS**~|*u88'v+=3+61;d{KdD#1A?wn^^{`)t%=)6T.^O&h{Od6J/.)&-r5_*L1'-'JVh.ZL?nI{)\w0aFL^p
08-10 10:47:41.375 21060 21060 F crashpad: 2-,d}ny~QJ?N{eQFLrt_ErPv{GNR8ut3qauquMC9hv||]TFuCR,jOr\v|1@r/^K,{Jjq<<Uaytm6\p')5kH_FxIy')WlH_nL_6jqV9~T/IJm:!C9$%HINH^NHc.)8ibh!"K6Uay=qI]SA\3EP!GWB<FT.11int(1{l[vzx|3iw+]m>`opN\AmVY^aK5Z0YR>S[{{"#<i0S?6lnp_;A#jgs(}jwDVOdR^yw_Jai*.UiJW\nd!9/JW2il+#m?w,MMjJW,:?Jrt4#l\f6,e,kc6PWZHFDIu2ubN'1JLA.NK6\9s)M1^/J?t9[:Nd6Fb^<f6~V[pKf*H?BGtLtjNaaNwDV'?0q06@r<%'%;A_K*Pq>#m3iJ\v$d\$m{m2i*zJj"G8tZZ]01KGwVy*"=+"GVukgsYb:zL8Y]0uK]K`;I55EhJ~\>`GtJ?n(?6a6F,zNV.*T<sgeuS**^\{kX)EtYC<@5wpBfH*V,tTtyTbRuapn.N#mmZgs(}Hv'95sD0$mk9
08-10 10:47:41.375 21060 21060 F crashpad: Av4Nk|J6J?U,I5Lt:7hvHtJ?n(uacKZX]sk[v$l\$m]n&F!G~oa@n(K6Hv\DA2<@HCKweh]c5kZ)@)/LYv#^I{"m;mFZ"Gp2v4eskchCuuro7R]T#myLhM5\$NR_.o6rO)#b5k~Zb:xtipTM+i"G6k>jRNIyW_Cr6qr,4<_KTumH6|Mgu+@i^t0CW_GtEiz"9OW_c!?B?[C5=w,zW_aKB<rj@aEK0g$WpF'v5^m>`opNj\|;l?wa6id3">x]f72j7Pn+hpN:1:OtB;{TNH/\D[!vAKz)@)Hv\$OrxNoN]cieH{Ag[]!g(b@as5TKjlTA[lY`^*n8:Bm)~IDk&LbJYkbR=Nl8+Q26@)'E7QRf_29O^r2H?B;n2ohg>Z:f.O2i.<)J;Rxd'o)Y.O<^@q+Jc&m)k\WllQ>|ua:RTc&e.f.JDs#f,Q~N*ux;'v=v;`,v)Tclm8&J}c@bh5$p*Y1K)<9/Tk[vL.sw3hJWkg{/bu/`xA3cNLFhSw?noWaS!<%n
08-10 10:47:41.375 21060 21060 F crashpad: }I|k<u-uB-X--vdVJ:S4OkBo@+Dnd(6u08gG{<(VeGN3{/,<4n%Te/@1`rx$0m[`n9!anUV8SutugC>|TcJjijAt+`33t5.x3TM\aQ|zXi?qWPmX4b131A{q5N#4[/?qZ]|BH/Ir/A6zrX\=%GZKqh(*gl$Fv^keN>:mP=C-"b/UF,tLu/#S8kWh2r117MN8d+W&w`;b'17vU8WKrI6Eh;f+V+3E|7Os0\P9^Jo~k@4rNXDK;v<wJ8;sZmNT9wwIXf(x`HZ^=JGv{KF00u*O[v5GiG{?AvB!ZFz^'vW^XmAUyf5mtX&bFj^]"G&o>xzNF<9N<9Pl~K\POaL8PSbP-mnf2utn{QWwru(t{*of;Zgm}dO^5<MM>8RF]J$.N8dzWaDC[oanP]/^iOd[GvYjNGz0t,qv-UXe?}y},wbsU8+Ks~Ws!ilDV@aaR4VPW*b2vP(6{a+EZ:&r&+kFFuxQHrtXSv%[>Gi_&Lgs2p`IlJjverz@9kN^Z2m8x:7AZsbz
08-10 10:47:41.375 21060 21060 F crashpad: (=NstOo0?k$CBIa-zwNsH@6mr]iYD[4>HU,Lx]>6Rg\_]rN?0J%FD+7W1Qjh0O-n\0h5kLOYjoHSfp0O=^>t+XwQ(6ELXYHw6?e&;A-Jl.;A,J|6\p")<^L+z&-&'AgrqJk83vv2u,4;7vi87D#RXq1ZiQD#!<,J2!2:FLqtEV^8HWB<FT$1qx01{luszCL*pao[miKJnmQ[*i:T4<apnD6s^ESO,{;6hpd9'E[uN^RBkgHTS{Kw-/AK4f^g6Q,L=,J+^@36[LLCKHHt:OW_eui7>q^gJ/blMn*|Mj0W4e,k/6$76p^g>mp5{_GJ4n|UosZ_}Vt'_V2W`=7?X.1s|"{O?sN;x;hAETD+A<nhuoUbRV|C;3Ma(zN`ZpuK<^$wigdl}p^6\[Fp>L5TTcFWfr'L{ouu1Vf4lwO6NH-KGwVy*")ADK1bP-=`cJ7Wf4t5Bs|"?B?[C5=w,zW_%tL#pz{o`nHG{o?JPgJV6aA443)wLJ+iYtzW[_1*_gd5[w_B
08-10 10:47:41.375 21060 21060 F crashpad: O9=vyR<'Bw{[Bo$3vqi1Egu~AK|jR[:p!BRrqX#H<*|nnyUQxcW3.l~4A+P6S%yO]r#Jr$CuMPg\7)"*[w.jwQN[q5_KDOXMAI]KbNF[zj2<k),U/px1#&<m2p\wd5tLbE1p~bhI\P[>$f,x>r#3RV`Hje"B}`22WV7k"L7mOrrq./Wawov|l>*v*^O*[wrL.t"x1f`MkFCL?W&4\wQcPLzdT![\IwWNS=,wT?0:NAa8nKCL;7<@*v4S61ia^o,gw3+wT?N[vsOcPL&ZF0$m`p"*ffHFE4,GEt*aXrqsB]!v&=@i.xqa0wGuv=zudO{w?Q?n&vMtUvLep\CrBW1sBhjd$(zh$IWX9g\_Ka^]-Z#iOu\Y}wdO5be^]G\Y<7>uup`igQ_M8k-`Us)q>(9\aw?;wJ&l`QZIF|z[@^$w'Vdl[/x~=G/jpa80N@<G:mmm'V^s44&DKw~K+gqh"&lVl6?qfFR;\\trq+2HYN7vA>+++wP?T=P*Ce+Z)v"^IH`p
08-10 10:47:41.378 21060 21060 F crashpad: Fvd60O01{lustCL*\wn[miKJpmIW*i.*^wLclk%V)1HV9vwf`ojf>6>kzR-i=]TC)dIWa^\?Av`r`ALJ^-KI*`*~"bU@iacMG_.>h,IVDj\2`qg9}i6Ch6f1c+b[f+cg'stprbU]n+kG4Tne*/Q_V/x~6q>/DD}u15{?_5aOlGZwTklig2)>6c^GPG|%P($(9G^OCR.?Thb7{H,n{]C:%/,}kD*Ctr)32YCnQy7*VzPyVy<2.'{{4%`|OGhLR1F^kE&IyD"{33:pY,{<1b<5(6RrSlbvqF;uKab&BVlIdo5ltK0uxnHVnrFtP1Lw-mY^IL_9eDv/;bfS|Zok!EJK"issH}xy,se~Kw:ScK>Y9Hy?&pC?JYBun^/b^5,dj21R]r(jMQZZGF5acPp-Hu;hOw}u9@UK+tP*W)6L%ZzBX`4Sl@#\k3s++-U,JpZH~_rs\g9UkGssH}l@LntI}*"GJL"JP9q+[h=a!64pHj[tH}BV#W`\2]GWvhQtVNQkIDvw
08-10 10:47:41.378 21060 21060 F crashpad: "zq\D2BuuyKX[__2-@^(jg,eFXFW>\s/~yxdARpA{oHd:XRJsn{z:DP_u6J2&tCrb;DLARi'{o>k|O{o\n4pzVLZB.>"SqOW;u%bHb",~t@}S.'JkO5L{5]@Kv8I2JBN[6<g;+64?lN(Ft>`L(&:|s@vXv:w1HV\3qEp}5Rb<Afs^HK7>)\o"K8z#5swg5Nk=Ptv5HLH-_8-1w3+|t.m6LLeeQ`k^C{F7_R^q>;fn[|a|taLn8x/[c'<Sq4pYibdj>dvVaiD$GaN9b2~|JB>Scy_!&8.>J_kXG2PVD3,;ABI9sIF5acPf8x/emXUJNc\C.Kw/^L(-qKEl`HL)^x!nKVde$Z`id"Nc\_p0ke$c6od=akK(kQK1he$;AWkXG~Z}`O@,y]Dr"IW!uaL:fdc.qHXL(EKNUL(4I1WvAU]bnjTL(+JD^PMJQQqvAP_u6J2x/9uwYWa;-*&xJz+^8x/-@d4N+KF$7&LMN/jI&tI\n#uc}\7/)5tCS|t-Rr+RJDd
08-10 10:47:41.378 21060 21060 F crashpad: Qaa`xdVJx/-iCB6d~Y&vG?jvmrJr3[{a;p8BTKaei+jS`y$tA.!rZVDM<5OWet&xGc6G;a/*4#B#(891_KQj|*!RZ6!L!`M9~ebGLL,`v2>W,@5N@nWZ(uJ676*7RA4_]@QAdgqvGWLQzKl82n0]ZFxKTvf*l\RA34V;ZTFn$70MS,-)va0s&NP5MY3yR:[*|dxu);RA4K8`#boExKvmT8%b7b0a0s^jdo91ptF"ua{Kjm+4.A/Q0y?yS,-)va0s&N<Kv{3yR:[*|dxu);RAxE(`#b1bVurop}zK^i^uFqhkQA#`OWC3]P<.!E-Hxv!p)R"NNXkIzKtr{F)_[bh-EQUnLK[Yo\"@g0o\Ls.Wdn"RhqvPN\71.sm>qV$K|oIJVH"m(fIroI&`e:1tMR,=XWX!4+V]^voIiQ1[Q4rK2Q.2p\=m5:&6^M.`XnH2}BR:,x\W{bF6=OfBAUMWRu'h9o+DFm:4A:h3}G.a#p%pRnk:_K2n:{jV43Lm$`y>X_7E
08-10 10:47:41.378 21060 21060 F crashpad: poVM,;}k}d6v);XTN4Z@WT>o+Qk`-JR5nd4EEU0sKm|ck:o(]m69;w&FSK9g&FXtrB344!q,csuZir\*8YD*'`'AQH*thG9oQ[&OP-_r\+p1WTFO$uk:dS.s#)uftw~pZJL],^)jB>bT-nfrjKItK(uYYK~`Vsh,xu([OJ26m7h6"LQg\tboUTk(FLFhp9:b2<>soIFpVPw;86Ui?q4;tVitoI:na%+WN4lw]X<o:b\Al5(poIJt<lBrJbb+apLW@bDe=SErl_'UVrt3iv*d4|ZfCa[JcK~`xp6x3_6$)X+ZjehWwM.brTL?Lw3pWN&H15XX@AmLHi2rcu':GH'a}`xe?v.)(A}KrZ]d|tT8Fu(Y?a]n@pfXkX)AkG6a~pP}Mkh?Hwrs:|A_,L(k?auv&+T?t+!c;ORZlv]tBX:Ly7~L4"vsFvF>?&2wK/4Kg`07%LF4YP85ro%a(YLgfQ5wI^>\"b)I)12qfX1:Lway'5(<JZ(5!G.ql_'UZt@s(G~(
08-10 10:47:41.378 21060 21060 F crashpad: ]r5pTh\EnIAvl6()NHYH=sxYalGtpWEW2tGf7wu4hm5uz0+th'<gCj8u2U-bN8YpT%2w;W3ms^`uV2ZE.Mel{o)bJzaKL'\=ig0F*V8nd;Zwuq^\EVlw()NH<jpYzv6/eT:LOqmyauL=R_"i=Jssl31LUwS\m**j@`D>!:aj:L-PVD3,j+"\l*np4-GS2NN_euRP>LOKX*NHTr:J{5{ostLE4`~wc*J}=Y>h`n_a\uV2N\=sJ5VQkIzvlZ~0)YLJ"K/s&<+bS=[twn\6">~@?\&Mi*NHWg\5{ospLKQFXgMnd;(<$*NHJqZFU53OuMvv6/eT:L_Y1{:LKo3\og}v&gLT6q,J>H#er*Y+^6DW;Wx3>s$O$ZauL=d;[#yq!p(r1,(IigrI\nDdr*S.]uRP5Low</ik,)FrmeaZq+NHnCtOb6lK[Yq>'.Jgvpxq`Akp;>O
08-10 10:47:41.378 21060 21060 F crashpad: -----END CRASHPAD MINIDUMP-----
08-10 10:47:41.380 21060 21060 D AndroidRuntime: Shutting down VM
08-10 10:47:42.206  1707 21073 I DropBoxManagerService: add tag=data_app_crash isTagEnabled=true flags=0x2
08-10 10:47:42.206  1707  4795 W ActivityTaskManager:   Force finishing activity com.anonymous.videodownloaderapp/.MainActivity
08-10 10:47:42.207  1331  1396 I BpBinder: onLastStrongRef automatically unlinking death recipients: 
08-10 10:47:42.208 17070 17070 D VRI[0 fl=}]: visibilityChanged oldVisibility=false newVisibility=true
08-10 10:47:42.209  2478  2498 V WindowManagerShell: Transition requested (#209): android.os.BinderProxy@9a35dc0 TransitionRequestInfo { type = CLOSE, triggerTask = null, pipChange = null, remoteTransition = null, displayChange = null, flags = 16, debugId = 209 }
08-10 10:47:42.209 20968 20968 I Process : Sending signal. PID: 20968 SIG: 9
08-10 10:47:42.236  1707  2865 I ActivityManager: Process com.anonymous.videodownloaderapp (pid 20968) has died: fg  TOP 
08-10 10:47:42.236  1707  1766 I WindowManager: WIN DEATH: Window{12230b8 u0 com.anonymous.videodownloaderapp/com.anonymous.videodownloaderapp.MainActivity}
08-10 10:47:42.237 20589 20589 I adbd    : Remote process closed the socket (on MSG_PEEK)
08-10 10:47:42.238  1707  2384 D ConnectivityService: releasing NetworkRequest [ REQUEST id=1508, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10507 RequestorUid: 10507 RequestorPkg: com.anonymous.videodownloaderapp UnderlyingNetworks: Null] ] (release request)
08-10 10:47:42.238  1707  2865 I ActivityManager: Killing 21003:com.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0/u0a507i39 (adj 0): isolated not needed
08-10 10:47:42.240  1707  1858 D DisplayManagerService: Drop pending events for gone uid 10507
08-10 10:47:42.240  3994  4003 D ForegroundUtils: UID: 10507 deleted.
08-10 10:47:42.242  1707  2384 D InetDiagMessage: Destroyed 0 sockets, proto=IPPROTO_TCP, family=AF_INET, states=14
08-10 10:47:42.242  1707  2384 D InetDiagMessage: Destroyed 0 sockets, proto=IPPROTO_TCP, family=AF_INET6, states=14
08-10 10:47:42.242  1707  2384 D InetDiagMessage: Destroyed live tcp sockets for uids={10507} in 1ms
08-10 10:47:42.243  1707  1921 W libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
08-10 10:47:42.243  1707  2384 D InetDiagMessage: Destroyed 0 sockets, proto=IPPROTO_TCP, family=AF_INET, states=14
08-10 10:47:42.243  1707  1860 W ActivityManager: setHasOverlayUi called on unknown pid: 20968
08-10 10:47:42.243  1707  2384 D InetDiagMessage: Destroyed 0 sockets, proto=IPPROTO_TCP, family=AF_INET6, states=14
08-10 10:47:42.243  1707  2384 D InetDiagMessage: Destroyed live tcp sockets for uids={20507} in 1ms
08-10 10:47:42.244  2615  2615 D ViewRootImpl: Skipping stats log for color mode
08-10 10:47:42.245  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:42.245  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:42.245  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:42.245  3994  4003 D ForegroundUtils: Foreground UID status:
08-10 10:47:42.245  3994  4003 D ForegroundUtils: UID: 1002 is in foreground: true
08-10 10:47:42.245  3994  4003 D ForegroundUtils: UID: 10140 is in foreground: true
08-10 10:47:42.245  3994  4003 D ForegroundUtils: UID: 10176 is in foreground: true
08-10 10:47:42.245  3994  4003 D ForegroundUtils: UID: 10183 is in foreground: true
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 10190 is in foreground: true
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 10194 is in foreground: false
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 10221 is in foreground: false
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 10225 is in foreground: false
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 10233 is in foreground: false
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 10238 is in foreground: false
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 10239 is in foreground: false
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 10245 is in foreground: false
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 10255 is in foreground: false
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 10469 is in foreground: false
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 1010137 is in foreground: true
08-10 10:47:42.246  3994  4003 D ForegroundUtils: UID: 1010190 is in foreground: true
08-10 10:47:42.246  1707  1921 W libprocessgroup: SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
08-10 10:47:42.246  1707  1914 I libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_10507/pid_20968
08-10 10:47:42.247  1119  1119 I Zygote  : Process 20968 exited due to signal 9 (Killed)
08-10 10:47:42.247 17070 17070 I bmex    : onStart
08-10 10:47:42.248 17070 17070 I bjvp    : (REDACTED) [%s] onStart()
08-10 10:47:42.248 17070 17070 I bjxt    : Reloading theme data
08-10 10:47:42.248  1707  2435 D BackgroundInstallControlService: Package event received: 0
08-10 10:47:42.248 17070 17070 I bmex    : onResume
08-10 10:47:42.250  1707  4798 I AppWidgetServiceImpl: startListening() 0
08-10 10:47:42.250  2478  2498 D WindowManagerShell: setLauncherKeepClearAreaHeight: visible=true, height=260
08-10 10:47:42.252  2615  2615 D BaseDepthController: setSurface:
08-10 10:47:42.252  2615  2615 D BaseDepthController: 	mWaitingOnSurfaceValidity: false
08-10 10:47:42.252  2615  2615 D BaseDepthController: 	mSurface: Surface(name=com.android.launcher3/com.android.launcher3.uioverrides.QuickstepLauncher)/@0xd8c3063
08-10 10:47:42.256  2615  2615 D StatsLog: LAUNCHER_ONRESUME
08-10 10:47:42.259  2478  5604 V WindowManagerShell: onTransitionReady(transaction=7331509186268)
08-10 10:47:42.259  2478  2498 V WindowManagerShell: onTransitionReady (#209) android.os.BinderProxy@9a35dc0: {id=209 t=CLOSE f=0x10 trk=0 r=[0@Point(0, 0)] c=[
08-10 10:47:42.259  2478  2498 V WindowManagerShell:         {m=TO_FRONT f=SHOW_WALLPAPER|MOVE_TO_TOP leash=Surface(name=Task=1)/@0x39f9af9 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
08-10 10:47:42.259  2478  2498 V WindowManagerShell:         {m=CLOSE f=NONE leash=Surface(name=Task=51)/@0x1c30a3e sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
08-10 10:47:42.259  2478  2498 V WindowManagerShell:         {m=TO_FRONT f=IS_WALLPAPER leash=Surface(name=WallpaperWindowToken{7dd9a8e token=android.os.Binder@de64190})/@0x71e709f sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0}
08-10 10:47:42.259  2478  2498 V WindowManagerShell:     ]}
08-10 10:47:42.259  2478  2498 V WindowManagerShell: Playing animation for (#209) android.os.BinderProxy@9a35dc0@0
08-10 10:47:42.259  2478  2498 V ShellRecents: RecentsTransitionHandler.startAnimation: no controller found
08-10 10:47:42.260  2478  2498 V WindowManagerShell: Transition doesn't have explicit remote, search filters for match for {id=209 t=CLOSE f=0x10 trk=0 r=[0@Point(0, 0)] c=[{m=TO_FRONT f=SHOW_WALLPAPER|MOVE_TO_TOP leash=Surface(name=Task=1)/@0x39f9af9 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},{m=CLOSE f=NONE leash=Surface(name=Task=51)/@0x1c30a3e sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},{m=TO_FRONT f=IS_WALLPAPER leash=Surface(name=WallpaperWindowToken{7dd9a8e token=android.os.Binder@de64190})/@0x71e709f sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0}]}
08-10 10:47:42.260  2478  2498 V WindowManagerShell:  Checking filter Pair{{types=[] flags=0x0] notFlags=0x100 checks=[{atype=home independent=true modes=[OPEN,TO_FRONT] flags=NONE mustBeTask=false order=TOP topActivity=ComponentInfo{com.android.launcher3/com.android.launcher3.uioverrides.QuickstepLauncher} launchCookie=null windowingMode=undefined},{atype=standard independent=true modes=[CLOSE,TO_BACK] flags=NONE mustBeTask=false order=ANY topActivity=null launchCookie=null windowingMode=undefined},{NOT atype=undefined independent=true modes=[] flags=NONE mustBeTask=true order=ANY topActivity=null launchCookie=null customAnim=true windowingMode=undefined}]} RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@43ce1a8, appThread = android.app.IApplicationThread$Stub$Proxy@a9d74c1, debugName = QuickstepLaunchHome }}
08-10 10:47:42.260  2478  2498 D RemoteTransitionHandler: Found filterPair{{types=[] flags=0x0] notFlags=0x100 checks=[{atype=home independent=true modes=[OPEN,TO_FRONT] flags=NONE mustBeTask=false order=TOP topActivity=ComponentInfo{com.android.launcher3/com.android.launcher3.uioverrides.QuickstepLauncher} launchCookie=null windowingMode=undefined},{atype=standard independent=true modes=[CLOSE,TO_BACK] flags=NONE mustBeTask=false order=ANY topActivity=null launchCookie=null windowingMode=undefined},{NOT atype=undefined independent=true modes=[] flags=NONE mustBeTask=true order=ANY topActivity=null launchCookie=null customAnim=true windowingMode=undefined}]} RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@43ce1a8, appThread = android.app.IApplicationThread$Stub$Proxy@a9d74c1, debugName = QuickstepLaunchHome }}
08-10 10:47:42.260  2478  2498 V WindowManagerShell:  Delegate animation for (#209) to RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@43ce1a8, appThread = android.app.IApplicationThread$Stub$Proxy@a9d74c1, debugName = QuickstepLaunchHome }
08-10 10:47:42.262  2615  2615 D StateManager: createAtomicAnimation - fromState: Background, toState: Normal, partial trace:
08-10 10:47:42.262  2615  2615 D StateManager: 	at com.android.quickstep.util.ScalingWorkspaceRevealAnim.<init>(ScalingWorkspaceRevealAnim.kt:89)
08-10 10:47:42.262  2615  2615 D StateManager: 	at com.android.launcher3.QuickstepTransitionManager.createWallpaperOpenAnimations(QuickstepTransitionManager.java:1697)
08-10 10:47:42.262  2615  2615 D StateManager: 	at com.android.launcher3.QuickstepTransitionManager$WallpaperOpenLauncherAnimationRunner.onAnimationStart(QuickstepTransitionManager.java:1830)
08-10 10:47:42.263  2615  2615 D b/311077782: LauncherAnimationRunner.setAnimation
08-10 10:47:42.262 20320 20320 I binder  : undelivered transaction 2442720, process died.
08-10 10:47:42.262 20320 20320 I binder  : undelivered transaction 2442723, process died.
08-10 10:47:42.262 20320 20320 I binder  : undelivered transaction 2442719, process died.
08-10 10:47:42.264  2615  2639 W HWUI    : Image decoding logging dropped!
08-10 10:47:42.264  1331  1331 I BpBinder: onLastStrongRef automatically unlinking death recipients: 
08-10 10:47:42.265  1707  1914 I libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_99039/pid_21003
08-10 10:47:42.265  2600  2600 I Zygote  : Process 21003 exited cleanly (0)
08-10 10:47:42.269  1707  1857 V WindowManager: Sent Transition (#209) createdAt=08-10 10:47:42.206 via request=TransitionRequestInfo { type = CLOSE, triggerTask = null, pipChange = null, remoteTransition = null, displayChange = null, flags = 16, debugId = 209 }
08-10 10:47:42.269  1707  1857 V WindowManager:     startWCT=WindowContainerTransaction { changes= {} hops= [] errorCallbackToken=null taskFragmentOrganizer=null }
08-10 10:47:42.269  1707  1857 V WindowManager:     info={id=209 t=CLOSE f=0x10 trk=0 r=[0@Point(0, 0)] c=[
08-10 10:47:42.269  1707  1857 V WindowManager:         {WCT{RemoteToken{65e4f0d Task{2ea6ff9 #1 type=home}}} m=TO_FRONT f=SHOW_WALLPAPER|MOVE_TO_TOP leash=Surface(name=Task=1)/@0xe810d15 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
08-10 10:47:42.269  1707  1857 V WindowManager:         {WCT{RemoteToken{ed647e9 Task{e20ff6c #51 type=standard A=10507:com.anonymous.videodownloaderapp}}} m=CLOSE f=NONE leash=Surface(name=Task=51)/@0x976f9ed sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
08-10 10:47:42.269  1707  1857 V WindowManager:         {m=TO_FRONT f=IS_WALLPAPER leash=Surface(name=WallpaperWindowToken{7dd9a8e token=android.os.Binder@de64190})/@0xe51e5d4 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0}
08-10 10:47:42.269  1707  1857 V WindowManager:     ]}
08-10 10:47:42.270  1235  1235 I android.hardware.power-service-qti: Power setMode: 5 to: 0
08-10 10:47:42.271  2478  2498 V WindowManagerShell:  animated by com.android.wm.shell.transition.RemoteTransitionHandler@5cdf972
08-10 10:47:42.272  1707  1858 D DisplayManagerService: Drop pending events for gone uid 99039
08-10 10:47:42.272  1707  2862 W UserManagerService: Requested status bar icon for non-badged user 0
08-10 10:47:42.272  3994  4003 D ForegroundUtils: UID: 99039 deleted.
08-10 10:47:42.279  1707  1862 W ActivityTaskManager: Unable to send transaction to client proc com.anonymous.videodownloaderapp: no app thread
08-10 10:47:42.295  1331  1331 I BpBinder: onLastStrongRef automatically unlinking death recipients: 
08-10 10:47:42.305  1707  2357 I ImeTracker: com.android.launcher3:1e0018eb: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
08-10 10:47:42.306  2615  2615 D InsetsController: hide(ime(), fromIme=false)
08-10 10:47:42.306  2615  2615 I ImeTracker: com.android.launcher3:1e0018eb: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
08-10 10:47:42.309  3893  3893 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():2054 
08-10 10:47:42.310  3893  3893 I Module  : DeviceLockedStatusModuleProvider$Module.updateDeviceLockedStatus():100 repeatCheckTimes = 0, locked = false
08-10 10:47:42.311  3893  3893 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1315 onStartInput(EditorInfo{EditorInfo{packageName=com.android.launcher3, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
08-10 10:47:42.312  3893  3893 I Module  : DeviceLockedStatusModuleProvider$Module.updateDeviceLockedStatus():100 repeatCheckTimes = 1, locked = false
08-10 10:47:42.313  1707  2862 W PackageConfigPersister: App-specific configuration not found for packageName: com.android.launcher3 and userId: 0
08-10 10:47:42.472  1236  2320 I slpi    : STK3A5X ALS_OC: lux:80.519997, last_ps:1[1717], CH0:55, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:42.503  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-10 10:47:42.503  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-10 10:47:42.505  1227  6058 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-10 10:47:42.505  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-10 10:47:42.546  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-10 10:47:42.546  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-10 10:47:42.571  2914 20938 I NearbyMediums: Found Fast Ble Advertisements :
08-10 10:47:42.571  2914 20938 I NearbyMediums: BleAdvertisement { version=2, socketVersion=2, isFast=true, serviceIdHash=null, data=[ 0x23 0x4e 0x55 0x41 0x48 0x11 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ], deviceToken=[ 0x8f 0xcf ], rxAdvertisement=null } : 2 times.
08-10 10:47:42.571  2914 20938 I NearbyMediums: Total 2 fast advertisements from 1 unique advertisers.
08-10 10:47:42.696  1227  5890 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-10 10:47:42.708  1707  1860 W ActivityTaskManager: Activity top resumed state loss timeout for ActivityRecord{117477407 u0 com.anonymous.videodownloaderapp/.MainActivity t51 f} isExiting}
08-10 10:47:42.907  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:42.907  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:42.907  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:42.908  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:42.908  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:43.270  2615  2615 D ScalingWorkspaceRevealAnim: alpha of workspace at the end of animation: 1.0
08-10 10:47:43.273  2478  2498 V WindowManagerShell: Transition animation finished (aborted=false), notifying core (#209) android.os.BinderProxy@9a35dc0@0
08-10 10:47:43.278  1707  1857 V WindowManager: Finish Transition (#209): created at 08-10 10:47:42.206 collect-started=0.067ms request-sent=0.096ms started=3.765ms ready=34.45ms sent=52.177ms finished=1070.064ms
08-10 10:47:43.278  1707  4791 W ActivityManager: pid 1707 system sent binder code 7 with flags 1 to frozen apps and got error -32
08-10 10:47:43.279  1707  4791 W WindowManager: Exception thrown during dispatchAppVisibility Window{12230b8 u0 com.anonymous.videodownloaderapp/com.anonymous.videodownloaderapp.MainActivity EXITING}
08-10 10:47:43.279  1707  4791 W WindowManager: android.os.DeadObjectException
08-10 10:47:43.279  1707  4791 W WindowManager: 	at android.os.BinderProxy.transactNative(Native Method)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at android.os.BinderProxy.transact(BinderProxy.java:592)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at android.view.IWindow$Stub$Proxy.dispatchAppVisibility(IWindow.java:557)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at com.android.server.wm.WindowState.sendAppVisibilityToClients(WindowState.java:3298)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at com.android.server.wm.WindowContainer.sendAppVisibilityToClients(WindowContainer.java:1343)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at com.android.server.wm.WindowToken.setClientVisible(WindowToken.java:437)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at com.android.server.wm.ActivityRecord.setClientVisible(ActivityRecord.java:7147)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at com.android.server.wm.ActivityRecord.postApplyAnimation(ActivityRecord.java:5841)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at com.android.server.wm.ActivityRecord.commitVisibility(ActivityRecord.java:5784)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at com.android.server.wm.Transition.finishTransition(Transition.java:1373)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at com.android.server.wm.TransitionController.finishTransition(TransitionController.java:986)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at com.android.server.wm.WindowOrganizerController.finishTransition(WindowOrganizerController.java:526)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at android.window.IWindowOrganizerController$Stub.onTransact(IWindowOrganizerController.java:293)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at com.android.server.wm.WindowOrganizerController.onTransact(WindowOrganizerController.java:215)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at android.os.Binder.execTransactInternal(Binder.java:1411)
08-10 10:47:43.279  1707  4791 W WindowManager: 	at android.os.Binder.execTransact(Binder.java:1350)
08-10 10:47:43.280  1707  4791 W Process : Unable to open /proc/20968/status
08-10 10:47:43.293  2478  2498 V WindowManagerShell: Track 0 became idle
08-10 10:47:43.293  2478  2498 V WindowManagerShell: All active transition animations finished
08-10 10:47:43.298  1707  4791 W UserManagerService: Requested status bar icon for non-badged user 0
08-10 10:47:43.298  2615  2615 D RecentsView: onTaskRemoved: 51, not handling task stack changes
08-10 10:47:43.298  2615  2615 D RecentsView: onTaskRemoved: 51, not handling task stack changes
08-10 10:47:43.300  1707  2865 W UserManagerService: Requested status bar icon for non-badged user 0
08-10 10:47:43.320  1331  1331 E BpTransactionCompletedListener: Failed to transact (-32)
08-10 10:47:43.332  2615  2615 W FrameTracker: Missed App frame:JANK_APPLICATION, 913546, 23254072, CUJ=J<LAUNCHER_APP_CLOSE_TO_HOME>
08-10 10:47:43.582  1331  1579 D DisplayModeController: setDesiredMode 4630947043778501762 {mode={fps=48.00 Hz, modePtr={id=4, vsyncRate=48.00 Hz, peakRefreshRate=48.00 Hz}}, emitEvent=false, force=false}
08-10 10:47:43.587  1257  1361 I SDM     : HWDeviceDRM::UpdateMixerAttributes: Mixer WxH 1080x2400-0 for Peripheral
08-10 10:47:43.588  1257  1361 I SDM     : HWCDisplay::SubmitDisplayConfig: Active configuration changed to: 4
08-10 10:47:43.593   563   563 I         : (mipi_mot_cmd_csot_1080p_dsc_667)+
08-10 10:47:43.615   563   563 I dsi_panel_set_cabc: Set CABC to (0)
08-10 10:47:43.615   563   563 I         : Set ACL to (0)
08-10 10:47:43.615   563   563 I         : Set DC to (1)
08-10 10:47:43.814  1236  2320 I slpi    : STK3A5X ALS_OC: lux:117.120003, last_ps:1[1720], CH0:80, CH2:3, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:43.822  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:43.917  1236  2320 I slpi    : STK3A5X ALS_OC: lux:146.399994, last_ps:1[1721], CH0:100, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:43.983  1707  4019 E TaskPersister: File error accessing recents directory (directory doesn't exist?).
08-10 10:47:44.122  1236  2320 I slpi    : STK3A5X ALS_OC: lux:162.503998, last_ps:1[1710], CH0:111, CH2:5, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:44.124 23875 23939 I System.out: onBinaryReceived
08-10 10:47:44.436  1236  2320 I slpi    : STK3A5X ALS_OC: lux:134.688004, last_ps:1[1719], CH0:92, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:44.639  1236  2320 I slpi    : STK3A5X ALS_OC: lux:111.264000, last_ps:1[1716], CH0:76, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:44.744  1236  2320 I slpi    : STK3A5X ALS_OC: lux:95.160004, last_ps:1[1716], CH0:65, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:44.919  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:44.919  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:44.919  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:44.919  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:44.919  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:44.950  1236  2320 I slpi    : STK3A5X ALS_OC: lux:106.872002, last_ps:1[1717], CH0:73, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:45.053  1236  2320 I slpi    : STK3A5X ALS_OC: lux:121.512001, last_ps:1[1727], CH0:83, CH2:3, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:45.133  1227  1227 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-10 10:47:45.134  1227  1227 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-10 10:47:45.135  1227  1227 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Writing IBS_WAKE_IND
08-10 10:47:45.144  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_ACK: 0xFC
08-10 10:47:45.144  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Signal wack_cond_
08-10 10:47:45.144  1227  1227 D vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Unblocked from waiting for FC, pthread_cond_timedwait ret = 0
08-10 10:47:45.145  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-10 10:47:45.145  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-10 10:47:45.145  5655  5883 I bluetooth: system/gd/hci/le_address_manager.cc:593 GetNextPrivateAddressIntervalRange: client=LeAddressManager, nonwake=8m41s, wake=14m54s
08-10 10:47:45.151  5655  5883 I bluetooth: system/gd/hci/le_address_manager.cc:792 OnCommandComplete: Received command complete with op_code LE_SET_RANDOM_ADDRESS(0x2005)
08-10 10:47:45.151  5655  5883 I bluetooth: system/gd/hci/le_address_manager.cc:812 OnCommandComplete: update random address : xx:xx:xx:xx:d9:46
08-10 10:47:45.151  5655  5883 I bluetooth: system/gd/hci/le_address_manager.cc:403 resume_registered_clients: Resuming registered clients
08-10 10:47:45.195  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-10 10:47:45.778  1236  2320 I slpi    : STK3A5X ALS_OC: lux:101.015999, last_ps:1[1719], CH0:69, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:45.981  1236  2320 I slpi    : STK3A5X ALS_OC: lux:86.375999, last_ps:1[1721], CH0:59, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:46.154  1227  6063 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceSleep: TX Awake, Sending SLEEP_IND
08-10 10:47:46.154  1227  6063 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-10 10:47:46.306  1227  5890 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-10 10:47:46.603  1236  2320 I slpi    : STK3A5X ALS_OC: lux:77.592003, last_ps:1[1716], CH0:53, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:46.854  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:46.912  1236  2320 I slpi    : STK3A5X ALS_OC: lux:90.767998, last_ps:1[1714], CH0:62, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:46.937  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:46.937  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:46.937  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:46.937  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:46.937  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:47.015  1236  2320 I slpi    : STK3A5X ALS_OC: lux:133.223999, last_ps:1[1720], CH0:91, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:47.116  1236  2320 I slpi    : STK3A5X ALS_OC: lux:158.112000, last_ps:1[1724], CH0:108, CH2:5, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:47.253  1707  2384 D InetDiagMessage: Destroyed 0 sockets, proto=IPPROTO_TCP, family=AF_INET, states=14
08-10 10:47:47.254  1707  2384 D InetDiagMessage: Destroyed 0 sockets, proto=IPPROTO_TCP, family=AF_INET6, states=14
08-10 10:47:47.255  1707  2384 D InetDiagMessage: Destroyed live tcp sockets for uids={10162} in 3ms
08-10 10:47:47.257  1707  2384 D InetDiagMessage: Destroyed 0 sockets, proto=IPPROTO_TCP, family=AF_INET, states=14
08-10 10:47:47.259  1707  2384 D InetDiagMessage: Destroyed 0 sockets, proto=IPPROTO_TCP, family=AF_INET6, states=14
08-10 10:47:47.259  1707  2384 D InetDiagMessage: Destroyed live tcp sockets for uids={20162} in 4ms
08-10 10:47:47.372 20320 20320 I sd 0    : 0:0:4: [sde] Synchronizing SCSI cache
08-10 10:47:47.375 20320 20320 I ufshpb_suspend: 3235 info: ufshpb_lu 4 goto suspend
08-10 10:47:47.375 20320 20320 I ufshpb_suspend: 3236 info: ufshpb_lu 4 changes suspend state
08-10 10:47:47.736  1236  2320 I slpi    : STK3A5X ALS_OC: lux:140.544006, last_ps:1[1720], CH0:96, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:47.944  1236  2320 I slpi    : STK3A5X ALS_OC: lux:117.120003, last_ps:1[1714], CH0:80, CH2:3, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:47.974 20513 20513 I ufshpb_resume: 3261 info: ufshpb_lu 4 resume. do_task_work 0 retry 0
08-10 10:47:48.358  1236  2320 I slpi    : STK3A5X ALS_OC: lux:130.296005, last_ps:1[1711], CH0:89, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:48.459  1236  2320 I slpi    : STK3A5X ALS_OC: lux:144.936005, last_ps:1[1718], CH0:99, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:48.562  1236  2320 I slpi    : STK3A5X ALS_OC: lux:159.576004, last_ps:1[1715], CH0:109, CH2:5, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:48.951  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:48.951  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:48.951  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:48.951  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:48.952  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:49.887  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:50.422  1236  2320 I slpi    : STK3A5X ALS_OC: lux:140.544006, last_ps:1[1708], CH0:96, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:50.525  1236  2320 I slpi    : STK3A5X ALS_OC: lux:124.440002, last_ps:1[1717], CH0:85, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:50.836  1236  2320 I slpi    : STK3A5X ALS_OC: lux:152.255997, last_ps:1[1719], CH0:104, CH2:5, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:50.905  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-10 10:47:50.905  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-10 10:47:50.907  1227  6058 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-10 10:47:50.907  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-10 10:47:50.950  1227  6058 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-10 10:47:50.950  1227  6058 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-10 10:47:50.964  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:50.964  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:50.964  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:50.965  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:50.965  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:51.101  1227  5890 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-10 10:47:51.351  1236  2320 I slpi    : STK3A5X ALS_OC: lux:133.223999, last_ps:1[1718], CH0:91, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:51.453  1236  2320 I slpi    : STK3A5X ALS_OC: lux:106.872002, last_ps:1[1726], CH0:73, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:51.556  1236  2320 I slpi    : STK3A5X ALS_OC: lux:84.912003, last_ps:1[1717], CH0:58, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:51.581 20513 20513 I sd 0    : 0:0:4: [sde] Synchronizing SCSI cache
08-10 10:47:51.583 20513 20513 I ufshpb_suspend: 3235 info: ufshpb_lu 4 goto suspend
08-10 10:47:51.583 20513 20513 I ufshpb_suspend: 3236 info: ufshpb_lu 4 changes suspend state
08-10 10:47:51.971  1236  2320 I slpi    : STK3A5X ALS_OC: lux:76.127998, last_ps:1[1716], CH0:52, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:52.197 20513 20513 E msm_dwc3_perf_vote_update: latency updated to: 61
08-10 10:47:52.249  1707  2339 D ActivityManager: freezing 12432 com.android.webview:webview_service
08-10 10:47:52.256  1707  2384 D InetDiagMessage: Destroyed 0 sockets, proto=IPPROTO_TCP, family=AF_INET, states=14
08-10 10:47:52.258  1707  2384 D InetDiagMessage: Destroyed 0 sockets, proto=IPPROTO_TCP, family=AF_INET6, states=14
08-10 10:47:52.258  1707  2384 D InetDiagMessage: Destroyed live tcp sockets for uids={10162} in 5ms
08-10 10:47:52.317  1116 20583 I netd    : tetherGetStats() -> {[TetherStatsParcel{iface: wlan0, rxBytes: 270093235, rxPackets: 113280, txBytes: 51630824, txPackets: 65023, ifIndex: 0}]} <3.83ms>
08-10 10:47:52.344 20320 20320 I ufshpb_resume: 3261 info: ufshpb_lu 4 resume. do_task_work 0 retry 0
08-10 10:47:52.350  1116 20583 I netd    : bandwidthSetGlobalAlert(2097152) <0.52ms>
08-10 10:47:52.351  2736  2764 I QImsService: VtDataUsageProvider : onSetAlert:2097152
08-10 10:47:52.351  2736  2736 I QImsService: ImsServiceClassTracker : updateAlertQuota:newQuota=2097152,Remaining=2097152
08-10 10:47:52.573  2914 20938 I NearbyMediums: Found Fast Ble Advertisements :
08-10 10:47:52.573  2914 20938 I NearbyMediums: BleAdvertisement { version=2, socketVersion=2, isFast=true, serviceIdHash=null, data=[ 0x23 0x4e 0x55 0x41 0x48 0x11 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ], deviceToken=[ 0x8f 0xcf ], rxAdvertisement=null } : 1 times.
08-10 10:47:52.573  2914 20938 I NearbyMediums: Total 1 fast advertisements from 1 unique advertisers.
08-10 10:47:52.797  1236  2320 I slpi    : STK3A5X ALS_OC: lux:84.912003, last_ps:1[1715], CH0:58, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:52.898  1236  2320 I slpi    : STK3A5X ALS_OC: lux:99.552002, last_ps:1[1716], CH0:68, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:52.919  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:52.972  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:52.972  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:52.972  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:52.972  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:52.972  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:53.001  1236  2320 I slpi    : STK3A5X ALS_OC: lux:120.047997, last_ps:1[1725], CH0:82, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:53.209  1236  2320 I slpi    : STK3A5X ALS_OC: lux:143.472000, last_ps:1[1724], CH0:98, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:53.830  1236  2320 I slpi    : STK3A5X ALS_OC: lux:128.832001, last_ps:1[1718], CH0:88, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:54.139  1236  2320 I slpi    : STK3A5X ALS_OC: lux:115.655998, last_ps:1[1720], CH0:79, CH2:3, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:54.330 20513 20513 E msm_dwc3_perf_vote_update: latency updated to: -1
08-10 10:47:54.656  1236  2320 I slpi    : STK3A5X ALS_OC: lux:103.944000, last_ps:1[1716], CH0:71, CH2:2, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:54.977  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:54.977  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:54.978  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:54.978  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:54.979  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:55.688  1236  2320 I slpi    : STK3A5X ALS_OC: lux:92.232002, last_ps:1[1715], CH0:63, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:55.954  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:56.103  1236  2320 I slpi    : STK3A5X ALS_OC: lux:105.407997, last_ps:1[1710], CH0:72, CH2:2, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:56.982  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:56.983  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:56.983  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:56.983  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:56.983  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:57.236  1236  2320 I slpi    : STK3A5X ALS_OC: lux:93.695999, last_ps:1[1720], CH0:64, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:57.960  1236  2320 I slpi    : STK3A5X ALS_OC: lux:83.447998, last_ps:1[1717], CH0:57, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:58.501 20320 20320 I sd 0    : 0:0:4: [sde] Synchronizing SCSI cache
08-10 10:47:58.504 20320 20320 I ufshpb_suspend: 3235 info: ufshpb_lu 4 goto suspend
08-10 10:47:58.504 20320 20320 I ufshpb_suspend: 3236 info: ufshpb_lu 4 changes suspend state
08-10 10:47:58.993  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:47:58.993  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:47:58.994  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:47:58.994  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:47:58.994  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:47:58.995  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:47:59.302  1236  2320 I slpi    : STK3A5X ALS_OC: lux:98.087997, last_ps:1[1716], CH0:67, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:59.405  1236  2320 I slpi    : STK3A5X ALS_OC: lux:111.264000, last_ps:1[1718], CH0:76, CH2:2, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:59.507  1236  2320 I slpi    : STK3A5X ALS_OC: lux:131.759995, last_ps:1[1726], CH0:90, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:59.613  1236  2320 I slpi    : STK3A5X ALS_OC: lux:159.576004, last_ps:1[1716], CH0:109, CH2:5, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:47:59.899 20518 20518 I ufshpb_resume: 3261 info: ufshpb_lu 4 resume. do_task_work 0 retry 0
08-10 10:47:59.975  1707  2339 D ActivityManager: freezing 12117 com.mgoogle.android.gms
08-10 10:48:00.233  1236  2320 I slpi    : STK3A5X ALS_OC: lux:130.296005, last_ps:1[1720], CH0:89, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:00.438  1236  2320 I slpi    : STK3A5X ALS_OC: lux:109.800003, last_ps:1[1719], CH0:75, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:00.645  1236  2320 I slpi    : STK3A5X ALS_OC: lux:96.624001, last_ps:1[1708], CH0:66, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:00.849  1236  2320 I slpi    : STK3A5X ALS_OC: lux:106.872002, last_ps:1[1722], CH0:73, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:01.010  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:48:01.010  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:48:01.011  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:48:01.011  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:48:01.011  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:48:01.264  1236  2320 I slpi    : STK3A5X ALS_OC: lux:121.512001, last_ps:1[1719], CH0:83, CH2:3, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:01.470  1236  2320 I slpi    : STK3A5X ALS_OC: lux:134.688004, last_ps:1[1715], CH0:92, CH2:4, CH4:8, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:01.679  1236  2320 I slpi    : STK3A5X ALS_OC: lux:112.727997, last_ps:1[1721], CH0:77, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:01.778  1236  2320 I slpi    : STK3A5X ALS_OC: lux:92.232002, last_ps:1[1715], CH0:63, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:01.800  1116 20583 I netd    : setProcSysNet(4, 2, wlan0, retrans_time_ms, 750) <0.30ms>
08-10 10:48:01.800  1116 20583 I netd    : setProcSysNet(4, 2, wlan0, ucast_solicit, 10) <0.09ms>
08-10 10:48:01.801  1116 20583 I netd    : setProcSysNet(6, 2, wlan0, retrans_time_ms, 750) <0.15ms>
08-10 10:48:01.801  1116 20583 I netd    : setProcSysNet(6, 2, wlan0, ucast_solicit, 10) <0.12ms>
08-10 10:48:01.986  1236  2320 I slpi    : STK3A5X ALS_OC: lux:80.519997, last_ps:1[1718], CH0:55, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:02.015  1707  2376 E WifiStaIfaceAidlImpl: getCachedScanData failed with service-specific exception: android.os.ServiceSpecificException:  (code 4)
08-10 10:48:02.579  2914 21087 I NearbyMediums: No BLE Fast/GATT advertisements found in the latest cycle.
08-10 10:48:02.581  2914 21088 I NearbyConnections: Processing lost BlePeripheral BlePeripheral{advertisement=BleAdvertisement { version=2, socketVersion=2, isFast=true, serviceIdHash=null, data=[ 0x23 0x4e 0x55 0x41 0x48 0x11 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ], deviceToken=[ 0x8f 0xcf ], rxAdvertisement=null }, dctAdvertisement=null, address=null, psm=0, deviceToken=<ByteString@ea53ecb size=2 contents="\217\317">, isSecondProfile=false, rxInstantConnectionAdv=null}.
08-10 10:48:02.582  2914 21088 I NearbyConnections: Lost BleEndpoint for BlePeripheral BlePeripheral{advertisement=BleAdvertisement { version=2, socketVersion=2, isFast=true, serviceIdHash=null, data=[ 0x23 0x4e 0x55 0x41 0x48 0x11 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ], deviceToken=[ 0x8f 0xcf ], rxAdvertisement=null }, dctAdvertisement=null, address=null, psm=0, deviceToken=<ByteString@ea53ecb size=2 contents="\217\317">, isSecondProfile=false, rxInstantConnectionAdv=null} (with EndpointId NUAH and EndpointInfo [ 0x32 0xc2 0xd4 0x00 0x98 0xaa 0xe3 0x0c 0x36 0xf9 0x7b 0xa1 0x0c 0xb8 0x89 0x8a 0xaf ]).
08-10 10:48:02.583  2914 21088 I NearbyConnections: ClientProxy(166113317) reporting onEndpointLost(NUAH)
08-10 10:48:02.585  2914 21089 I NearbySharing: NearbySharing Internal event EndpointLost(endpointId=NUAH)
08-10 10:48:02.813  1236  2320 I slpi    : STK3A5X ALS_OC: lux:89.304001, last_ps:1[1713], CH0:61, CH2:1, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:02.916  1236  2320 I slpi    : STK3A5X ALS_OC: lux:102.480003, last_ps:1[1715], CH0:70, CH2:2, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:03.017  1236  2320 I slpi    : STK3A5X ALS_OC: lux:114.192001, last_ps:1[1713], CH0:78, CH2:3, CH4:7, AWAIT:32, ACRL:33, AGAIN:33, INT1:9, INT2:0, PS:50, LED:128, flag:64, state:7, scale:183.976
08-10 10:48:03.024  9308  9351 D LOWI-********.y: [LOWI-Scan] wait_event:Wait done with Cmd 103
08-10 10:48:03.024  1244  5927 I WifiHAL : event received NL80211_CMD_VENDOR, vendor_id = 0x1374, subcmd = 0xd
08-10 10:48:03.024  9308  9351 D LOWI-********.y: [LOWI-Scan] do_listen_events: Rcvd valid Netlink Cmd 0 Err 0
08-10 10:48:03.025  1623  1623 I cnss-daemon: nl80211 response handler invoked
08-10 10:48:03.025  1623  1623 I cnss-daemon: nl80211_response_handler: cmd 103, vendorID 4980, subcmd 13  received
08-10 10:48:03.246 20518 20518 I sd 0    : 0:0:4: [sde] Synchronizing SCSI cache
08-10 10:48:03.249 20518 20518 I ufshpb_suspend: 3235 info: ufshpb_lu 4 goto suspend
08-10 10:48:03.249 20518 20518 I ufshpb_suspend: 3236 info: ufshpb_lu 4 changes suspend state
08-10 10:48:03.273   851   851 I logd    : logdr: UID=2000 GID=2000 PID=21092 n tail=0 logMask=99 pid=0 start=0ns deadline=0ns

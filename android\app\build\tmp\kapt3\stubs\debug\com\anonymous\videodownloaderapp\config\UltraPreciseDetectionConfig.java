package com.anonymous.videodownloaderapp.config;

/**
 * Configurações Centralizadas do Sistema de Detecção Ultra-Preciso
 * Todas as constantes, configurações e parâmetros do sistema
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\b\u0014\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0013\b\t\n\u000b\f\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014\u0015\u0016\u0017\u0018\u0019\u001aB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004J\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007\u00a8\u0006\u001b"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig;", "", "()V", "toMap", "", "", "validateConfiguration", "", "Cache", "Codecs", "Debouncing", "Development", "Experiments", "JavaScript", "Logging", "MobileLifecycle", "Network", "Optimization", "Performance", "Security", "SpecialCases", "Telemetry", "Timeouts", "VideoFormats", "VideoQuality", "WebView", "YouTube", "app_debug"})
public final class UltraPreciseDetectionConfig {
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig INSTANCE = null;
    
    private UltraPreciseDetectionConfig() {
        super();
    }
    
    /**
     * Validar configurações
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> validateConfiguration() {
        return null;
    }
    
    /**
     * Obter configuração como mapa
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> toMap() {
        return null;
    }
    
    /**
     * Configurações de Cache
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Cache;", "", "()V", "CACHE_CLEANUP_THRESHOLD", "", "CACHE_EVICTION_PERCENTAGE", "MANIFEST_CACHE_SIZE_MB", "", "MANIFEST_CACHE_TTL_MS", "", "METADATA_CACHE_SIZE_MB", "METADATA_CACHE_TTL_MS", "THUMBNAIL_CACHE_SIZE_MB", "THUMBNAIL_CACHE_TTL_MS", "VIDEO_CACHE_SIZE_MB", "VIDEO_CACHE_TTL_MS", "app_debug"})
    public static final class Cache {
        public static final int VIDEO_CACHE_SIZE_MB = 100;
        public static final int MANIFEST_CACHE_SIZE_MB = 20;
        public static final int THUMBNAIL_CACHE_SIZE_MB = 50;
        public static final int METADATA_CACHE_SIZE_MB = 10;
        public static final long VIDEO_CACHE_TTL_MS = 3600000L;
        public static final long MANIFEST_CACHE_TTL_MS = 1800000L;
        public static final long THUMBNAIL_CACHE_TTL_MS = 86400000L;
        public static final long METADATA_CACHE_TTL_MS = 7200000L;
        public static final double CACHE_CLEANUP_THRESHOLD = 0.8;
        public static final double CACHE_EVICTION_PERCENTAGE = 0.2;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Cache INSTANCE = null;
        
        private Cache() {
            super();
        }
    }
    
    /**
     * Configurações de Codecs
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\b\b\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00040\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00040\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\rR\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Codecs;", "", "()V", "AAC", "", "AV1", "H264", "H265", "MP3", "OPUS", "PREFERRED_AUDIO_CODECS", "", "getPREFERRED_AUDIO_CODECS", "()Ljava/util/List;", "PREFERRED_VIDEO_CODECS", "getPREFERRED_VIDEO_CODECS", "VORBIS", "VP8", "VP9", "app_debug"})
    public static final class Codecs {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String H264 = "h264";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String H265 = "h265";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VP8 = "vp8";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VP9 = "vp9";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AV1 = "av1";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AAC = "aac";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String MP3 = "mp3";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String OPUS = "opus";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VORBIS = "vorbis";
        @org.jetbrains.annotations.NotNull()
        private static final java.util.List<java.lang.String> PREFERRED_VIDEO_CODECS = null;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.List<java.lang.String> PREFERRED_AUDIO_CODECS = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Codecs INSTANCE = null;
        
        private Codecs() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getPREFERRED_VIDEO_CODECS() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getPREFERRED_AUDIO_CODECS() {
            return null;
        }
    }
    
    /**
     * Configurações de Debouncing
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Debouncing;", "", "()V", "DEFAULT_DEBOUNCE_MS", "", "HIGH_PRIORITY_DEBOUNCE_MS", "LOW_PRIORITY_DEBOUNCE_MS", "NAVIGATION_DEBOUNCE_MS", "RESIZE_DEBOUNCE_MS", "SCROLL_DEBOUNCE_MS", "YOUTUBE_DEBOUNCE_MS", "app_debug"})
    public static final class Debouncing {
        public static final long DEFAULT_DEBOUNCE_MS = 300L;
        public static final long HIGH_PRIORITY_DEBOUNCE_MS = 100L;
        public static final long LOW_PRIORITY_DEBOUNCE_MS = 500L;
        public static final long YOUTUBE_DEBOUNCE_MS = 200L;
        public static final long SCROLL_DEBOUNCE_MS = 150L;
        public static final long RESIZE_DEBOUNCE_MS = 250L;
        public static final long NAVIGATION_DEBOUNCE_MS = 500L;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Debouncing INSTANCE = null;
        
        private Debouncing() {
            super();
        }
    }
    
    /**
     * Configurações de Desenvolvimento
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Development;", "", "()V", "CRASH_REPORTING_ENABLED", "", "DEBUG_OVERLAY_ENABLED", "ENABLE_DEBUG_MODE", "ENABLE_MEMORY_LEAK_DETECTION", "ENABLE_MOCK_DATA", "ENABLE_PERFORMANCE_PROFILING", "MOCK_VIDEO_URLS", "", "", "getMOCK_VIDEO_URLS", "()Ljava/util/List;", "VERBOSE_LOGGING_ENABLED", "app_debug"})
    public static final class Development {
        public static final boolean ENABLE_DEBUG_MODE = false;
        public static final boolean ENABLE_MOCK_DATA = false;
        public static final boolean ENABLE_PERFORMANCE_PROFILING = false;
        public static final boolean ENABLE_MEMORY_LEAK_DETECTION = false;
        public static final boolean DEBUG_OVERLAY_ENABLED = false;
        public static final boolean VERBOSE_LOGGING_ENABLED = false;
        public static final boolean CRASH_REPORTING_ENABLED = true;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.List<java.lang.String> MOCK_VIDEO_URLS = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Development INSTANCE = null;
        
        private Development() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getMOCK_VIDEO_URLS() {
            return null;
        }
    }
    
    /**
     * Configurações de Experimentação
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0006\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Experiments;", "", "()V", "A_B_TEST_DURATION_MS", "", "ENABLE_ADVANCED_HLS_PARSING", "", "ENABLE_BETA_YOUTUBE_DETECTION", "ENABLE_EXPERIMENTAL_FEATURES", "ENABLE_MACHINE_LEARNING_SCORING", "ENABLE_PREDICTIVE_CACHING", "EXPERIMENT_SAMPLE_RATE", "", "app_debug"})
    public static final class Experiments {
        public static final boolean ENABLE_EXPERIMENTAL_FEATURES = false;
        public static final boolean ENABLE_BETA_YOUTUBE_DETECTION = true;
        public static final boolean ENABLE_ADVANCED_HLS_PARSING = true;
        public static final boolean ENABLE_MACHINE_LEARNING_SCORING = false;
        public static final boolean ENABLE_PREDICTIVE_CACHING = false;
        public static final double EXPERIMENT_SAMPLE_RATE = 0.1;
        public static final long A_B_TEST_DURATION_MS = 604800000L;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Experiments INSTANCE = null;
        
        private Experiments() {
            super();
        }
    }
    
    /**
     * Configurações de JavaScript
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\"\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u000e\u0010\t\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00040\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\bR\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$JavaScript;", "", "()V", "BATTERY_SAVER_SCRIPT", "", "CRITICAL_SCRIPTS", "", "getCRITICAL_SCRIPTS", "()Ljava/util/Set;", "EXECUTION_RETRY_COUNT", "", "EXECUTION_RETRY_DELAY_MS", "", "INJECTION_DELAY_MS", "OPTIONAL_SCRIPTS", "getOPTIONAL_SCRIPTS", "PERFORMANCE_SCRIPT", "VIDEO_DETECTOR_SCRIPT", "YOUTUBE_OPTIMIZER_SCRIPT", "app_debug"})
    public static final class JavaScript {
        public static final long INJECTION_DELAY_MS = 100L;
        public static final int EXECUTION_RETRY_COUNT = 3;
        public static final long EXECUTION_RETRY_DELAY_MS = 500L;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VIDEO_DETECTOR_SCRIPT = "video_detector_enhanced.js";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String PERFORMANCE_SCRIPT = "performance_monitor.js";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String BATTERY_SAVER_SCRIPT = "battery_saver.js";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String YOUTUBE_OPTIMIZER_SCRIPT = "youtube_optimizer.js";
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> CRITICAL_SCRIPTS = null;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> OPTIONAL_SCRIPTS = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.JavaScript INSTANCE = null;
        
        private JavaScript() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getCRITICAL_SCRIPTS() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getOPTIONAL_SCRIPTS() {
            return null;
        }
    }
    
    /**
     * Configurações de Logging
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Logging;", "", "()V", "ENABLE_DEBUG_LOGGING", "", "ENABLE_ERROR_LOGGING", "ENABLE_PERFORMANCE_LOGGING", "ENABLE_TELEMETRY_LOGGING", "LOG_ROTATION_INTERVAL_MS", "", "LOG_TAG_DETECTION", "", "LOG_TAG_INTEGRATION", "LOG_TAG_PERFORMANCE", "LOG_TAG_SYSTEM", "LOG_TAG_TELEMETRY", "MAX_LOG_FILES", "", "MAX_LOG_FILE_SIZE_MB", "app_debug"})
    public static final class Logging {
        public static final boolean ENABLE_DEBUG_LOGGING = true;
        public static final boolean ENABLE_PERFORMANCE_LOGGING = true;
        public static final boolean ENABLE_ERROR_LOGGING = true;
        public static final boolean ENABLE_TELEMETRY_LOGGING = false;
        public static final int MAX_LOG_FILE_SIZE_MB = 10;
        public static final int MAX_LOG_FILES = 5;
        public static final long LOG_ROTATION_INTERVAL_MS = 86400000L;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String LOG_TAG_SYSTEM = "UltraPreciseSystem";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String LOG_TAG_DETECTION = "VideoDetection";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String LOG_TAG_PERFORMANCE = "Performance";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String LOG_TAG_TELEMETRY = "Telemetry";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String LOG_TAG_INTEGRATION = "Integration";
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Logging INSTANCE = null;
        
        private Logging() {
            super();
        }
    }
    
    /**
     * Configurações de Lifecycle Mobile
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0002\b\u0005\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$MobileLifecycle;", "", "()V", "BATTERY_CRITICAL_THRESHOLD", "", "BATTERY_LOW_THRESHOLD", "MEMORY_CRITICAL_THRESHOLD_MB", "MEMORY_LOW_THRESHOLD_MB", "PERFORMANCE_DEGRADATION_THRESHOLD", "", "SCAN_INTERVAL_BACKGROUND_MS", "", "SCAN_INTERVAL_BATTERY_SAVER_MS", "SCAN_INTERVAL_LOW_MEMORY_MS", "SCAN_INTERVAL_NORMAL_MS", "THERMAL_THROTTLING_THRESHOLD", "app_debug"})
    public static final class MobileLifecycle {
        public static final int BATTERY_LOW_THRESHOLD = 20;
        public static final int BATTERY_CRITICAL_THRESHOLD = 10;
        public static final int MEMORY_LOW_THRESHOLD_MB = 100;
        public static final int MEMORY_CRITICAL_THRESHOLD_MB = 50;
        public static final long SCAN_INTERVAL_NORMAL_MS = 1000L;
        public static final long SCAN_INTERVAL_BATTERY_SAVER_MS = 2000L;
        public static final long SCAN_INTERVAL_LOW_MEMORY_MS = 1500L;
        public static final long SCAN_INTERVAL_BACKGROUND_MS = 5000L;
        public static final double PERFORMANCE_DEGRADATION_THRESHOLD = 0.7;
        public static final double THERMAL_THROTTLING_THRESHOLD = 0.8;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.MobileLifecycle INSTANCE = null;
        
        private MobileLifecycle() {
            super();
        }
    }
    
    /**
     * Configurações de Rede
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\b\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0007R\u000e\u0010\n\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\rX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\rX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\rX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Network;", "", "()V", "ALLOWED_PROTOCOLS", "", "", "getALLOWED_PROTOCOLS", "()Ljava/util/Set;", "BLOCKED_EXTENSIONS", "getBLOCKED_EXTENSIONS", "CONNECTION_TIMEOUT_MS", "", "MAX_MANIFEST_SIZE_MB", "", "MAX_REDIRECTS", "MAX_RESPONSE_SIZE_MB", "MAX_RETRIES", "READ_TIMEOUT_MS", "RETRY_DELAY_MS", "USER_AGENT", "WRITE_TIMEOUT_MS", "app_debug"})
    public static final class Network {
        public static final long CONNECTION_TIMEOUT_MS = 10000L;
        public static final long READ_TIMEOUT_MS = 15000L;
        public static final long WRITE_TIMEOUT_MS = 10000L;
        public static final int MAX_REDIRECTS = 5;
        public static final int MAX_RETRIES = 3;
        public static final long RETRY_DELAY_MS = 1000L;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String USER_AGENT = "UltraPreciseVideoDetector/1.0";
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> ALLOWED_PROTOCOLS = null;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> BLOCKED_EXTENSIONS = null;
        public static final int MAX_MANIFEST_SIZE_MB = 10;
        public static final int MAX_RESPONSE_SIZE_MB = 50;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Network INSTANCE = null;
        
        private Network() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getALLOWED_PROTOCOLS() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getBLOCKED_EXTENSIONS() {
            return null;
        }
    }
    
    /**
     * Configurações de Otimização
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Optimization;", "", "()V", "BATTERY_CHECK_INTERVAL_MS", "", "CACHE_CLEANUP_INTERVAL_MS", "LIFECYCLE_STATE_CHECK_INTERVAL_MS", "MEMORY_CLEANUP_INTERVAL_MS", "PERFORMANCE_MONITORING_INTERVAL_MS", "SYSTEM_OPTIMIZATION_INTERVAL_MS", "TELEMETRY_FLUSH_INTERVAL_MS", "app_debug"})
    public static final class Optimization {
        public static final long SYSTEM_OPTIMIZATION_INTERVAL_MS = 60000L;
        public static final long MEMORY_CLEANUP_INTERVAL_MS = 300000L;
        public static final long TELEMETRY_FLUSH_INTERVAL_MS = 120000L;
        public static final long CACHE_CLEANUP_INTERVAL_MS = 600000L;
        public static final long PERFORMANCE_MONITORING_INTERVAL_MS = 30000L;
        public static final long BATTERY_CHECK_INTERVAL_MS = 60000L;
        public static final long LIFECYCLE_STATE_CHECK_INTERVAL_MS = 5000L;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Optimization INSTANCE = null;
        
        private Optimization() {
            super();
        }
    }
    
    /**
     * Configurações de Performance
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0007\n\u0002\u0010\u0007\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Performance;", "", "()V", "CACHE_SIZE_LIMIT_MB", "", "CPU_USAGE_THRESHOLD_PERCENT", "", "MAX_CANDIDATES_PER_DETECTION", "MAX_CONCURRENT_DETECTIONS", "MAX_DETECTION_HISTORY", "MAX_JAVASCRIPT_HEAP_SIZE_MB", "MEMORY_CLEANUP_THRESHOLD_MB", "MIN_VIDEO_DURATION_SECONDS", "MIN_VIDEO_SCORE", "", "app_debug"})
    public static final class Performance {
        public static final int MAX_CONCURRENT_DETECTIONS = 3;
        public static final int MAX_CANDIDATES_PER_DETECTION = 20;
        public static final float MIN_VIDEO_SCORE = 5.0F;
        public static final double MIN_VIDEO_DURATION_SECONDS = 10.0;
        public static final int MAX_DETECTION_HISTORY = 50;
        public static final int MEMORY_CLEANUP_THRESHOLD_MB = 100;
        public static final double CPU_USAGE_THRESHOLD_PERCENT = 80.0;
        public static final int CACHE_SIZE_LIMIT_MB = 50;
        public static final int MAX_JAVASCRIPT_HEAP_SIZE_MB = 128;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Performance INSTANCE = null;
        
        private Performance() {
            super();
        }
    }
    
    /**
     * Configurações de Segurança
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0007R\u000e\u0010\n\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000fX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u000fX\u0086T\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015\u00a8\u0006\u0016"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Security;", "", "()V", "ALLOWED_SCHEMES", "", "", "getALLOWED_SCHEMES", "()Ljava/util/Set;", "BLOCKED_SCHEMES", "getBLOCKED_SCHEMES", "ENABLE_CONTENT_SECURITY_POLICY", "", "ENABLE_MIXED_CONTENT_BLOCKING", "ENABLE_SAFE_BROWSING", "MAX_METADATA_SIZE_KB", "", "MAX_TITLE_LENGTH", "MAX_URL_LENGTH", "SANITIZATION_PATTERNS", "", "getSANITIZATION_PATTERNS", "()Ljava/util/Map;", "app_debug"})
    public static final class Security {
        public static final boolean ENABLE_CONTENT_SECURITY_POLICY = true;
        public static final boolean ENABLE_MIXED_CONTENT_BLOCKING = true;
        public static final boolean ENABLE_SAFE_BROWSING = true;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> ALLOWED_SCHEMES = null;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> BLOCKED_SCHEMES = null;
        public static final int MAX_URL_LENGTH = 2048;
        public static final int MAX_TITLE_LENGTH = 200;
        public static final int MAX_METADATA_SIZE_KB = 10;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Map<java.lang.String, java.lang.String> SANITIZATION_PATTERNS = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Security INSTANCE = null;
        
        private Security() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getALLOWED_SCHEMES() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getBLOCKED_SCHEMES() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Map<java.lang.String, java.lang.String> getSANITIZATION_PATTERNS() {
            return null;
        }
    }
    
    /**
     * Configurações de Detecção de Casos Especiais
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u0011"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$SpecialCases;", "", "()V", "LIVE_STREAM_MANIFEST_UPDATE_INTERVAL_MS", "", "MIN_VIDEO_ELEMENT_SIZE_PX", "", "MIN_VISIBLE_AREA_PERCENTAGE", "", "OFFSCREEN_CHECK_INTERVAL_MS", "PIP_STATE_CHECK_INTERVAL_MS", "QUARANTINE_DURATION_MS", "QUARANTINE_REASONS", "", "", "getQUARANTINE_REASONS", "()Ljava/util/Set;", "app_debug"})
    public static final class SpecialCases {
        public static final long QUARANTINE_DURATION_MS = 300000L;
        public static final long OFFSCREEN_CHECK_INTERVAL_MS = 1000L;
        public static final long PIP_STATE_CHECK_INTERVAL_MS = 500L;
        public static final long LIVE_STREAM_MANIFEST_UPDATE_INTERVAL_MS = 30000L;
        public static final double MIN_VISIBLE_AREA_PERCENTAGE = 50.0;
        public static final int MIN_VIDEO_ELEMENT_SIZE_PX = 100;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> QUARANTINE_REASONS = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.SpecialCases INSTANCE = null;
        
        private SpecialCases() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getQUARANTINE_REASONS() {
            return null;
        }
    }
    
    /**
     * Configurações de Telemetria
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u0007R\u000e\u0010\u000e\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Telemetry;", "", "()V", "CRITICAL_METRICS", "", "", "getCRITICAL_METRICS", "()Ljava/util/Set;", "DATA_RETENTION_DAYS", "", "MAX_EVENTS_IN_MEMORY", "MAX_METRICS_IN_MEMORY", "OPTIONAL_METRICS", "getOPTIONAL_METRICS", "PERFORMANCE_METRIC_PRECISION", "PERSISTENCE_BATCH_SIZE", "REPORT_GENERATION_INTERVAL_MS", "", "TIMESTAMP_PRECISION_MS", "", "app_debug"})
    public static final class Telemetry {
        public static final int MAX_EVENTS_IN_MEMORY = 1000;
        public static final int MAX_METRICS_IN_MEMORY = 500;
        public static final int PERSISTENCE_BATCH_SIZE = 100;
        public static final int DATA_RETENTION_DAYS = 7;
        public static final long REPORT_GENERATION_INTERVAL_MS = 3600000L;
        public static final int PERFORMANCE_METRIC_PRECISION = 2;
        public static final boolean TIMESTAMP_PRECISION_MS = true;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> CRITICAL_METRICS = null;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> OPTIONAL_METRICS = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Telemetry INSTANCE = null;
        
        private Telemetry() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getCRITICAL_METRICS() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getOPTIONAL_METRICS() {
            return null;
        }
    }
    
    /**
     * Configurações de Timeout
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$Timeouts;", "", "()V", "DETECTION_TIMEOUT_MS", "", "JAVASCRIPT_EXECUTION_TIMEOUT_MS", "MANIFEST_PARSING_TIMEOUT_MS", "NETWORK_REQUEST_TIMEOUT_MS", "QUALITY_MAPPING_TIMEOUT_MS", "SPECIAL_CASES_TIMEOUT_MS", "WEBVIEW_CONFIGURATION_TIMEOUT_MS", "app_debug"})
    public static final class Timeouts {
        public static final long DETECTION_TIMEOUT_MS = 5000L;
        public static final long JAVASCRIPT_EXECUTION_TIMEOUT_MS = 3000L;
        public static final long QUALITY_MAPPING_TIMEOUT_MS = 1000L;
        public static final long SPECIAL_CASES_TIMEOUT_MS = 500L;
        public static final long WEBVIEW_CONFIGURATION_TIMEOUT_MS = 2000L;
        public static final long NETWORK_REQUEST_TIMEOUT_MS = 10000L;
        public static final long MANIFEST_PARSING_TIMEOUT_MS = 2000L;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.Timeouts INSTANCE = null;
        
        private Timeouts() {
            super();
        }
    }
    
    /**
     * Configurações de Formatos de Vídeo
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0010\"\n\u0002\b\b\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00040\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u000e\u0010\u0015\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$VideoFormats;", "", "()V", "AVI", "", "DASH", "FLV", "HLS", "M3U8", "MKV", "MOV", "MP4", "MPD", "PROGRESSIVE_FORMATS", "", "getPROGRESSIVE_FORMATS", "()Ljava/util/Set;", "STREAMING_FORMATS", "getSTREAMING_FORMATS", "SUPPORTED_FORMATS", "getSUPPORTED_FORMATS", "WEBM", "app_debug"})
    public static final class VideoFormats {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String MP4 = "mp4";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String WEBM = "webm";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String HLS = "hls";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String DASH = "dash";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String M3U8 = "m3u8";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String MPD = "mpd";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AVI = "avi";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String MOV = "mov";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String FLV = "flv";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String MKV = "mkv";
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> SUPPORTED_FORMATS = null;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> STREAMING_FORMATS = null;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> PROGRESSIVE_FORMATS = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.VideoFormats INSTANCE = null;
        
        private VideoFormats() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getSUPPORTED_FORMATS() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getSTREAMING_FORMATS() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getPROGRESSIVE_FORMATS() {
            return null;
        }
    }
    
    /**
     * Configurações de Qualidade de Vídeo
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010$\n\u0002\u0010\b\n\u0002\b\t\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u001d\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u001d\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00040\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$VideoQuality;", "", "()V", "AUTO", "", "FULL_HD", "HD", "LOW_SD", "MINIMUM", "QUAD_HD", "QUALITY_PRIORITIES", "", "", "getQUALITY_PRIORITIES", "()Ljava/util/Map;", "RESOLUTION_TO_QUALITY", "getRESOLUTION_TO_QUALITY", "SD", "ULTRA_HD_4K", "UNKNOWN", "VERY_LOW", "app_debug"})
    public static final class VideoQuality {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ULTRA_HD_4K = "2160p";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String QUAD_HD = "1440p";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String FULL_HD = "1080p";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String HD = "720p";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SD = "480p";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String LOW_SD = "360p";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String VERY_LOW = "240p";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String MINIMUM = "144p";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String AUTO = "Auto";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String UNKNOWN = "Unknown";
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Map<java.lang.String, java.lang.Integer> QUALITY_PRIORITIES = null;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Map<java.lang.String, java.lang.String> RESOLUTION_TO_QUALITY = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.VideoQuality INSTANCE = null;
        
        private VideoQuality() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Map<java.lang.String, java.lang.Integer> getQUALITY_PRIORITIES() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Map<java.lang.String, java.lang.String> getRESOLUTION_TO_QUALITY() {
            return null;
        }
    }
    
    /**
     * Configurações de WebView
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\t\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u000e\u0010\n\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\fX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$WebView;", "", "()V", "APPLICATION_CACHE_QUOTA_MB", "", "BLOCKED_DOMAINS", "", "", "getBLOCKED_DOMAINS", "()Ljava/util/Set;", "BRIDGE_CALLBACK_NAME", "BUILTIN_ZOOM_CONTROLS", "", "DISPLAY_ZOOM_CONTROLS", "DOM_STORAGE_QUOTA_MB", "ERROR_CALLBACK_NAME", "JAVASCRIPT_INTERFACE_NAME", "USER_AGENT_DESKTOP", "USER_AGENT_MOBILE", "WEB_SQL_DATABASE_QUOTA_MB", "ZOOM_CONTROLS_ENABLED", "app_debug"})
    public static final class WebView {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String USER_AGENT_DESKTOP = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String USER_AGENT_MOBILE = "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String JAVASCRIPT_INTERFACE_NAME = "UltraPreciseDetector";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String BRIDGE_CALLBACK_NAME = "onVideoDetected";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String ERROR_CALLBACK_NAME = "onDetectionError";
        public static final int DOM_STORAGE_QUOTA_MB = 50;
        public static final int WEB_SQL_DATABASE_QUOTA_MB = 50;
        public static final int APPLICATION_CACHE_QUOTA_MB = 50;
        public static final boolean ZOOM_CONTROLS_ENABLED = false;
        public static final boolean BUILTIN_ZOOM_CONTROLS = false;
        public static final boolean DISPLAY_ZOOM_CONTROLS = false;
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Set<java.lang.String> BLOCKED_DOMAINS = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.WebView INSTANCE = null;
        
        private WebView() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Set<java.lang.String> getBLOCKED_DOMAINS() {
            return null;
        }
    }
    
    /**
     * Configurações de Detecção YouTube
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\u0010\b\n\u0002\b\t\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00040\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/anonymous/videodownloaderapp/config/UltraPreciseDetectionConfig$YouTube;", "", "()V", "BASE_URL", "", "EMBED_URL", "GOOGLEVIDEO_DOMAIN", "ITAG_QUALITY_MAP", "", "", "getITAG_QUALITY_MAP", "()Ljava/util/Map;", "MOBILE_URL", "SHORT_URL", "STREAMING_DATA_PATTERN", "YOUTUBE_CDN_DOMAIN", "YTPLAYER_CONFIG_PATTERN", "YT_INITIAL_PLAYER_RESPONSE_PATTERN", "app_debug"})
    public static final class YouTube {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String BASE_URL = "youtube.com";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String SHORT_URL = "youtu.be";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String MOBILE_URL = "m.youtube.com";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String EMBED_URL = "youtube.com/embed";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String YTPLAYER_CONFIG_PATTERN = "ytplayer.config\\s*=\\s*({.+?});\\s*ytplayer";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String YT_INITIAL_PLAYER_RESPONSE_PATTERN = "ytInitialPlayerResponse\\s*=\\s*({.+?});\\s*var";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String STREAMING_DATA_PATTERN = "\"streamingData\":\\s*({.+?})(?=,\")";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String GOOGLEVIDEO_DOMAIN = "googlevideo.com";
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String YOUTUBE_CDN_DOMAIN = "ytimg.com";
        @org.jetbrains.annotations.NotNull()
        private static final java.util.Map<java.lang.Integer, java.lang.String> ITAG_QUALITY_MAP = null;
        @org.jetbrains.annotations.NotNull()
        public static final com.anonymous.videodownloaderapp.config.UltraPreciseDetectionConfig.YouTube INSTANCE = null;
        
        private YouTube() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Map<java.lang.Integer, java.lang.String> getITAG_QUALITY_MAP() {
            return null;
        }
    }
}
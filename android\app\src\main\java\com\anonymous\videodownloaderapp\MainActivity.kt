package com.anonymous.videodownloaderapp

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController

import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.anonymous.videodownloaderapp.features.player.VideoPlayerScreen
import com.anonymous.videodownloaderapp.features.browser.BrowserScreen
import com.anonymous.videodownloaderapp.features.downloads.DownloadsScreen
import com.anonymous.videodownloaderapp.features.settings.SettingsScreen
import androidx.compose.ui.platform.LocalContext

import com.anonymous.videodownloaderapp.services.DownloadTracker
import com.anonymous.videodownloaderapp.services.BackgroundDownloadService
import com.anonymous.videodownloaderapp.data.DownloadRepository

class MainActivity : ComponentActivity() {
    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Initialize services that need context
        val downloadTracker = DownloadTracker(applicationContext)
        

        setContent {
            App(downloadTracker)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun App(downloadTracker: DownloadTracker) {
    val context = LocalContext.current
    val navController = rememberNavController()
    val currentBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = currentBackStackEntry?.destination?.route

    Scaffold(
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = "browser",
            modifier = Modifier.padding(innerPadding)
        ) {
            composable("browser") { 
                BrowserScreen(
                    navController = navController,
                    downloadRepository = DownloadRepository.get(context)
                )
            }
            composable("downloads") { DownloadsScreen(navController) }
            composable("settings") { SettingsScreen() }
            composable(
                "videoPlayer/{videoUrl}",
                arguments = listOf(navArgument("videoUrl") { type = NavType.StringType })
            ) { backStackEntry ->
                VideoPlayerScreen(navController, backStackEntry.arguments?.getString("videoUrl") ?: "")
            }
        }
    }
}

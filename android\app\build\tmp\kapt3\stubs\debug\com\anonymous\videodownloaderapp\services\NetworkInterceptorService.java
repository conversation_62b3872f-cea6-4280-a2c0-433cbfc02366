package com.anonymous.videodownloaderapp.services;

/**
 * Interceptor OkHttp nativo que captura todos os requests de mídia antes mesmo 
 * que cheguem ao WebView. Substitui completamente a Performance API limitada por CORS.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000~\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010#\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0006\u0018\u0000 22\u00020\u0001:\u00042345B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u000eJ\u0018\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\tH\u0002J\u0010\u0010\u001c\u001a\u00020\u00162\b\b\u0002\u0010\u001d\u001a\u00020\u001eJ\u001a\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\b2\b\u0010\"\u001a\u0004\u0018\u00010\bH\u0002J\u0014\u0010#\u001a\u0004\u0018\u00010\b2\b\u0010$\u001a\u0004\u0018\u00010\bH\u0002J\u001e\u0010%\u001a\b\u0012\u0004\u0012\u00020\t0&2\u0006\u0010\'\u001a\u00020\b2\b\b\u0002\u0010\u001d\u001a\u00020\u001eJ\u0012\u0010(\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020*0)J\u0010\u0010+\u001a\u00020\u00192\u0006\u0010,\u001a\u00020-H\u0016J\u000e\u0010.\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u000eJ\u0018\u0010/\u001a\u0002002\u0006\u00101\u001a\u00020 2\u0006\u0010\u001a\u001a\u00020\u0019H\u0002R\u0016\u0010\u0003\u001a\n \u0005*\u0004\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n \u0005*\u0004\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000b\u001a\n \u0005*\u0004\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0013\u001a\n \u0005*\u0004\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0014\u001a\n \u0005*\u0004\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00066"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService;", "Lokhttp3/Interceptor;", "()V", "dashPattern", "Ljava/util/regex/Pattern;", "kotlin.jvm.PlatformType", "detectedRequests", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaRequest;", "googleVideoPattern", "hlsPattern", "listeners", "", "Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaRequestListener;", "requestCounter", "Ljava/util/concurrent/atomic/AtomicLong;", "scope", "Lkotlinx/coroutines/CoroutineScope;", "videoPattern", "videoPlaybackPattern", "addListener", "", "listener", "captureAndReturnResponse", "Lokhttp3/Response;", "response", "mediaRequest", "cleanup", "maxAge", "", "detectMediaType", "Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaType;", "url", "contentType", "extractFrameOrigin", "referer", "getRequestsByOrigin", "", "origin", "getStats", "", "", "intercept", "chain", "Lokhttp3/Interceptor$Chain;", "removeListener", "shouldCaptureContent", "", "mediaType", "Companion", "MediaRequest", "MediaRequestListener", "MediaType", "app_debug"})
public final class NetworkInterceptorService implements okhttp3.Interceptor {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.anonymous.videodownloaderapp.services.NetworkInterceptorService INSTANCE;
    private final java.util.regex.Pattern hlsPattern = null;
    private final java.util.regex.Pattern dashPattern = null;
    private final java.util.regex.Pattern videoPattern = null;
    private final java.util.regex.Pattern googleVideoPattern = null;
    private final java.util.regex.Pattern videoPlaybackPattern = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequest> detectedRequests = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicLong requestCounter = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope scope = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequestListener> listeners = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.services.NetworkInterceptorService.Companion Companion = null;
    
    private NetworkInterceptorService() {
        super();
    }
    
    public final void addListener(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequestListener listener) {
    }
    
    public final void removeListener(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequestListener listener) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public okhttp3.Response intercept(@org.jetbrains.annotations.NotNull()
    okhttp3.Interceptor.Chain chain) {
        return null;
    }
    
    private final com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaType detectMediaType(java.lang.String url, java.lang.String contentType) {
        return null;
    }
    
    private final boolean shouldCaptureContent(com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaType mediaType, okhttp3.Response response) {
        return false;
    }
    
    private final okhttp3.Response captureAndReturnResponse(okhttp3.Response response, com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequest mediaRequest) {
        return null;
    }
    
    private final java.lang.String extractFrameOrigin(java.lang.String referer) {
        return null;
    }
    
    /**
     * Obter requests detectados por frame/origem para priorizar conteúdo do player visível
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequest> getRequestsByOrigin(@org.jetbrains.annotations.NotNull()
    java.lang.String origin, long maxAge) {
        return null;
    }
    
    /**
     * Limpar requests antigos para evitar vazamento de memória
     */
    public final void cleanup(long maxAge) {
    }
    
    /**
     * Obter estatísticas de requests para debugging
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getStats() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$Companion;", "", "()V", "INSTANCE", "Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkInterceptorService getInstance() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u001e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Ba\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\b\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u000b\u001a\u00020\b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010$\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0011J\u000b\u0010%\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010\'\u001a\u00020\bH\u00c6\u0003J\t\u0010(\u001a\u00020\rH\u00c6\u0003J|\u0010)\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u000b\u001a\u00020\b2\b\b\u0002\u0010\f\u001a\u00020\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010*J\u0013\u0010+\u001a\u00020,2\b\u0010-\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010.\u001a\u00020/H\u00d6\u0001J\t\u00100\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0014R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0014R\u0013\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0014R\u0011\u0010\u000b\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0014R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0014\u00a8\u00061"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaRequest;", "", "id", "", "url", "method", "contentType", "contentLength", "", "userAgent", "referer", "timestamp", "mediaType", "Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaType;", "frameOrigin", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;JLcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaType;Ljava/lang/String;)V", "getContentLength", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getContentType", "()Ljava/lang/String;", "getFrameOrigin", "getId", "getMediaType", "()Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaType;", "getMethod", "getReferer", "getTimestamp", "()J", "getUrl", "getUserAgent", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;JLcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaType;Ljava/lang/String;)Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaRequest;", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    public static final class MediaRequest {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String id = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String url = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String method = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String contentType = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.Long contentLength = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String userAgent = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String referer = null;
        private final long timestamp = 0L;
        @org.jetbrains.annotations.NotNull()
        private final com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaType mediaType = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String frameOrigin = null;
        
        public MediaRequest(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.NotNull()
        java.lang.String method, @org.jetbrains.annotations.Nullable()
        java.lang.String contentType, @org.jetbrains.annotations.Nullable()
        java.lang.Long contentLength, @org.jetbrains.annotations.Nullable()
        java.lang.String userAgent, @org.jetbrains.annotations.Nullable()
        java.lang.String referer, long timestamp, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaType mediaType, @org.jetbrains.annotations.Nullable()
        java.lang.String frameOrigin) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMethod() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getContentType() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Long getContentLength() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getUserAgent() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getReferer() {
            return null;
        }
        
        public final long getTimestamp() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaType getMediaType() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getFrameOrigin() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component10() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Long component5() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component6() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component7() {
            return null;
        }
        
        public final long component8() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaType component9() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequest copy(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.NotNull()
        java.lang.String method, @org.jetbrains.annotations.Nullable()
        java.lang.String contentType, @org.jetbrains.annotations.Nullable()
        java.lang.Long contentLength, @org.jetbrains.annotations.Nullable()
        java.lang.String userAgent, @org.jetbrains.annotations.Nullable()
        java.lang.String referer, long timestamp, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaType mediaType, @org.jetbrains.annotations.Nullable()
        java.lang.String frameOrigin) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\u0010\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\t"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaRequestListener;", "", "onManifestContentAvailable", "", "request", "Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaRequest;", "content", "", "onMediaRequestDetected", "app_debug"})
    public static abstract interface MediaRequestListener {
        
        public abstract void onMediaRequestDetected(@org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequest request);
        
        public abstract void onManifestContentAvailable(@org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaRequest request, @org.jetbrains.annotations.NotNull()
        java.lang.String content);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkInterceptorService$MediaType;", "", "(Ljava/lang/String;I)V", "HLS_MASTER", "HLS_SEGMENT", "DASH_MANIFEST", "DASH_SEGMENT", "PROGRESSIVE_VIDEO", "GOOGLE_VIDEO", "UNKNOWN", "app_debug"})
    public static enum MediaType {
        /*public static final*/ HLS_MASTER /* = new HLS_MASTER() */,
        /*public static final*/ HLS_SEGMENT /* = new HLS_SEGMENT() */,
        /*public static final*/ DASH_MANIFEST /* = new DASH_MANIFEST() */,
        /*public static final*/ DASH_SEGMENT /* = new DASH_SEGMENT() */,
        /*public static final*/ PROGRESSIVE_VIDEO /* = new PROGRESSIVE_VIDEO() */,
        /*public static final*/ GOOGLE_VIDEO /* = new GOOGLE_VIDEO() */,
        /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
        
        MediaType() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.anonymous.videodownloaderapp.services.NetworkInterceptorService.MediaType> getEntries() {
            return null;
        }
    }
}
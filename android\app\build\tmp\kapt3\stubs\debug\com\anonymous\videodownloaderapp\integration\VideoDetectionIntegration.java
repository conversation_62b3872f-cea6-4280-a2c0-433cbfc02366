package com.anonymous.videodownloaderapp.integration;

/**
 * Integração do Sistema de Detecção Ultra-Preciso
 * Conecta o sistema avançado com a UI e componentes existentes
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00be\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010#\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010$\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 n2\u00020\u0001:\u0005nopqrB\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001a\u0010)\u001a\u00020\u00172\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00170\u0016J\u001a\u0010+\u001a\u00020\u00172\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00170\u0016J\u001a\u0010,\u001a\u00020\u00172\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00170\u0016J\u0006\u0010-\u001a\u00020\u0017J\u0006\u0010.\u001a\u00020\u0017J\u0018\u0010/\u001a\u00020\u00072\u0006\u00100\u001a\u0002012\u0006\u00102\u001a\u00020\fH\u0002J>\u00103\u001a\u0004\u0018\u00010\u00072\u0006\u00104\u001a\u00020\f2\u0006\u00102\u001a\u00020\f2\b\b\u0002\u00105\u001a\u0002062\b\b\u0002\u00107\u001a\u0002062\b\b\u0002\u00108\u001a\u000209H\u0086@\u00a2\u0006\u0002\u0010:J&\u0010;\u001a\u0004\u0018\u00010\f2\u0006\u0010<\u001a\u00020\f2\u0012\u0010=\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010>H\u0002J\u0012\u0010?\u001a\u0004\u0018\u00010\f2\u0006\u0010<\u001a\u00020\fH\u0002J\u0010\u0010@\u001a\u00020\f2\u0006\u0010A\u001a\u00020BH\u0002J\u0019\u0010C\u001a\u0004\u0018\u00010\f2\b\u0010D\u001a\u0004\u0018\u00010EH\u0002\u00a2\u0006\u0002\u0010FJ#\u0010G\u001a\u0004\u0018\u00010\f2\b\u0010H\u001a\u0004\u0018\u00010B2\b\u0010D\u001a\u0004\u0018\u00010EH\u0002\u00a2\u0006\u0002\u0010IJ\u0012\u0010J\u001a\u0004\u0018\u00010\f2\u0006\u0010<\u001a\u00020\fH\u0002J\f\u0010K\u001a\b\u0012\u0004\u0012\u00020\u00070LJ\u0012\u0010M\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010>J\u0010\u0010N\u001a\u00020\u00172\u0006\u0010O\u001a\u00020\fH\u0002J\u0010\u0010P\u001a\u00020\u00172\u0006\u0010Q\u001a\u00020\tH\u0002J\u0010\u0010R\u001a\u00020\u00172\u0006\u0010S\u001a\u00020\u0007H\u0002J\u0016\u0010T\u001a\u00020\u00172\u0006\u0010S\u001a\u000201H\u0082@\u00a2\u0006\u0002\u0010UJ\u001a\u0010V\u001a\u00020\u00172\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00170\u0016J\u000e\u0010W\u001a\u00020\u00172\u0006\u00104\u001a\u00020\fJ\u0006\u0010X\u001a\u00020\u0017J\b\u0010Y\u001a\u00020\u0017H\u0002J\b\u0010Z\u001a\u00020\u0017H\u0002J(\u0010[\u001a\u0002062\u0006\u0010\\\u001a\u00020\r2\u0006\u00104\u001a\u00020\f2\b\b\u0002\u0010]\u001a\u00020^H\u0086@\u00a2\u0006\u0002\u0010_J\u0018\u0010`\u001a\u00020\u00172\u0006\u0010\\\u001a\u00020\r2\u0006\u00104\u001a\u00020\fH\u0002J\u0006\u0010a\u001a\u00020\u0017J!\u0010b\u001a\u00020\u00172\u0017\u0010c\u001a\u0013\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\t0\u0016\u00a2\u0006\u0002\bdH\u0002J\u0010\u0010e\u001a\u00020\u00172\u0006\u0010f\u001a\u00020gH\u0002J\u0010\u0010h\u001a\u00020\u00172\u0006\u0010i\u001a\u00020jH\u0002J \u0010k\u001a\u00020\u00172\u0006\u0010S\u001a\u00020\u00072\b\b\u0002\u0010l\u001a\u00020\fH\u0082@\u00a2\u0006\u0002\u0010mR\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\r0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0010\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R \u0010\u0014\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00170\u00160\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\t0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\u001c\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00170\u00160\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\u001d\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00170\u00160\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u001e\u001a\u00020\u001f8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\"\u0010#\u001a\u0004\b \u0010!R\u001b\u0010$\u001a\u00020%8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b(\u0010#\u001a\u0004\b&\u0010\'\u00a8\u0006s"}, d2 = {"Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_detectionResults", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$VideoDetectionResult;", "_integrationState", "Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$IntegrationState;", "activeWebViews", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Landroid/webkit/WebView;", "detectionHistory", "", "detectionResults", "Lkotlinx/coroutines/flow/StateFlow;", "getDetectionResults", "()Lkotlinx/coroutines/flow/StateFlow;", "errorCallbacks", "", "Lkotlin/Function1;", "", "integrationState", "getIntegrationState", "scope", "Lkotlinx/coroutines/CoroutineScope;", "stateCallbacks", "uiCallbacks", "ultraPreciseSystem", "Lcom/anonymous/videodownloaderapp/services/UltraPreciseVideoDetectionSystem;", "getUltraPreciseSystem", "()Lcom/anonymous/videodownloaderapp/services/UltraPreciseVideoDetectionSystem;", "ultraPreciseSystem$delegate", "Lkotlin/Lazy;", "videoDetectionStore", "Lcom/anonymous/videodownloaderapp/features/browser/VideoDetectionStore;", "getVideoDetectionStore", "()Lcom/anonymous/videodownloaderapp/features/browser/VideoDetectionStore;", "videoDetectionStore$delegate", "addErrorCallback", "callback", "addStateCallback", "addUICallback", "cleanup", "clearDetectionHistory", "convertToIntegrationResult", "systemResult", "Lcom/anonymous/videodownloaderapp/services/UltraPreciseVideoDetectionSystem$DetectionResult;", "pageUrl", "detectVideosInCurrentPage", "webViewId", "enableYouTubeOptimization", "", "enableSpecialCases", "priority", "Lcom/anonymous/videodownloaderapp/services/OptimizedJavaScriptBridge$Priority;", "(Ljava/lang/String;Ljava/lang/String;ZZLcom/anonymous/videodownloaderapp/services/OptimizedJavaScriptBridge$Priority;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "extractThumbnail", "url", "metadata", "", "extractYouTubeVideoId", "formatBytes", "bytes", "", "formatDuration", "duration", "", "(Ljava/lang/Double;)Ljava/lang/String;", "formatFileSize", "bitrate", "(Ljava/lang/Long;Ljava/lang/Double;)Ljava/lang/String;", "generateThumbnailFromUrl", "getDetectionHistory", "", "getIntegrationStatistics", "notifyError", "message", "notifyStateCallbacks", "state", "notifyUICallbacks", "result", "processDetectionResult", "(Lcom/anonymous/videodownloaderapp/services/UltraPreciseVideoDetectionSystem$DetectionResult;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeUICallback", "removeWebView", "resume", "setupIntegration", "setupSystemCallbacks", "setupWebViewForDetection", "webView", "optimizationProfile", "Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$ConfigurationProfile;", "(Landroid/webkit/WebView;Ljava/lang/String;Lcom/anonymous/videodownloaderapp/services/WebViewConfigurationManager$ConfigurationProfile;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setupWebViewNavigation", "suspend", "updateIntegrationState", "update", "Lkotlin/ExtensionFunctionType;", "updatePerformanceMetrics", "metrics", "Lcom/anonymous/videodownloaderapp/services/UltraPreciseVideoDetectionSystem$DetectionPerformanceMetrics;", "updateSystemStateInIntegration", "systemState", "Lcom/anonymous/videodownloaderapp/services/UltraPreciseVideoDetectionSystem$SystemState;", "updateVideoDetectionStore", "tabId", "(Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$VideoDetectionResult;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "DetectedVideo", "IntegrationState", "PerformanceSnapshot", "VideoDetectionResult", "app_debug"})
public final class VideoDetectionIntegration {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration instance;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy ultraPreciseSystem$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy videoDetectionStore$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.IntegrationState> _integrationState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.IntegrationState> integrationState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult> _detectionResults = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult> detectionResults = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope scope = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, android.webkit.WebView> activeWebViews = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult> detectionHistory = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<kotlin.jvm.functions.Function1<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult, kotlin.Unit>> uiCallbacks = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<kotlin.jvm.functions.Function1<java.lang.String, kotlin.Unit>> errorCallbacks = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<kotlin.jvm.functions.Function1<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.IntegrationState, kotlin.Unit>> stateCallbacks = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.Companion Companion = null;
    
    private VideoDetectionIntegration(android.content.Context context) {
        super();
    }
    
    private final com.anonymous.videodownloaderapp.services.UltraPreciseVideoDetectionSystem getUltraPreciseSystem() {
        return null;
    }
    
    private final com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore getVideoDetectionStore() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.IntegrationState> getIntegrationState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult> getDetectionResults() {
        return null;
    }
    
    /**
     * Configurar integração
     */
    private final void setupIntegration() {
    }
    
    /**
     * Configurar callbacks do sistema
     */
    private final void setupSystemCallbacks() {
    }
    
    /**
     * Configurar WebView para detecção
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setupWebViewForDetection(@org.jetbrains.annotations.NotNull()
    android.webkit.WebView webView, @org.jetbrains.annotations.NotNull()
    java.lang.String webViewId, @org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.services.WebViewConfigurationManager.ConfigurationProfile optimizationProfile, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Configurar navegação da WebView
     */
    private final void setupWebViewNavigation(android.webkit.WebView webView, java.lang.String webViewId) {
    }
    
    /**
     * Detectar vídeos na página atual
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object detectVideosInCurrentPage(@org.jetbrains.annotations.NotNull()
    java.lang.String webViewId, @org.jetbrains.annotations.NotNull()
    java.lang.String pageUrl, boolean enableYouTubeOptimization, boolean enableSpecialCases, @org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.services.OptimizedJavaScriptBridge.Priority priority, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult> $completion) {
        return null;
    }
    
    /**
     * Processar resultado de detecção
     */
    private final java.lang.Object processDetectionResult(com.anonymous.videodownloaderapp.services.UltraPreciseVideoDetectionSystem.DetectionResult result, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Converter resultado do sistema para formato de integração
     */
    private final com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult convertToIntegrationResult(com.anonymous.videodownloaderapp.services.UltraPreciseVideoDetectionSystem.DetectionResult systemResult, java.lang.String pageUrl) {
        return null;
    }
    
    /**
     * Atualizar store de detecção de vídeo
     */
    private final java.lang.Object updateVideoDetectionStore(com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult result, java.lang.String tabId, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Atualizar métricas de performance
     */
    private final void updatePerformanceMetrics(com.anonymous.videodownloaderapp.services.UltraPreciseVideoDetectionSystem.DetectionPerformanceMetrics metrics) {
    }
    
    /**
     * Atualizar estado do sistema na integração
     */
    private final void updateSystemStateInIntegration(com.anonymous.videodownloaderapp.services.UltraPreciseVideoDetectionSystem.SystemState systemState) {
    }
    
    /**
     * Atualizar estado da integração
     */
    private final void updateIntegrationState(kotlin.jvm.functions.Function1<? super com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.IntegrationState, com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.IntegrationState> update) {
    }
    
    /**
     * Formatar duração
     */
    private final java.lang.String formatDuration(java.lang.Double duration) {
        return null;
    }
    
    /**
     * Extrair thumbnail
     */
    private final java.lang.String extractThumbnail(java.lang.String url, java.util.Map<java.lang.String, ? extends java.lang.Object> metadata) {
        return null;
    }
    
    /**
     * Gerar thumbnail da URL
     */
    private final java.lang.String generateThumbnailFromUrl(java.lang.String url) {
        return null;
    }
    
    /**
     * Extrair ID do vídeo do YouTube
     */
    private final java.lang.String extractYouTubeVideoId(java.lang.String url) {
        return null;
    }
    
    /**
     * Formatar tamanho do arquivo
     */
    private final java.lang.String formatFileSize(java.lang.Long bitrate, java.lang.Double duration) {
        return null;
    }
    
    /**
     * Formatar bytes
     */
    private final java.lang.String formatBytes(long bytes) {
        return null;
    }
    
    /**
     * Obter histórico de detecções
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult> getDetectionHistory() {
        return null;
    }
    
    /**
     * Obter estatísticas da integração
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getIntegrationStatistics() {
        return null;
    }
    
    /**
     * Limpar histórico
     */
    public final void clearDetectionHistory() {
    }
    
    /**
     * Remover WebView
     */
    public final void removeWebView(@org.jetbrains.annotations.NotNull()
    java.lang.String webViewId) {
    }
    
    /**
     * Adicionar callback de UI
     */
    public final void addUICallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult, kotlin.Unit> callback) {
    }
    
    /**
     * Remover callback de UI
     */
    public final void removeUICallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult, kotlin.Unit> callback) {
    }
    
    /**
     * Adicionar callback de erro
     */
    public final void addErrorCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * Adicionar callback de estado
     */
    public final void addStateCallback(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.IntegrationState, kotlin.Unit> callback) {
    }
    
    /**
     * Notificar callbacks de UI
     */
    private final void notifyUICallbacks(com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult result) {
    }
    
    /**
     * Notificar erro
     */
    private final void notifyError(java.lang.String message) {
    }
    
    /**
     * Notificar callbacks de estado
     */
    private final void notifyStateCallbacks(com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.IntegrationState state) {
    }
    
    /**
     * Suspender integração
     */
    public final void suspend() {
    }
    
    /**
     * Retomar integração
     */
    public final void resume() {
    }
    
    /**
     * Limpeza de recursos
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$Companion;", "", "()V", "instance", "Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration;", "getInstance", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010$\n\u0002\b\u001c\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bi\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0014\b\u0002\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u000f\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\u0015\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u000fH\u00c6\u0003J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u000bH\u00c6\u0003J\t\u0010\'\u001a\u00020\rH\u00c6\u0003J\u007f\u0010(\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\r2\u0014\b\u0002\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u000fH\u00c6\u0001J\u0013\u0010)\u001a\u00020\u000b2\b\u0010*\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010+\u001a\u00020,H\u00d6\u0001J\t\u0010-\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0015R\u001d\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0012R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0013\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0012R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0012\u00a8\u0006."}, d2 = {"Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$DetectedVideo;", "", "url", "", "title", "quality", "format", "duration", "thumbnail", "fileSize", "isLive", "", "score", "", "metadata", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZFLjava/util/Map;)V", "getDuration", "()Ljava/lang/String;", "getFileSize", "getFormat", "()Z", "getMetadata", "()Ljava/util/Map;", "getQuality", "getScore", "()F", "getThumbnail", "getTitle", "getUrl", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class DetectedVideo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String url = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String title = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String quality = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String format = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String duration = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String thumbnail = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String fileSize = null;
        private final boolean isLive = false;
        private final float score = 0.0F;
        @org.jetbrains.annotations.NotNull()
        private final java.util.Map<java.lang.String, java.lang.Object> metadata = null;
        
        public DetectedVideo(@org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.NotNull()
        java.lang.String title, @org.jetbrains.annotations.NotNull()
        java.lang.String quality, @org.jetbrains.annotations.NotNull()
        java.lang.String format, @org.jetbrains.annotations.Nullable()
        java.lang.String duration, @org.jetbrains.annotations.Nullable()
        java.lang.String thumbnail, @org.jetbrains.annotations.Nullable()
        java.lang.String fileSize, boolean isLive, float score, @org.jetbrains.annotations.NotNull()
        java.util.Map<java.lang.String, ? extends java.lang.Object> metadata) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getTitle() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getQuality() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getFormat() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getDuration() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getThumbnail() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getFileSize() {
            return null;
        }
        
        public final boolean isLive() {
            return false;
        }
        
        public final float getScore() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Map<java.lang.String, java.lang.Object> getMetadata() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Map<java.lang.String, java.lang.Object> component10() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component5() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component6() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component7() {
            return null;
        }
        
        public final boolean component8() {
            return false;
        }
        
        public final float component9() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.DetectedVideo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.NotNull()
        java.lang.String title, @org.jetbrains.annotations.NotNull()
        java.lang.String quality, @org.jetbrains.annotations.NotNull()
        java.lang.String format, @org.jetbrains.annotations.Nullable()
        java.lang.String duration, @org.jetbrains.annotations.Nullable()
        java.lang.String thumbnail, @org.jetbrains.annotations.Nullable()
        java.lang.String fileSize, boolean isLive, float score, @org.jetbrains.annotations.NotNull()
        java.util.Map<java.lang.String, ? extends java.lang.Object> metadata) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0019\b\u0086\b\u0018\u00002\u00020\u0001BS\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\nH\u00c6\u0003J\u000f\u0010 \u001a\b\u0012\u0004\u0012\u00020\n0\fH\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003JW\u0010\"\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00c6\u0001J\u0013\u0010#\u001a\u00020\u00032\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020\bH\u00d6\u0001J\t\u0010&\u001a\u00020\nH\u00d6\u0001R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0014R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006\'"}, d2 = {"Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$IntegrationState;", "", "isSystemReady", "", "isDetecting", "lastDetectionTime", "", "totalVideosDetected", "", "currentPageUrl", "", "detectionErrors", "", "performanceMetrics", "Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$PerformanceSnapshot;", "(ZZJILjava/lang/String;Ljava/util/List;Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$PerformanceSnapshot;)V", "getCurrentPageUrl", "()Ljava/lang/String;", "getDetectionErrors", "()Ljava/util/List;", "()Z", "getLastDetectionTime", "()J", "getPerformanceMetrics", "()Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$PerformanceSnapshot;", "getTotalVideosDetected", "()I", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class IntegrationState {
        private final boolean isSystemReady = false;
        private final boolean isDetecting = false;
        private final long lastDetectionTime = 0L;
        private final int totalVideosDetected = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String currentPageUrl = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.String> detectionErrors = null;
        @org.jetbrains.annotations.Nullable()
        private final com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.PerformanceSnapshot performanceMetrics = null;
        
        public IntegrationState(boolean isSystemReady, boolean isDetecting, long lastDetectionTime, int totalVideosDetected, @org.jetbrains.annotations.NotNull()
        java.lang.String currentPageUrl, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> detectionErrors, @org.jetbrains.annotations.Nullable()
        com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.PerformanceSnapshot performanceMetrics) {
            super();
        }
        
        public final boolean isSystemReady() {
            return false;
        }
        
        public final boolean isDetecting() {
            return false;
        }
        
        public final long getLastDetectionTime() {
            return 0L;
        }
        
        public final int getTotalVideosDetected() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getCurrentPageUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getDetectionErrors() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.PerformanceSnapshot getPerformanceMetrics() {
            return null;
        }
        
        public IntegrationState() {
            super();
        }
        
        public final boolean component1() {
            return false;
        }
        
        public final boolean component2() {
            return false;
        }
        
        public final long component3() {
            return 0L;
        }
        
        public final int component4() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> component6() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.PerformanceSnapshot component7() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.IntegrationState copy(boolean isSystemReady, boolean isDetecting, long lastDetectionTime, int totalVideosDetected, @org.jetbrains.annotations.NotNull()
        java.lang.String currentPageUrl, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> detectionErrors, @org.jetbrains.annotations.Nullable()
        com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.PerformanceSnapshot performanceMetrics) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0014\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0006H\u00c6\u0003JE\u0010\u0019\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\u0006H\u00c6\u0001J\u0013\u0010\u001a\u001a\u00020\u001b2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001d\u001a\u00020\u001eH\u00d6\u0001J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\fR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\fR\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010\u00a8\u0006!"}, d2 = {"Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$PerformanceSnapshot;", "", "averageDetectionTime", "", "successRate", "memoryUsage", "", "cpuUsage", "cacheHitRate", "timestamp", "(DDJDDJ)V", "getAverageDetectionTime", "()D", "getCacheHitRate", "getCpuUsage", "getMemoryUsage", "()J", "getSuccessRate", "getTimestamp", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
    public static final class PerformanceSnapshot {
        private final double averageDetectionTime = 0.0;
        private final double successRate = 0.0;
        private final long memoryUsage = 0L;
        private final double cpuUsage = 0.0;
        private final double cacheHitRate = 0.0;
        private final long timestamp = 0L;
        
        public PerformanceSnapshot(double averageDetectionTime, double successRate, long memoryUsage, double cpuUsage, double cacheHitRate, long timestamp) {
            super();
        }
        
        public final double getAverageDetectionTime() {
            return 0.0;
        }
        
        public final double getSuccessRate() {
            return 0.0;
        }
        
        public final long getMemoryUsage() {
            return 0L;
        }
        
        public final double getCpuUsage() {
            return 0.0;
        }
        
        public final double getCacheHitRate() {
            return 0.0;
        }
        
        public final long getTimestamp() {
            return 0L;
        }
        
        public final double component1() {
            return 0.0;
        }
        
        public final double component2() {
            return 0.0;
        }
        
        public final long component3() {
            return 0L;
        }
        
        public final double component4() {
            return 0.0;
        }
        
        public final double component5() {
            return 0.0;
        }
        
        public final long component6() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.PerformanceSnapshot copy(double averageDetectionTime, double successRate, long memoryUsage, double cpuUsage, double cacheHitRate, long timestamp) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0014\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B9\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\u0002\u0010\fJ\u000f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\bH\u00c6\u0003J\t\u0010\u0019\u001a\u00020\nH\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003JG\u0010\u001b\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0001J\u0013\u0010\u001c\u001a\u00020\n2\b\u0010\u001d\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001J\t\u0010 \u001a\u00020\u0006H\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014\u00a8\u0006!"}, d2 = {"Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$VideoDetectionResult;", "", "videos", "", "Lcom/anonymous/videodownloaderapp/integration/VideoDetectionIntegration$DetectedVideo;", "source", "", "processingTime", "", "qualityEnhanced", "", "specialCasesHandled", "(Ljava/util/List;Ljava/lang/String;JZLjava/util/List;)V", "getProcessingTime", "()J", "getQualityEnhanced", "()Z", "getSource", "()Ljava/lang/String;", "getSpecialCasesHandled", "()Ljava/util/List;", "getVideos", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class VideoDetectionResult {
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.DetectedVideo> videos = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String source = null;
        private final long processingTime = 0L;
        private final boolean qualityEnhanced = false;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.String> specialCasesHandled = null;
        
        public VideoDetectionResult(@org.jetbrains.annotations.NotNull()
        java.util.List<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.DetectedVideo> videos, @org.jetbrains.annotations.NotNull()
        java.lang.String source, long processingTime, boolean qualityEnhanced, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> specialCasesHandled) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.DetectedVideo> getVideos() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSource() {
            return null;
        }
        
        public final long getProcessingTime() {
            return 0L;
        }
        
        public final boolean getQualityEnhanced() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getSpecialCasesHandled() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.DetectedVideo> component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        public final long component3() {
            return 0L;
        }
        
        public final boolean component4() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.VideoDetectionResult copy(@org.jetbrains.annotations.NotNull()
        java.util.List<com.anonymous.videodownloaderapp.integration.VideoDetectionIntegration.DetectedVideo> videos, @org.jetbrains.annotations.NotNull()
        java.lang.String source, long processingTime, boolean qualityEnhanced, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> specialCasesHandled) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}
plugins {
    id "com.android.application"
    id "org.jetbrains.kotlin.android"
    id "org.jetbrains.kotlin.kapt"
}

android {
    namespace "com.anonymous.videodownloaderapp"
    compileSdk 34

    defaultConfig {
        applicationId "com.anonymous.videodownloaderapp"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        kapt {
            arguments {
                arg("room.schemaLocation", "$projectDir/schemas".toString())
            }
        }
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            signingConfig signingConfigs.debug
            minifyEnabled true
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
        }
    }

    buildFeatures {
        compose true
        buildConfig true
    }

    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.10"
    }

    packagingOptions {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
}

dependencies {
    // Compose BOM
    implementation platform("androidx.compose:compose-bom:2024.10.01")
    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.ui:ui-tooling-preview"
    // Material3 core and window size classes
    implementation "androidx.compose.material3:material3"
    implementation "androidx.compose.material3:material3-window-size-class"
    // Optional extended icons if needed
    implementation "androidx.compose.material:material-icons-extended"
    debugImplementation "androidx.compose.ui:ui-tooling"
    implementation "androidx.activity:activity-compose:1.9.2"
    implementation "androidx.navigation:navigation-compose:2.8.0"

    // Material Components library (provides Material theme resources referenced in XML)
    implementation "com.google.android.material:material:1.12.0"
    // AppCompat (optional but useful if any appcompat widgets/attrs are referenced)
    implementation "androidx.appcompat:appcompat:1.7.0"

    // Kotlin coroutines
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.1"

    // Room
    implementation "androidx.room:room-runtime:2.6.1"
    implementation "androidx.room:room-ktx:2.6.1"
    kapt "androidx.room:room-compiler:2.6.1"

    // WorkManager
    implementation "androidx.work:work-runtime-ktx:2.9.1"
    
    // Accompanist SwipeRefresh
    implementation "com.google.accompanist:accompanist-swiperefresh:0.32.0"
    implementation "com.squareup.okhttp3:okhttp:4.12.0"
    implementation "com.squareup.okio:okio:3.9.0"

    // Media3 (pin explicit versions instead of BOM to avoid repository BOM lookup issues)
    implementation "androidx.media3:media3-exoplayer:1.4.1"
    implementation "androidx.media3:media3-ui:1.4.1"
    implementation "androidx.media3:media3-exoplayer-hls:1.4.1"
    implementation "androidx.media3:media3-exoplayer-dash:1.4.1"
    implementation "androidx.media3:media3-datasource-okhttp:1.4.1"
    implementation "androidx.media3:media3-database:1.4.1"
    implementation "androidx.media3:media3-datasource:1.4.1"
    implementation "androidx.media3:media3-common:1.4.1"
    implementation "androidx.media3:media3-datasource-rtmp:1.4.1"

    // AndroidX core + lifecycle
    implementation "androidx.core:core-ktx:1.13.1"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.8.4"
    implementation "androidx.lifecycle:lifecycle-service:2.8.4"

    // DataStore
    implementation "androidx.datastore:datastore-preferences:1.1.1"
    implementation "androidx.datastore:datastore-core:1.1.1"

    // Test (optional baseline)
    testImplementation "junit:junit:4.13.2"
    androidTestImplementation "androidx.test.ext:junit:1.2.1"
    androidTestImplementation "androidx.test.espresso:espresso-core:3.6.1"
    implementation "com.google.code.gson:gson:2.10.1"

    // Accompanist SwipeRefresh for pull-to-refresh
    implementation "com.google.accompanist:accompanist-swiperefresh:0.35.1-alpha"
}

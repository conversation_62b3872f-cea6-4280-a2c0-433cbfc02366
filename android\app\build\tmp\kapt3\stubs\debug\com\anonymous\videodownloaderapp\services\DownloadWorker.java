package com.anonymous.videodownloaderapp.services;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 .2\u00020\u0001:\u0001.B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0082@\u00a2\u0006\u0002\u0010\u001cJ(\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020$H\u0002J\u000e\u0010%\u001a\u00020&H\u0096@\u00a2\u0006\u0002\u0010\'J\u001e\u0010(\u001a\u00020&2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010)\u001a\u00020 H\u0082@\u00a2\u0006\u0002\u0010*J\b\u0010+\u001a\u00020$H\u0002J\u0010\u0010,\u001a\u00020\u00192\u0006\u0010-\u001a\u00020 H\u0002R\u001b\u0010\u0007\u001a\u00020\b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000b\u0010\f\u001a\u0004\b\t\u0010\nR\u000e\u0010\r\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u000e\u001a\u00020\u000f8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0012\u0010\f\u001a\u0004\b\u0010\u0010\u0011R\u001b\u0010\u0013\u001a\u00020\u00148BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0017\u0010\f\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006/"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/DownloadWorker;", "Landroidx/work/CoroutineWorker;", "appContext", "Landroid/content/Context;", "params", "Landroidx/work/WorkerParameters;", "(Landroid/content/Context;Landroidx/work/WorkerParameters;)V", "client", "Lokhttp3/OkHttpClient;", "getClient", "()Lokhttp3/OkHttpClient;", "client$delegate", "Lkotlin/Lazy;", "context", "repo", "Lcom/anonymous/videodownloaderapp/data/DownloadRepository;", "getRepo", "()Lcom/anonymous/videodownloaderapp/data/DownloadRepository;", "repo$delegate", "settings", "Lcom/anonymous/videodownloaderapp/datastore/SettingsStore;", "getSettings", "()Lcom/anonymous/videodownloaderapp/datastore/SettingsStore;", "settings$delegate", "cancelDownload", "", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createForegroundInfo", "Landroidx/work/ForegroundInfo;", "title", "", "progress", "", "isPaused", "", "doWork", "Landroidx/work/ListenableWorker$Result;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "failWith", "message", "(JLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isOnWifi", "notifyCompleted", "displayName", "Companion", "app_debug"})
public final class DownloadWorker extends androidx.work.CoroutineWorker {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy repo$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy settings$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy client$delegate = null;
    private static final int DEFAULT_BUFFER = 65536;
    private static final int NOTIFICATION_COMPLETE_BASE = 4000;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KEY_ID = "key_id";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KEY_URL = "key_url";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KEY_TITLE = "key_title";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KEY_MIME = "key_mime";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KEY_PROGRESS = "key_progress";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KEY_URI = "key_uri";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KEY_BYTES = "key_bytes";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TAG_DOWNLOAD = "download_task";
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.services.DownloadWorker.Companion Companion = null;
    
    public DownloadWorker(@org.jetbrains.annotations.NotNull()
    android.content.Context appContext, @org.jetbrains.annotations.NotNull()
    androidx.work.WorkerParameters params) {
        super(null, null);
    }
    
    private final com.anonymous.videodownloaderapp.data.DownloadRepository getRepo() {
        return null;
    }
    
    private final com.anonymous.videodownloaderapp.datastore.SettingsStore getSettings() {
        return null;
    }
    
    private final okhttp3.OkHttpClient getClient() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object doWork(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super androidx.work.ListenableWorker.Result> $completion) {
        return null;
    }
    
    private final boolean isOnWifi() {
        return false;
    }
    
    private final void notifyCompleted(java.lang.String displayName) {
    }
    
    private final java.lang.Object failWith(long id, java.lang.String message, kotlin.coroutines.Continuation<? super androidx.work.ListenableWorker.Result> $completion) {
        return null;
    }
    
    private final java.lang.Object cancelDownload(long id, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final androidx.work.ForegroundInfo createForegroundInfo(long id, java.lang.String title, int progress, boolean isPaused) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J?\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00062\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0017\u00a2\u0006\u0002\u0010\u0018R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0019"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/DownloadWorker$Companion;", "", "()V", "DEFAULT_BUFFER", "", "KEY_BYTES", "", "KEY_ID", "KEY_MIME", "KEY_PROGRESS", "KEY_TITLE", "KEY_URI", "KEY_URL", "NOTIFICATION_COMPLETE_BASE", "TAG_DOWNLOAD", "enqueue", "Landroidx/work/Operation;", "context", "Landroid/content/Context;", "url", "title", "mimeType", "wifiOnly", "", "(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;)Landroidx/work/Operation;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.work.Operation enqueue(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.Nullable()
        java.lang.String title, @org.jetbrains.annotations.Nullable()
        java.lang.String mimeType, @org.jetbrains.annotations.Nullable()
        java.lang.Boolean wifiOnly) {
            return null;
        }
    }
}
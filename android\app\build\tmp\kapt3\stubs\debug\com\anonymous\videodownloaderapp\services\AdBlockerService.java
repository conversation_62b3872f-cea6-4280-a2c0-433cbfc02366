package com.anonymous.videodownloaderapp.services;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u0000 \n2\u00020\u0001:\u0001\nB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b\u00a8\u0006\u000b"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/AdBlockerService;", "", "()V", "createBlockedResponse", "Landroid/webkit/WebResourceResponse;", "shouldBlockRequest", "", "request", "Landroid/webkit/WebResourceRequest;", "shouldBlockTracker", "Companion", "app_debug"})
public final class AdBlockerService {
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Set<java.lang.String> adDomains = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.util.regex.Pattern> adPatterns = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Set<java.lang.String> adResourceTypes = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.services.AdBlockerService.Companion Companion = null;
    
    public AdBlockerService() {
        super();
    }
    
    /**
     * Verifica se uma requisição deve ser bloqueada
     */
    public final boolean shouldBlockRequest(@org.jetbrains.annotations.NotNull()
    android.webkit.WebResourceRequest request) {
        return false;
    }
    
    /**
     * Cria uma resposta vazia para requisições bloqueadas
     */
    @org.jetbrains.annotations.NotNull()
    public final android.webkit.WebResourceResponse createBlockedResponse() {
        return null;
    }
    
    /**
     * Verifica se deve bloquear trackers de privacidade
     */
    public final boolean shouldBlockTracker(@org.jetbrains.annotations.NotNull()
    android.webkit.WebResourceRequest request) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0006\u001a\u0010\u0012\f\u0012\n \t*\u0004\u0018\u00010\b0\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/AdBlockerService$Companion;", "", "()V", "adDomains", "", "", "adPatterns", "", "Ljava/util/regex/Pattern;", "kotlin.jvm.PlatformType", "adResourceTypes", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}
package com.anonymous.videodownloaderapp.cache;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000b\u0018\u00002\u00020\u0001:\u0003,-.B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\t2\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0013J\u0016\u0010\u0015\u001a\u00020\u00102\u0006\u0010\u0016\u001a\u00020\t2\u0006\u0010\u0017\u001a\u00020\u0006J.\u0010\u0018\u001a\u00020\u00102\u0006\u0010\u0019\u001a\u00020\t2\u0006\u0010\u001a\u001a\u00020\t2\u0006\u0010\u001b\u001a\u00020\t2\u0006\u0010\u001c\u001a\u00020\u00132\u0006\u0010\u001d\u001a\u00020\tJ\u0010\u0010\u001e\u001a\u00020\u00102\b\b\u0002\u0010\u001f\u001a\u00020 J\u0010\u0010!\u001a\u00020\u00102\b\b\u0002\u0010\"\u001a\u00020 J\u0006\u0010#\u001a\u00020$J\u0010\u0010%\u001a\u0004\u0018\u00010\n2\u0006\u0010\u0011\u001a\u00020\tJ\u0010\u0010&\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0016\u001a\u00020\tJ\u0010\u0010\'\u001a\u0004\u0018\u00010\u000e2\u0006\u0010\u0019\u001a\u00020\tJ\u0012\u0010(\u001a\u0004\u0018\u00010\u000e2\u0006\u0010)\u001a\u00020\tH\u0002J\u0018\u0010*\u001a\u00020\u00102\u0006\u0010)\u001a\u00020\t2\u0006\u0010+\u001a\u00020\u000eH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00060\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u000e0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006/"}, d2 = {"Lcom/anonymous/videodownloaderapp/cache/DownloadCacheManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "diskCacheDir", "Ljava/io/File;", "progressCache", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/anonymous/videodownloaderapp/cache/DownloadCacheManager$DownloadProgress;", "thumbnailCache", "Landroid/util/LruCache;", "urlCache", "Lcom/anonymous/videodownloaderapp/cache/DownloadCacheManager$CachedUrlData;", "cacheProgress", "", "downloadId", "bytesDownloaded", "", "totalBytes", "cacheThumbnail", "videoId", "thumbnailFile", "cacheUrlData", "originalUrl", "resolvedUrl", "fileName", "fileSize", "mimeType", "clearCacheBySize", "maxSizeMB", "", "clearExpiredCache", "maxAgeHours", "getCacheStats", "Lcom/anonymous/videodownloaderapp/cache/DownloadCacheManager$CacheStats;", "getCachedProgress", "getCachedThumbnail", "getCachedUrlData", "loadFromDisk", "key", "saveToDisk", "data", "CacheStats", "CachedUrlData", "DownloadProgress", "app_debug"})
public final class DownloadCacheManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final android.util.LruCache<java.lang.String, com.anonymous.videodownloaderapp.cache.DownloadCacheManager.CachedUrlData> urlCache = null;
    @org.jetbrains.annotations.NotNull()
    private final android.util.LruCache<java.lang.String, java.io.File> thumbnailCache = null;
    @org.jetbrains.annotations.NotNull()
    private final java.io.File diskCacheDir = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.anonymous.videodownloaderapp.cache.DownloadCacheManager.DownloadProgress> progressCache = null;
    
    public DownloadCacheManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    public final void cacheUrlData(@org.jetbrains.annotations.NotNull()
    java.lang.String originalUrl, @org.jetbrains.annotations.NotNull()
    java.lang.String resolvedUrl, @org.jetbrains.annotations.NotNull()
    java.lang.String fileName, long fileSize, @org.jetbrains.annotations.NotNull()
    java.lang.String mimeType) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.anonymous.videodownloaderapp.cache.DownloadCacheManager.CachedUrlData getCachedUrlData(@org.jetbrains.annotations.NotNull()
    java.lang.String originalUrl) {
        return null;
    }
    
    public final void cacheThumbnail(@org.jetbrains.annotations.NotNull()
    java.lang.String videoId, @org.jetbrains.annotations.NotNull()
    java.io.File thumbnailFile) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.io.File getCachedThumbnail(@org.jetbrains.annotations.NotNull()
    java.lang.String videoId) {
        return null;
    }
    
    public final void cacheProgress(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId, long bytesDownloaded, long totalBytes) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.anonymous.videodownloaderapp.cache.DownloadCacheManager.DownloadProgress getCachedProgress(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId) {
        return null;
    }
    
    public final void clearExpiredCache(int maxAgeHours) {
    }
    
    public final void clearCacheBySize(int maxSizeMB) {
    }
    
    private final void saveToDisk(java.lang.String key, com.anonymous.videodownloaderapp.cache.DownloadCacheManager.CachedUrlData data) {
    }
    
    private final com.anonymous.videodownloaderapp.cache.DownloadCacheManager.CachedUrlData loadFromDisk(java.lang.String key) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.anonymous.videodownloaderapp.cache.DownloadCacheManager.CacheStats getCacheStats() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0007H\u00c6\u0003J1\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\nR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\n\u00a8\u0006\u001a"}, d2 = {"Lcom/anonymous/videodownloaderapp/cache/DownloadCacheManager$CacheStats;", "", "urlCacheSize", "", "thumbnailCacheSize", "diskCacheSize", "diskCacheTotalSize", "", "(IIIJ)V", "getDiskCacheSize", "()I", "getDiskCacheTotalSize", "()J", "getThumbnailCacheSize", "getUrlCacheSize", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
    public static final class CacheStats {
        private final int urlCacheSize = 0;
        private final int thumbnailCacheSize = 0;
        private final int diskCacheSize = 0;
        private final long diskCacheTotalSize = 0L;
        
        public CacheStats(int urlCacheSize, int thumbnailCacheSize, int diskCacheSize, long diskCacheTotalSize) {
            super();
        }
        
        public final int getUrlCacheSize() {
            return 0;
        }
        
        public final int getThumbnailCacheSize() {
            return 0;
        }
        
        public final int getDiskCacheSize() {
            return 0;
        }
        
        public final long getDiskCacheTotalSize() {
            return 0L;
        }
        
        public final int component1() {
            return 0;
        }
        
        public final int component2() {
            return 0;
        }
        
        public final int component3() {
            return 0;
        }
        
        public final long component4() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.cache.DownloadCacheManager.CacheStats copy(int urlCacheSize, int thumbnailCacheSize, int diskCacheSize, long diskCacheTotalSize) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0011\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B/\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0006H\u00c6\u0003J;\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u0006H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000bR\u0011\u0010\b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\r\u00a8\u0006\u001d"}, d2 = {"Lcom/anonymous/videodownloaderapp/cache/DownloadCacheManager$CachedUrlData;", "", "resolvedUrl", "", "fileName", "fileSize", "", "mimeType", "timestamp", "(Ljava/lang/String;Ljava/lang/String;JLjava/lang/String;J)V", "getFileName", "()Ljava/lang/String;", "getFileSize", "()J", "getMimeType", "getResolvedUrl", "getTimestamp", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    public static final class CachedUrlData {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String resolvedUrl = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String fileName = null;
        private final long fileSize = 0L;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String mimeType = null;
        private final long timestamp = 0L;
        
        public CachedUrlData(@org.jetbrains.annotations.NotNull()
        java.lang.String resolvedUrl, @org.jetbrains.annotations.NotNull()
        java.lang.String fileName, long fileSize, @org.jetbrains.annotations.NotNull()
        java.lang.String mimeType, long timestamp) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getResolvedUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getFileName() {
            return null;
        }
        
        public final long getFileSize() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMimeType() {
            return null;
        }
        
        public final long getTimestamp() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        public final long component3() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component4() {
            return null;
        }
        
        public final long component5() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.cache.DownloadCacheManager.CachedUrlData copy(@org.jetbrains.annotations.NotNull()
        java.lang.String resolvedUrl, @org.jetbrains.annotations.NotNull()
        java.lang.String fileName, long fileSize, @org.jetbrains.annotations.NotNull()
        java.lang.String mimeType, long timestamp) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\'\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\bR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\b\u00a8\u0006\u0016"}, d2 = {"Lcom/anonymous/videodownloaderapp/cache/DownloadCacheManager$DownloadProgress;", "", "bytesDownloaded", "", "totalBytes", "lastUpdated", "(JJJ)V", "getBytesDownloaded", "()J", "getLastUpdated", "getTotalBytes", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
    public static final class DownloadProgress {
        private final long bytesDownloaded = 0L;
        private final long totalBytes = 0L;
        private final long lastUpdated = 0L;
        
        public DownloadProgress(long bytesDownloaded, long totalBytes, long lastUpdated) {
            super();
        }
        
        public final long getBytesDownloaded() {
            return 0L;
        }
        
        public final long getTotalBytes() {
            return 0L;
        }
        
        public final long getLastUpdated() {
            return 0L;
        }
        
        public final long component1() {
            return 0L;
        }
        
        public final long component2() {
            return 0L;
        }
        
        public final long component3() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.cache.DownloadCacheManager.DownloadProgress copy(long bytesDownloaded, long totalBytes, long lastUpdated) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}
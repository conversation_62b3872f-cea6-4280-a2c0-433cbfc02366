<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Network and media permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- Foreground service permissions (Android 12+) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <application
        android:name=".MainApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.App"
        android:hardwareAccelerated="true"
        tools:targetApi="33">

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.App">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- BroadcastReceiver for Notification actions (pause/resume/cancel) -->
        <receiver
            android:name=".services.DownloadActionReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.anonymous.videodownloaderapp.action.PAUSE" />
                <action android:name="com.anonymous.videodownloaderapp.action.RESUME" />
                <action android:name="com.anonymous.videodownloaderapp.action.CANCEL" />
            </intent-filter>
        </receiver>

        <!-- No Android Service components added here to avoid behavior changes.
             EnhancedDownloadService is a helper class, not an Android Service. -->

    </application>

</manifest>
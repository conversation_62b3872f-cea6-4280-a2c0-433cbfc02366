package com.anonymous.videodownloaderapp.features.browser

object BrowserConstants {
    // URLs
    const val DEFAULT_URL = "https://www.google.com"
    const val SEARCH_URL_TEMPLATE = "https://www.google.com/search?q=%s"
    
    // Timeouts e delays
    const val INITIAL_VIDEO_DETECTION_DELAY_MS = 3000L
    const val SECONDARY_VIDEO_DETECTION_DELAY_MS = 6000L
    const val REFRESH_DETECTION_DELAY_MS = 1000L
    
    // UI
    const val ADDRESS_BAR_MAX_DISPLAY_LENGTH = 30
    const val ADDRESS_BAR_COMPACT_HEIGHT_DP = 40
    const val ADDRESS_BAR_EXPANDED_HEIGHT_DP = 56
    const val ADDRESS_BAR_ANIMATION_DURATION_MS = 300
    
    // WebView settings
    const val WEBVIEW_CACHE_SIZE_MB = 100
    const val WEBVIEW_USER_AGENT_SUFFIX = " VideoDownloader/1.0"
    
    // Video detection
    const val MAX_DETECTED_VIDEOS = 50
    const val VIDEO_URL_MIN_LENGTH = 10
    
    // Download
    const val DEFAULT_VIDEO_TITLE = "Vídeo"
    const val DEFAULT_VIDEO_MIME_TYPE = "video/mp4"
    
    // JavaScript interface
    const val JS_INTERFACE_NAME = "VideoDetector"
    
    // Asset files
    const val VIDEO_DETECTOR_SCRIPT_PATH = "video_detector_enhanced.js"
}
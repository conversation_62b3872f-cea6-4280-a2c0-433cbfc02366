package com.anonymous.videodownloaderapp.features.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.anonymous.videodownloaderapp.datastore.SettingsStore
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen() {
    val context = LocalContext.current
    val store = remember { SettingsStore.get(context) }
    val scope = rememberCoroutineScope()

    val settings by store.settingsFlow.collectAsState(initial = SettingsStore.Settings())

    // Estados das configurações (now bound to persistent store)
    var wifiOnly by remember(settings.wifiOnly) { mutableStateOf(settings.wifiOnly) }
    var maxParallel by remember(settings.concurrencyLimit) { mutableStateOf(settings.concurrencyLimit.toString()) }
    var usePip by remember(settings.enablePip) { mutableStateOf(settings.enablePip) }

    var autoDownload by remember(settings.autoDownload) { mutableStateOf(settings.autoDownload) }
    var downloadPath by remember(settings.downloadPathFlag) { mutableStateOf("/storage/emulated/0/Download/VideoDownloader") } // placeholder
    // Map indexes to labels
    val videoQualityOptions = listOf("Auto", "2160p", "1080p", "720p", "480p", "360p")
    val audioQualityOptions = listOf("High", "Medium", "Low")
    var videoQualityIndex by remember(settings.videoQualityIndex) { mutableStateOf(settings.videoQualityIndex) }
    var audioQualityIndex by remember(settings.audioQualityIndex) { mutableStateOf(settings.audioQualityIndex) }

    var darkMode by remember(settings.darkMode) { mutableStateOf(settings.darkMode) }
    var showThumbnails by remember { mutableStateOf(true) } // UI only for now
    var enableNotifications by remember(settings.notifications) { mutableStateOf(settings.notifications) }
    var enableVibration by remember(settings.vibration) { mutableStateOf(settings.vibration) }
    var enableFastDownload by remember(settings.fastDownload) { mutableStateOf(settings.fastDownload) }
    var enableAdBlock by remember(settings.adBlock) { mutableStateOf(settings.adBlock) }
    var enableJavaScript by remember(settings.javascript) { mutableStateOf(settings.javascript) }
    var enableCookies by remember(settings.cookies) { mutableStateOf(settings.cookies) }
    var enableCache by remember(settings.cache) { mutableStateOf(settings.cache) }
    var maxCacheSize by remember(settings.maxCacheMb) { mutableStateOf(settings.maxCacheMb.toString()) }
    var enableAutoUpdate by remember(settings.autoUpdate) { mutableStateOf(settings.autoUpdate) }
    var enableAnalytics by remember(settings.analytics) { mutableStateOf(settings.analytics) }
    var enableBackgroundDownload by remember(settings.backgroundDownload) { mutableStateOf(settings.backgroundDownload) }
    var maxRetries by remember(settings.maxRetries) { mutableStateOf(settings.maxRetries.toString()) }
    var connectionTimeout by remember(settings.connectTimeoutS) { mutableStateOf(settings.connectTimeoutS.toString()) }
    var readTimeout by remember(settings.readTimeoutS) { mutableStateOf(settings.readTimeoutS.toString()) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Configurações", fontWeight = FontWeight.Bold) },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        }
    ) { inner ->
        LazyColumn(
            modifier = Modifier
                .padding(inner)
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Seção Download
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "⬇ Download",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Spacer(Modifier.height(12.dp))

                        SettingRow(
                            icon = Icons.Default.Wifi,
                            title = "Somente Wi-Fi",
                            subtitle = "Baixar apenas quando conectado ao Wi-Fi",
                            checked = wifiOnly,
                            onCheckedChange = { wifiOnly = it }
                        )

                        SettingRow(
                            icon = Icons.Default.Download,
                            title = "Download Automático",
                            subtitle = "Iniciar download automaticamente ao detectar vídeo",
                            checked = autoDownload,
                            onCheckedChange = {
                                autoDownload = it
                                scope.launch { store.setAutoDownload(it) }
                            }
                        )

                        SettingRow(
                            icon = Icons.Default.Speed,
                            title = "Download Rápido",
                            subtitle = "Usar múltiplas conexões para acelerar downloads",
                            checked = enableFastDownload,
                            onCheckedChange = {
                                enableFastDownload = it
                                scope.launch { store.setFastDownload(it) }
                            }
                        )

                        SettingRow(
                            icon = Icons.Default.PlayArrow,
                            title = "Download em Background",
                            subtitle = "Continuar downloads quando o app estiver fechado",
                            checked = enableBackgroundDownload,
                            onCheckedChange = {
                                enableBackgroundDownload = it
                                scope.launch { store.setBackgroundDownload(it) }
                            }
                        )

                        OutlinedTextField(
                            value = maxParallel,
                            onValueChange = { v -> maxParallel = v.filter { c -> c.isDigit() }.take(2) },
                            label = { Text("Downloads Simultâneos") },
                            supportingText = { Text("Máximo: 20") },
                            leadingIcon = { Icon(Icons.Default.Queue, null) },
                            modifier = Modifier.fillMaxWidth()
                        )

                        OutlinedTextField(
                            value = downloadPath,
                            onValueChange = { downloadPath = it },
                            label = { Text("Pasta de Download") },
                            leadingIcon = { Icon(Icons.Default.Folder, null) },
                            modifier = Modifier.fillMaxWidth(),
                            trailingIcon = {
                                // Placeholder action; in future this should launch SAF picker and persist UriPermission
                                IconButton(onClick = { /* open folder picker */ }) {
                                    Icon(Icons.Default.Edit, null)
                                }
                            }
                        )

                        OutlinedTextField(
                            value = maxRetries,
                            onValueChange = { v -> maxRetries = v.filter { c -> c.isDigit() }.take(1) },
                            label = { Text("Tentativas Máximas") },
                            supportingText = { Text("Número de tentativas em caso de falha") },
                            leadingIcon = { Icon(Icons.Default.Refresh, null) },
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }

            // Seção Qualidade
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "🎥 Qualidade",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Spacer(Modifier.height(12.dp))

                        QualitySelector(
                            title = "Qualidade de Vídeo Padrão",
                            options = videoQualityOptions,
                            selected = videoQualityOptions.getOrElse(videoQualityIndex) { "Auto" },
                            onSelectionChange = { choice ->
                                val idx = videoQualityOptions.indexOf(choice).coerceAtLeast(0)
                                videoQualityIndex = idx
                                scope.launch { store.setVideoQualityIndex(idx) }
                            }
                        )

                        Spacer(Modifier.height(8.dp))

                        QualitySelector(
                            title = "Qualidade de Áudio Padrão",
                            options = audioQualityOptions,
                            selected = audioQualityOptions.getOrElse(audioQualityIndex) { "High" },
                            onSelectionChange = { choice ->
                                val idx = audioQualityOptions.indexOf(choice).coerceAtLeast(0)
                                audioQualityIndex = idx
                                scope.launch { store.setAudioQualityIndex(idx) }
                            }
                        )
                    }
                }
            }

            // Seção Interface
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "🎨 Interface",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Spacer(Modifier.height(12.dp))

                        SettingRow(
                            icon = Icons.Default.DarkMode,
                            title = "Modo Escuro",
                            subtitle = "Usar tema escuro",
                            checked = darkMode,
                            onCheckedChange = {
                                darkMode = it
                                scope.launch { store.setDarkMode(it) }
                            }
                        )

                        SettingRow(
                            icon = Icons.Default.Image,
                            title = "Mostrar Miniaturas",
                            subtitle = "Exibir thumbnails dos vídeos",
                            checked = showThumbnails,
                            onCheckedChange = { showThumbnails = it }
                        )

                        SettingRow(
                            icon = Icons.Default.PictureInPicture,
                            title = "Picture-in-Picture",
                            subtitle = "Reproduzir vídeos em janela flutuante",
                            checked = usePip,
                            onCheckedChange = {
                                usePip = it
                                scope.launch { store.setEnablePip(it) }
                            }
                        )
                    }
                }
            }

            // Seção Navegador
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "🌐 Navegador",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Spacer(Modifier.height(12.dp))

                        SettingRow(
                            icon = Icons.Default.Block,
                            title = "Bloquear Anúncios",
                            subtitle = "Bloquear publicidade nas páginas web",
                            checked = enableAdBlock,
                            onCheckedChange = {
                                enableAdBlock = it
                                scope.launch { store.setAdBlock(it) }
                            }
                        )

                        SettingRow(
                            icon = Icons.Default.Code,
                            title = "JavaScript",
                            subtitle = "Permitir execução de JavaScript",
                            checked = enableJavaScript,
                            onCheckedChange = {
                                enableJavaScript = it
                                scope.launch { store.setJavascript(it) }
                            }
                        )

                        SettingRow(
                            icon = Icons.Default.Cookie,
                            title = "Cookies",
                            subtitle = "Aceitar cookies dos websites",
                            checked = enableCookies,
                            onCheckedChange = {
                                enableCookies = it
                                scope.launch { store.setCookies(it) }
                            }
                        )

                        SettingRow(
                            icon = Icons.Default.Storage,
                            title = "Cache do Navegador",
                            subtitle = "Armazenar dados temporários para carregamento rápido",
                            checked = enableCache,
                            onCheckedChange = {
                                enableCache = it
                                scope.launch { store.setCache(it) }
                            }
                        )

                        OutlinedTextField(
                            value = maxCacheSize,
                            onValueChange = { v -> maxCacheSize = v.filter { c -> c.isDigit() }.take(4) },
                            label = { Text("Tamanho Máximo do Cache (MB)") },
                            leadingIcon = { Icon(Icons.Default.Storage, null) },
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }

            // Seção Notificações
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "🔔 Notificações",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Spacer(Modifier.height(12.dp))

                        SettingRow(
                            icon = Icons.Default.Notifications,
                            title = "Notificações",
                            subtitle = "Mostrar notificações de progresso",
                            checked = enableNotifications,
                            onCheckedChange = {
                                enableNotifications = it
                                scope.launch { store.setNotifications(it) }
                            }
                        )

                        SettingRow(
                            icon = Icons.Default.Vibration,
                            title = "Vibração",
                            subtitle = "Vibrar ao completar downloads",
                            checked = enableVibration,
                            onCheckedChange = {
                                enableVibration = it
                                scope.launch { store.setVibration(it) }
                            }
                        )
                    }
                }
            }

            // Seção Rede
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "🌐 Rede",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Spacer(Modifier.height(12.dp))

                        OutlinedTextField(
                            value = connectionTimeout,
                            onValueChange = { v -> connectionTimeout = v.filter { c -> c.isDigit() }.take(3) },
                            label = { Text("Timeout de Conexão (s)") },
                            leadingIcon = { Icon(Icons.Default.Timer, null) },
                            modifier = Modifier.fillMaxWidth()
                        )

                        OutlinedTextField(
                            value = readTimeout,
                            onValueChange = { v -> readTimeout = v.filter { c -> c.isDigit() }.take(3) },
                            label = { Text("Timeout de Leitura (s)") },
                            leadingIcon = { Icon(Icons.Default.Schedule, null) },
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }

            // Seção Privacidade
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "🔒 Privacidade",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Spacer(Modifier.height(12.dp))

                        SettingRow(
                            icon = Icons.Default.Analytics,
                            title = "Análises",
                            subtitle = "Enviar dados de uso anônimos",
                            checked = enableAnalytics,
                            onCheckedChange = {
                                enableAnalytics = it
                                scope.launch { store.setAnalytics(it) }
                            }
                        )
                    }
                }
            }

            // Seção Sobre
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "ℹ Sobre",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Spacer(Modifier.height(12.dp))

                        SettingRow(
                            icon = Icons.Default.SystemUpdate,
                            title = "Atualizações Automáticas",
                            subtitle = "Verificar atualizações automaticamente",
                            checked = enableAutoUpdate,
                            onCheckedChange = {
                                enableAutoUpdate = it
                                scope.launch { store.setAutoUpdate(it) }
                            }
                        )

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(Icons.Default.Info, null, tint = MaterialTheme.colorScheme.primary)
                            Spacer(Modifier.width(16.dp))
                            Column {
                                Text("Versão", fontWeight = FontWeight.Medium)
                                Text("1.0.0 Beta", fontSize = 12.sp, color = MaterialTheme.colorScheme.onSurfaceVariant)
                            }
                        }
                    }
                }
            }

            // Botão Salvar
            item {
                Button(
                    onClick = {
                        val limit = maxParallel.toIntOrNull()?.coerceIn(1, 20) ?: 20
                        val retries = maxRetries.toIntOrNull()?.coerceIn(0, 9) ?: 3
                        val cacheMb = maxCacheSize.toIntOrNull()?.coerceIn(10, 10000) ?: 500
                        val connTo = connectionTimeout.toIntOrNull()?.coerceIn(5, 300) ?: 30
                        val readTo = readTimeout.toIntOrNull()?.coerceIn(5, 600) ?: 60
                        scope.launch {
                            store.setWifiOnly(wifiOnly)
                            store.setEnablePip(usePip)
                            store.setConcurrencyLimit(limit)
                            store.setMaxRetries(retries)
                            store.setMaxCacheMb(cacheMb)
                            store.setConnectTimeout(connTo)
                            store.setReadTimeout(readTo)
                            // Persist already saved toggles/selections handled on change
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Icon(Icons.Default.Save, null)
                    Spacer(Modifier.width(8.dp))
                    Text("Salvar Configurações", fontWeight = FontWeight.Bold)
                }
            }
        }
    }
}

@Composable
private fun SettingRow(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary
        )
        Spacer(Modifier.width(16.dp))
        Column(Modifier.weight(1f)) {
            Text(title, fontWeight = FontWeight.Medium)
            Text(
                subtitle,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

@Composable
private fun QualitySelector(
    title: String,
    options: List<String>,
    selected: String,
    onSelectionChange: (String) -> Unit
) {
    Column {
        Text(title, fontWeight = FontWeight.Medium)
        Spacer(Modifier.height(8.dp))
        Row(
            modifier = Modifier.selectableGroup(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            options.forEach { option ->
                FilterChip(
                    selected = selected == option,
                    onClick = { onSelectionChange(option) },
                    label = { Text(option) }
                )
            }
        }
    }
}
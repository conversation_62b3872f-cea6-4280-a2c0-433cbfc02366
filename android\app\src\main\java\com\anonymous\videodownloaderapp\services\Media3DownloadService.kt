package com.anonymous.videodownloaderapp.services

import android.app.Notification
import android.content.Context
import android.content.Intent
import androidx.media3.common.util.NotificationUtil
import androidx.media3.common.util.Util
import androidx.media3.exoplayer.offline.Download
import androidx.media3.exoplayer.offline.DownloadManager
import androidx.media3.exoplayer.offline.DownloadService
import androidx.media3.exoplayer.scheduler.Scheduler
import com.anonymous.videodownloaderapp.R

/**
 * Foreground DownloadService using Media3 for segmented downloads (HLS/DASH).
 * This wires a DownloadManager with a simple on-disk cache and shows notifications.
 */
class Media3DownloadService : DownloadService(
    FOREGROUND_NOTIFICATION_ID,
    DEFAULT_FOREGROUND_NOTIFICATION_UPDATE_INTERVAL,
    CHANNEL_ID,
    R.string.download_channel_name,
    0 // Placeholder for channel description string resource
) {

    override fun getDownloadManager(): DownloadManager {
        return Media3DownloadHelper.getDownloadManager(this)
    }

    override fun getScheduler(): Scheduler? {
        // Optional: schedule when requirements met
        return null
    }

    override fun getForegroundNotification(downloads: MutableList<Download>, notMetRequirements: Int): Notification {
        val helper = Media3DownloadHelper.getNotificationHelper(this)
        return helper.buildProgressNotification(
            this,
            R.drawable.ic_download, // ensure you have this icon; else switch to app icon
            null,
            null,
            downloads,
            notMetRequirements
        )
    }

    companion object {
        const val CHANNEL_ID = "media3_downloads"
        const val FOREGROUND_NOTIFICATION_ID = 0xD07 // arbitrary

        fun start(context: Context) {
            val intent = android.content.Intent(context, Media3DownloadService::class.java)
            context.startService(intent)
        }

        fun stop(context: Context) {
            val intent = android.content.Intent(context, Media3DownloadService::class.java)
            context.stopService(intent)
        }
    }
}
/**
 * Deprecated duplicate DAO placeholder.
 * Canonical DAO is com.anonymous.videodownloaderapp.data.DownloadDao with DownloadItem.
 * Kept minimal to avoid behavior changes while preventing accidental imports.
 */
@file:Suppress("unused", "ClassName", "DeprecatedCallableAddReplaceWith")

package com.anonymous.videodownloaderapp.data.downloads

@Deprecated("Use com.anonymous.videodownloaderapp.data.DownloadDao with DownloadItem instead.")
interface DownloadDao_LegacyPlaceholder

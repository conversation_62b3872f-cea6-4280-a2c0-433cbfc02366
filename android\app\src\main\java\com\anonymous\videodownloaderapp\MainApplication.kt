package com.anonymous.videodownloaderapp

import android.app.Application
import com.anonymous.videodownloaderapp.integration.PerformanceIntegration
import com.anonymous.videodownloaderapp.optimization.StartupOptimizer
import com.anonymous.videodownloaderapp.services.NotificationHelper

class MainApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()

        // Ensure notification channels exist before any foreground work (no UI changes)
        NotificationHelper.ensureChannels(this)
        
        // Initialize performance improvements
        PerformanceIntegration.initialize(this)
        PerformanceIntegration.upgradeDownloadSystem()
        
        // Start startup optimization
        val startupOptimizer = StartupOptimizer(this)
        startupOptimizer.optimizeStartup()
        startupOptimizer.preloadCriticalData()
        
        // Start battery monitoring
        PerformanceIntegration.startBatteryMonitoring()
    }
    
    override fun onTerminate() {
        super.onTerminate()
        PerformanceIntegration.cleanup()
    }
}

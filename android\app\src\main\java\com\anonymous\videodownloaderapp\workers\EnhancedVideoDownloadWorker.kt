package com.anonymous.videodownloaderapp.workers

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.anonymous.videodownloaderapp.data.DownloadItem
import com.anonymous.videodownloaderapp.services.EnhancedDownloadService
import com.anonymous.videodownloaderapp.services.DownloadTracker
import com.google.gson.Gson

class EnhancedVideoDownloadWorker(appContext: Context, workerParams: WorkerParameters) :
    CoroutineWorker(appContext, workerParams) {

    private val enhancedDownloadService = EnhancedDownloadService(appContext)
    private val downloadTracker = DownloadTracker(appContext)
    private val gson = Gson()

    override suspend fun doWork(): Result {
        val downloadItemJson = inputData.getString("downloadItem")
            ?: return Result.failure()
        
        val downloadItem = try {
            gson.fromJson(downloadItemJson, DownloadItem::class.java)
        } catch (e: Exception) {
            return Result.failure()
        }

        return try {
            // Initialize download
            downloadTracker.updateDownload(downloadItem.copy(
                status = DownloadItem.Status.Running,
                updatedAt = System.currentTimeMillis()
            ))

            // Execute enhanced download
            enhancedDownloadService.startDownload(downloadItem)
            
            // Wait for completion (this is async, so we'll use a different approach)
            // For now, return success as the service handles completion
            Result.success()
            
        } catch (e: Exception) {
            downloadTracker.updateDownload(downloadItem.copy(
                status = DownloadItem.Status.Failed,
                error = e.localizedMessage,
                updatedAt = System.currentTimeMillis()
            ))
            Result.failure()
        }
    }

    companion object {
        fun createInputData(downloadItem: DownloadItem): androidx.work.Data {
            return androidx.work.workDataOf(
                "downloadItem" to Gson().toJson(downloadItem)
            )
        }
    }
}
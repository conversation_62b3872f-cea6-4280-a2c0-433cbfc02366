package com.anonymous.videodownloaderapp.services

import android.content.Context
import android.webkit.WebView
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * Sistema de Detecção de Qualidade de Vídeo Ultra-Preciso
 * Integra todos os componentes para máxima precisão e performance
 */
class UltraPreciseVideoDetectionSystem private constructor(private val context: Context) {

    data class DetectionResult(
        val candidates: List<VideoCandidate>,
        val processingTime: Long,
        val detectionSource: DetectionSource,
        val qualityMappingApplied: <PERSON>olean,
        val specialCasesDetected: List<SpecialCase>,
        val performanceMetrics: DetectionPerformanceMetrics,
        val timestamp: Long = System.currentTimeMillis()
    )

    data class VideoCandidate(
        val url: String,
        val quality: String,
        val format: VideoFormat,
        val title: String,
        val score: Float,
        val width: Int?,
        val height: Int?,
        val duration: Double?,
        val isLive: Boolean,
        val codec: String?,
        val bitrate: Long?,
        val frameRate: Double?,
        val audioCodec: String?,
        val metadata: Map<String, Any> = emptyMap()
    )

    data class DetectionPerformanceMetrics(
        val totalTime: Long,
        val javascriptTime: Long,
        val nativeTime: Long,
        val qualityMappingTime: Long,
        val specialCasesTime: Long,
        val memoryUsage: Long,
        val cpuUsage: Double,
        val cacheHitRate: Double
    )

    enum class DetectionSource {
        JAVASCRIPT_ENHANCED,
        NATIVE_INTERCEPTOR,
        HYBRID_COMBINED,
        YOUTUBE_SPECIFIC,
        HLS_MANIFEST,
        DASH_MANIFEST,
        PERFORMANCE_API
    }

    enum class VideoFormat {
        MP4, WEBM, HLS, DASH, UNKNOWN
    }

    enum class SpecialCase {
        OFFSCREEN_CONTENT,
        PICTURE_IN_PICTURE,
        LIVE_STREAM,
        QUARANTINED_CONTENT,
        MOBILE_OPTIMIZED
    }

    enum class SystemState {
        INITIALIZING,
        READY,
        DETECTING,
        OPTIMIZING,
        ERROR,
        SUSPENDED
    }

    companion object {
        private const val DETECTION_TIMEOUT_MS = 5000L
        private const val QUALITY_MAPPING_TIMEOUT_MS = 1000L
        private const val SPECIAL_CASES_TIMEOUT_MS = 500L
        private const val SYSTEM_OPTIMIZATION_INTERVAL_MS = 60000L // 1 minuto
        
        @Volatile
        private var instance: UltraPreciseVideoDetectionSystem? = null
        
        fun getInstance(context: Context): UltraPreciseVideoDetectionSystem {
            return instance ?: synchronized(this) {
                instance ?: UltraPreciseVideoDetectionSystem(context.applicationContext).also {
                    instance = it
                }
            }
        }
    }

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // Componentes do sistema
    private val youTubeQualityMapper by lazy { YouTubeQualityMapper(context) }
    private val mobileLifecycleManager by lazy { MobileLifecycleManager.getInstance(context) }
    private val specialCasesHandler by lazy { SpecialCasesHandler.getInstance(context) }
    private val optimizedJsBridge by lazy { OptimizedJavaScriptBridge.getInstance(context) }
    private val webViewConfigManager by lazy { WebViewConfigurationManager.getInstance(context) }
    private val telemetrySystem by lazy { TelemetrySystem.getInstance(context) }
    private val hybridVideoDetectionService by lazy { HybridVideoDetectionService.getInstance(context) }
    
    // Estado do sistema
    private val _systemState = MutableStateFlow(SystemState.INITIALIZING)
    val systemState: StateFlow<SystemState> = _systemState
    
    private val _lastDetectionResult = MutableStateFlow<DetectionResult?>(null)
    val lastDetectionResult: StateFlow<DetectionResult?> = _lastDetectionResult
    
    private val _isOptimizing = MutableStateFlow(false)
    val isOptimizing: StateFlow<Boolean> = _isOptimizing
    
    // Controle de operações
    private val activeDetections = ConcurrentHashMap<String, Job>()
    private val detectionCounter = AtomicLong(0)
    private val isSystemReady = AtomicBoolean(false)
    
    // Callbacks
    private val detectionCallbacks = mutableSetOf<(DetectionResult) -> Unit>()
    private val errorCallbacks = mutableSetOf<(String, Throwable?) -> Unit>()
    private val stateChangeCallbacks = mutableSetOf<(SystemState) -> Unit>()
    
    // Métricas de performance
    private var totalDetections = 0L
    private var successfulDetections = 0L
    private var averageDetectionTime = 0.0
    private var lastOptimizationTime = 0L

    init {
        initializeSystem()
    }

    /**
     * Inicializar sistema completo
     */
    private fun initializeSystem() {
        scope.launch {
            try {
                _systemState.value = SystemState.INITIALIZING
                
                // Configurar telemetria
                telemetrySystem.configure(
                    level = TelemetrySystem.TelemetryLevel.DETAILED,
                    enabled = true,
                    persistence = true,
                    realTimeReporting = false
                )
                
                // Configurar callbacks dos componentes
                setupComponentCallbacks()
                
                // Configurar bridge JavaScript
                setupJavaScriptBridge()
                
                // Iniciar otimização periódica
                startPeriodicOptimization()
                
                // Sistema pronto
                isSystemReady.set(true)
                _systemState.value = SystemState.READY
                
                telemetrySystem.recordPerformanceMetric(
                    TelemetrySystem.MetricType.DETECTION_LATENCY,
                    0.0,
                    mapOf("event" to "system_initialized")
                )
                
                notifyStateChange(SystemState.READY)
                
            } catch (e: Exception) {
                _systemState.value = SystemState.ERROR
                notifyError("Erro na inicialização do sistema", e)
            }
        }
    }

    /**
     * Configurar callbacks dos componentes
     */
    private fun setupComponentCallbacks() {
        // Callback do bridge JavaScript - fix type inference
        optimizedJsBridge.addCandidateCallback { candidates: List<OptimizedJavaScriptBridge.CandidateData> ->
            scope.launch {
                processJavaScriptCandidates(candidates)
            }
        }
        
        optimizedJsBridge.addErrorCallback { message, throwable ->
            notifyError("JavaScript Bridge Error: $message", throwable)
        }
        
        // Callback do lifecycle manager
        mobileLifecycleManager.addLifecycleCallback(object : MobileLifecycleManager.LifecycleCallback {
            override fun onLifecycleStateChanged(state: MobileLifecycleManager.LifecycleState) {
                scope.launch {
                    handleLifecycleStateChange(state)
                }
            }
            
            override fun onDetectionShouldPause() {
                suspendSystem()
            }
            
            override fun onDetectionShouldResume() {
                resumeSystem()
            }
            
            override fun onBatteryOptimizationRequired(level: Int) {
                scope.launch {
                    performMemoryOptimization()
                }
            }
        })
        
        // Callback de casos especiais - Remove non-existent addQuarantineCallback
        // specialCasesHandler.addQuarantineCallback { entry ->
        //     telemetrySystem.recordDetectionEvent(
        //         TelemetrySystem.DetectionEventType.DETECTION_FAILED,
        //         entry.url,
        //         errorMessage = "Quarantined: ${entry.reason}"
        //     )
        // }
    }

    /**
     * Configurar bridge JavaScript
     */
    private fun setupJavaScriptBridge() {
        // Configurações já são feitas no OptimizedJavaScriptBridge
        // Aqui apenas registramos callbacks adicionais se necessário
    }

    /**
     * Configurar WebView para detecção ultra-precisa
     */
    suspend fun configureWebView(
        webView: WebView,
        optimizationProfile: WebViewConfigurationManager.ConfigurationProfile = 
            WebViewConfigurationManager.ConfigurationProfile.VIDEO_DETECTION_FOCUSED
    ): Boolean {
        
        if (!isSystemReady.get()) {
            return false
        }
        
        return try {
            val startTime = System.currentTimeMillis()
            
            // Configurar WebView
            val configured = webViewConfigManager.configureWebView(webView, optimizationProfile)
            
            if (configured) {
                // Configurar bridge JavaScript
                optimizedJsBridge.setupWebView(webView)
                
                val configTime = System.currentTimeMillis() - startTime
                
                telemetrySystem.recordPerformanceMetric(
                    TelemetrySystem.MetricType.WEBVIEW_LOAD_TIME,
                    configTime.toDouble(),
                    mapOf(
                        "profile" to optimizationProfile.name,
                        "success" to true
                    )
                )
                
                telemetrySystem.recordDetectionEvent(
                    TelemetrySystem.DetectionEventType.WEBVIEW_CONFIGURED,
                    "webview_configured",
                    metadata = mapOf("profile" to optimizationProfile.name)
                )
            }
            
            configured
            
        } catch (e: Exception) {
            notifyError("Erro ao configurar WebView", e)
            false
        }
    }

    /**
     * Detectar vídeos com máxima precisão
     */
    suspend fun detectVideosUltraPrecise(
        webView: WebView,
        pageUrl: String,
        enableYouTubeOptimization: Boolean = true,
        enableSpecialCasesDetection: Boolean = true,
        priority: OptimizedJavaScriptBridge.Priority = OptimizedJavaScriptBridge.Priority.NORMAL
    ): DetectionResult? {
        
        if (!isSystemReady.get()) {
            return null
        }
        
        val detectionId = "detection_${detectionCounter.incrementAndGet()}"
        val startTime = System.currentTimeMillis()
        
        return try {
            _systemState.value = SystemState.DETECTING
            totalDetections++
            
            // Registrar início da detecção
            telemetrySystem.recordDetectionEvent(
                TelemetrySystem.DetectionEventType.DETECTION_STARTED,
                pageUrl
            )
            
            // Executar detecção com timeout
            val result = withTimeoutOrNull(DETECTION_TIMEOUT_MS) {
                performUltraPreciseDetection(
                    webView,
                    pageUrl,
                    enableYouTubeOptimization,
                    enableSpecialCasesDetection,
                    priority,
                    detectionId
                )
            }
            
            val totalTime = System.currentTimeMillis() - startTime
            
            if (result != null) {
                successfulDetections++
                averageDetectionTime = (averageDetectionTime * (successfulDetections - 1) + totalTime) / successfulDetections
                
                _lastDetectionResult.value = result
                
                // Registrar sucesso
                telemetrySystem.recordDetectionEvent(
                    TelemetrySystem.DetectionEventType.DETECTION_COMPLETED,
                    pageUrl,
                    candidatesFound = result.candidates.size,
                    processingTime = totalTime,
                    success = true
                )
                
                // Notificar callbacks
                notifyDetectionResult(result)
            } else {
                // Timeout ou erro
                telemetrySystem.recordDetectionEvent(
                    TelemetrySystem.DetectionEventType.DETECTION_FAILED,
                    pageUrl,
                    processingTime = totalTime,
                    success = false,
                    errorMessage = "Detection timeout"
                )
            }
            
            _systemState.value = SystemState.READY
            result
            
        } catch (e: Exception) {
            _systemState.value = SystemState.ERROR
            notifyError("Erro na detecção ultra-precisa", e)
            null
        } finally {
            activeDetections.remove(detectionId)
        }
    }

    /**
     * Executar detecção ultra-precisa
     */
    private suspend fun performUltraPreciseDetection(
        webView: WebView,
        pageUrl: String,
        enableYouTubeOptimization: Boolean,
        enableSpecialCasesDetection: Boolean,
        priority: OptimizedJavaScriptBridge.Priority,
        detectionId: String
    ): DetectionResult = withContext(Dispatchers.Main) {
        
        val startTime = System.currentTimeMillis()
        val candidates = mutableListOf<VideoCandidate>()
        val specialCases = mutableListOf<SpecialCase>()
        var detectionSource = DetectionSource.HYBRID_COMBINED
        
        // Métricas de performance
        var jsTime = 0L
        var nativeTime = 0L
        var qualityMappingTime = 0L
        var specialCasesTime = 0L
        
        try {
            // 1. Detecção JavaScript otimizada
            val jsStartTime = System.currentTimeMillis()
            optimizedJsBridge.detectVideosWithDebouncing(webView, pageUrl, priority)
            
            // Aguardar resultado do JavaScript (simulado)
            delay(100)
            jsTime = System.currentTimeMillis() - jsStartTime
            
            // 2. Detecção nativa híbrida - fix missing pageUrl parameter
            val nativeStartTime = System.currentTimeMillis()
            val nativeCandidates = hybridVideoDetectionService.detectVideos(webView, pageUrl)
            nativeTime = System.currentTimeMillis() - nativeStartTime
            
            // Converter candidatos nativos - fix property references
            nativeCandidates.forEach { nativeCandidate ->
                candidates.add(
                    VideoCandidate(
                        url = nativeCandidate.url,
                        quality = nativeCandidate.quality,
                        format = mapToVideoFormat(nativeCandidate.format),
                        title = "Video",  // VideoQuality doesn't have title
                        score = 75.0f,  // VideoQuality doesn't have score
                        width = null,  // VideoQuality doesn't have width
                        height = null,  // VideoQuality doesn't have height  
                        duration = null,  // VideoQuality doesn't have duration
                        isLive = false,  // VideoQuality doesn't have isLive
                        codec = null,
                        bitrate = null,
                        frameRate = null,
                        audioCodec = null
                    )
                )
            }
            
            // 3. Mapeamento de qualidade YouTube (if enabled) - remove problematic YouTube enhancement
            if (enableYouTubeOptimization && pageUrl.contains("youtube.com")) {
                val qualityStartTime = System.currentTimeMillis()
                
                // Use YouTubeQualityMapper directly instead of enhance function
                val youTubeQualities = youTubeQualityMapper.extractYouTubeQualities(webView, pageUrl)
                
                // Convert YouTube qualities to our VideoCandidate format
                youTubeQualities.forEach { ytQuality ->
                    candidates.add(
                        VideoCandidate(
                            url = ytQuality.url,
                            quality = ytQuality.quality,
                            format = VideoFormat.valueOf(ytQuality.codec?.uppercase() ?: "MP4"),
                            title = "YouTube Video",
                            score = 85.0f,
                            width = ytQuality.width,
                            height = ytQuality.height,
                            duration = null,
                            isLive = false,
                            codec = ytQuality.codec,
                            bitrate = ytQuality.contentLength,
                            frameRate = ytQuality.fps?.toDouble(),
                            audioCodec = if (ytQuality.hasAudio) "aac" else null,
                            metadata = mapOf(
                                "itag" to ytQuality.itag,
                                "hasAudio" to ytQuality.hasAudio
                            )
                        )
                    )
                }
                
                detectionSource = DetectionSource.YOUTUBE_SPECIFIC
                qualityMappingTime = System.currentTimeMillis() - qualityStartTime
                
                telemetrySystem.recordDetectionEvent(
                    TelemetrySystem.DetectionEventType.QUALITY_MAPPED,
                    pageUrl,
                    candidatesFound = candidates.size  // Use candidates instead of undefined enhancedCandidates
                )
            }
            
            // 4. Detecção de casos especiais (se habilitado)
            if (enableSpecialCasesDetection) {
                val specialStartTime = System.currentTimeMillis()
                
                // Check quarantine status instead of non-existent isContentOffscreen
                val hasQuarantinedContent = candidates.any { candidate ->
                    specialCasesHandler.isInQuarantine(candidate.url.hashCode().toString())
                }
                if (hasQuarantinedContent) {
                    specialCases.add(SpecialCase.OFFSCREEN_CONTENT)
                }
                
                // Check PiP state instead of non-existent isPiPActive
                if (specialCasesHandler.pipState.value.isActive) {
                    specialCases.add(SpecialCase.PICTURE_IN_PICTURE)
                }
                
                // Verificar streams live
                candidates.forEach { candidate ->
                    if (candidate.isLive) {
                        specialCases.add(SpecialCase.LIVE_STREAM)
                        specialCasesHandler.registerLiveStream(
                            candidate.url,  // manifestUrl: String
                            false,  // isDynamic: Boolean 
                            listOf(candidate.quality)  // initialQualities: List<String>
                        )
                    }
                }
                
                // Verificar otimização mobile
                val lifecycleState = mobileLifecycleManager.lifecycleState.value
                if (lifecycleState.isLowBattery) {
                    specialCases.add(SpecialCase.MOBILE_OPTIMIZED)
                }
                
                specialCasesTime = System.currentTimeMillis() - specialStartTime
            }
            
            // 5. Filtrar e ordenar candidatos finais
            val filteredCandidates = filterAndRankCandidates(candidates)
            
            // 6. Calcular métricas de performance
            val totalTime = System.currentTimeMillis() - startTime
            val runtime = Runtime.getRuntime()
            val memoryUsage = runtime.totalMemory() - runtime.freeMemory()
            
            val performanceMetrics = DetectionPerformanceMetrics(
                totalTime = totalTime,
                javascriptTime = jsTime,
                nativeTime = nativeTime,
                qualityMappingTime = qualityMappingTime,
                specialCasesTime = specialCasesTime,
                memoryUsage = memoryUsage,
                cpuUsage = getCurrentCpuUsage(),
                cacheHitRate = calculateCacheHitRate()
            )
            
            // Registrar métricas
            telemetrySystem.recordPerformanceMetric(
                TelemetrySystem.MetricType.DETECTION_LATENCY,
                totalTime.toDouble(),
                mapOf(
                    "source" to detectionSource.name,
                    "candidatesFound" to filteredCandidates.size,
                    "specialCases" to specialCases.size
                )
            )
            
            return@withContext DetectionResult(
                candidates = filteredCandidates,
                processingTime = totalTime,
                detectionSource = detectionSource,
                qualityMappingApplied = enableYouTubeOptimization && qualityMappingTime > 0,
                specialCasesDetected = specialCases,
                performanceMetrics = performanceMetrics
            )
            
        } catch (e: Exception) {
            throw e
        }
    }

    /**
     * Processar candidatos do JavaScript
     */
    private suspend fun processJavaScriptCandidates(
        jsCandidates: List<OptimizedJavaScriptBridge.CandidateData>
    ) {
        // Converter candidatos JavaScript para formato interno
        val candidates = jsCandidates.map { jsCandidate ->
            VideoCandidate(
                url = jsCandidate.url,
                quality = jsCandidate.quality,
                format = mapToVideoFormat(jsCandidate.format),
                title = jsCandidate.title,
                score = jsCandidate.score,
                width = jsCandidate.width,
                height = jsCandidate.height,
                duration = jsCandidate.duration,
                isLive = jsCandidate.isLive,
                codec = null,
                bitrate = null,
                frameRate = null,
                audioCodec = null
            )
        }
        
        // Processar candidatos se necessário
        telemetrySystem.recordDetectionEvent(
            TelemetrySystem.DetectionEventType.CANDIDATES_FOUND,
            "javascript_bridge",
            candidatesFound = candidates.size
        )
    }

    /**
     * Filtrar e classificar candidatos
     */
    private fun filterAndRankCandidates(candidates: List<VideoCandidate>): List<VideoCandidate> {
        return candidates
            .filter { it.score >= 5.0f } // Score mínimo
            .distinctBy { it.url } // Remover duplicatas
            .sortedWith(
                compareByDescending<VideoCandidate> { it.score }
                    .thenByDescending { getQualityPriority(it.quality) }
                    .thenByDescending { it.width ?: 0 }
            )
            .take(20) // Limitar a 20 candidatos
    }

    /**
     * Obter prioridade da qualidade
     */
    private fun getQualityPriority(quality: String): Int {
        return when {
            quality.contains("2160p") || quality.contains("4K") -> 100
            quality.contains("1440p") -> 90
            quality.contains("1080p") -> 80
            quality.contains("720p") -> 70
            quality.contains("480p") -> 60
            quality.contains("360p") -> 50
            quality.contains("240p") -> 40
            quality.contains("144p") -> 30
            else -> 20
        }
    }

    /**
     * Mapear formato de vídeo
     */
    private fun mapToVideoFormat(format: String): VideoFormat {
        return when (format.lowercase()) {
            "mp4" -> VideoFormat.MP4
            "webm" -> VideoFormat.WEBM
            "hls", "m3u8" -> VideoFormat.HLS
            "dash", "mpd" -> VideoFormat.DASH
            else -> VideoFormat.UNKNOWN
        }
    }

    /**
     * Extrair itag da URL
     */
    private fun extractItagFromUrl(url: String): Int? {
        return try {
            val regex = Regex("itag=(\\d+)")
            val match = regex.find(url)
            match?.groupValues?.get(1)?.toInt()
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Obter uso atual de CPU
     */
    private fun getCurrentCpuUsage(): Double {
        return try {
            // Implementação simplificada
            val runtime = Runtime.getRuntime()
            val processors = runtime.availableProcessors()
            // Retorna uma estimativa baseada no número de processadores
            (100.0 / processors) * 0.5 // Estimativa conservadora
        } catch (e: Exception) {
            0.0
        }
    }

    /**
     * Calcular taxa de acerto do cache
     */
    private fun calculateCacheHitRate(): Double {
        return try {
            // Implementação simplificada
            // Em uma implementação real, isso viria de estatísticas de cache
            if (successfulDetections > 0) {
                (successfulDetections.toDouble() / totalDetections) * 100
            } else {
                0.0
            }
        } catch (e: Exception) {
            0.0
        }
    }

    /**
     * Lidar com mudanças de estado do lifecycle
     */
    private suspend fun handleLifecycleStateChange(state: MobileLifecycleManager.LifecycleState) {
        when {
            state.isLowBattery -> {
                // Reduzir frequência de detecção
                telemetrySystem.recordPerformanceMetric(
                    TelemetrySystem.MetricType.BATTERY_DRAIN,
                    state.batteryLevel.toDouble(),
                    mapOf("optimization" to "enabled")
                )
            }
            state.isThrottled -> {
                // Limpar caches
                _systemState.value = SystemState.OPTIMIZING
                performMemoryOptimization()
                _systemState.value = SystemState.READY
            }
        }
    }

    /**
     * Otimização periódica do sistema
     */
    private fun startPeriodicOptimization() {
        scope.launch {
            while (isActive) {
                delay(SYSTEM_OPTIMIZATION_INTERVAL_MS)
                
                try {
                    _isOptimizing.value = true
                    performSystemOptimization()
                    lastOptimizationTime = System.currentTimeMillis()
                } catch (e: Exception) {
                    notifyError("Erro na otimização periódica", e)
                } finally {
                    _isOptimizing.value = false
                }
            }
        }
    }

    /**
     * Executar otimização do sistema
     */
    private suspend fun performSystemOptimization() {
        // Limpeza de memória
        performMemoryOptimization()
        
        // Otimização de performance
        optimizePerformance()
        
        // Limpeza de dados antigos
        cleanupOldData()
        
        telemetrySystem.recordPerformanceMetric(
            TelemetrySystem.MetricType.MEMORY_ALLOCATION,
            Runtime.getRuntime().freeMemory().toDouble(),
            mapOf("event" to "system_optimization")
        )
    }

    /**
     * Otimização de memória
     */
    private suspend fun performMemoryOptimization() {
        // Limpar detecções antigas
        if (activeDetections.size > 10) {
            val expiredDetections = activeDetections.filter { (_, job) -> !job.isActive }
            expiredDetections.forEach { (id, _) -> activeDetections.remove(id) }
        }
        
        // Forçar garbage collection
        System.gc()
    }

    /**
     * Otimização de performance
     */
    private suspend fun optimizePerformance() {
        // Ajustar configurações baseado na performance atual
        val currentPerformance = averageDetectionTime
        
        if (currentPerformance > 3000) { // > 3 segundos
            // Reduzir qualidade de detecção para melhorar performance
            telemetrySystem.recordPerformanceMetric(
                TelemetrySystem.MetricType.THROUGHPUT,
                1000.0 / currentPerformance,
                mapOf("optimization" to "performance_degraded")
            )
        }
    }

    /**
     * Limpeza de dados antigos
     */
    private suspend fun cleanupOldData() {
        // Implementar limpeza de dados antigos se necessário
    }

    /**
     * Adicionar callback de detecção
     */
    fun addDetectionCallback(callback: (DetectionResult) -> Unit) {
        detectionCallbacks.add(callback)
    }

    /**
     * Remover callback de detecção
     */
    fun removeDetectionCallback(callback: (DetectionResult) -> Unit) {
        detectionCallbacks.remove(callback)
    }

    /**
     * Adicionar callback de erro
     */
    fun addErrorCallback(callback: (String, Throwable?) -> Unit) {
        errorCallbacks.add(callback)
    }

    /**
     * Adicionar callback de mudança de estado
     */
    fun addStateChangeCallback(callback: (SystemState) -> Unit) {
        stateChangeCallbacks.add(callback)
    }

    /**
     * Notificar resultado de detecção
     */
    private fun notifyDetectionResult(result: DetectionResult) {
        detectionCallbacks.forEach { callback ->
            try {
                callback(result)
            } catch (e: Exception) {
                // Ignorar erros em callbacks
            }
        }
    }

    /**
     * Notificar erro
     */
    private fun notifyError(message: String, throwable: Throwable?) {
        errorCallbacks.forEach { callback ->
            try {
                callback(message, throwable)
            } catch (e: Exception) {
                // Ignorar erros em callbacks
            }
        }
    }

    /**
     * Notificar mudança de estado
     */
    private fun notifyStateChange(state: SystemState) {
        stateChangeCallbacks.forEach { callback ->
            try {
                callback(state)
            } catch (e: Exception) {
                // Ignorar erros em callbacks
            }
        }
    }

    /**
     * Obter estatísticas do sistema
     */
    fun getSystemStatistics(): Map<String, Any> {
        return mapOf(
            "systemState" to _systemState.value.name,
            "isSystemReady" to isSystemReady.get(),
            "totalDetections" to totalDetections,
            "successfulDetections" to successfulDetections,
            "successRate" to if (totalDetections > 0) (successfulDetections.toDouble() / totalDetections) else 0.0,
            "averageDetectionTime" to averageDetectionTime,
            "activeDetections" to activeDetections.size,
            "lastOptimizationTime" to lastOptimizationTime,
            "isOptimizing" to _isOptimizing.value,
            "memoryUsage" to (Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()),
            "telemetryStats" to telemetrySystem.getCurrentStats(),
            "lifecycleState" to mobileLifecycleManager.lifecycleState.value,
            "webViewStats" to webViewConfigManager.getConfigurationStats(),
            "bridgeStats" to optimizedJsBridge.getPerformanceStats()
        )
    }

    /**
     * Suspender sistema
     */
    fun suspendSystem() {
        _systemState.value = SystemState.SUSPENDED
        // Cancelar detecções ativas
        activeDetections.values.forEach { it.cancel() }
        activeDetections.clear()
    }

    /**
     * Retomar sistema
     */
    fun resumeSystem() {
        if (_systemState.value == SystemState.SUSPENDED) {
            _systemState.value = SystemState.READY
        }
    }

    /**
     * Limpeza de recursos
     */
    fun cleanup() {
        scope.cancel()
        activeDetections.values.forEach { it.cancel() }
        activeDetections.clear()
        detectionCallbacks.clear()
        errorCallbacks.clear()
        stateChangeCallbacks.clear()
        
        // Cleanup dos componentes
        youTubeQualityMapper.clearAllCache()
        // Não é possível chamar cleanup() (privado) no MobileLifecycleManager; rely on GC and lifecycle owner
        specialCasesHandler.cleanup()
        optimizedJsBridge.cleanup()
        webViewConfigManager.cleanup()
        telemetrySystem.cleanup()
        
        instance = null
    }
}
package com.anonymous.videodownloaderapp.config

/**
 * Configurações Centralizadas do Sistema de Detecção Ultra-Preciso
 * Todas as constantes, configurações e parâmetros do sistema
 */
object UltraPreciseDetectionConfig {

    /**
     * Configurações de Timeout
     */
    object Timeouts {
        const val DETECTION_TIMEOUT_MS = 5000L
        const val JAVASCRIPT_EXECUTION_TIMEOUT_MS = 3000L
        const val QUALITY_MAPPING_TIMEOUT_MS = 1000L
        const val SPECIAL_CASES_TIMEOUT_MS = 500L
        const val WEBVIEW_CONFIGURATION_TIMEOUT_MS = 2000L
        const val NETWORK_REQUEST_TIMEOUT_MS = 10000L
        const val MANIFEST_PARSING_TIMEOUT_MS = 2000L
    }

    /**
     * Configurações de Performance
     */
    object Performance {
        const val MAX_CONCURRENT_DETECTIONS = 3
        const val MAX_CANDIDATES_PER_DETECTION = 20
        const val MIN_VIDEO_SCORE = 5.0f
        const val MIN_VIDEO_DURATION_SECONDS = 10.0
        const val MAX_DETECTION_HISTORY = 50
        const val MEMORY_CLEANUP_THRESHOLD_MB = 100
        const val CPU_USAGE_THRESHOLD_PERCENT = 80.0
        const val CACHE_SIZE_LIMIT_MB = 50
        const val MAX_JAVASCRIPT_HEAP_SIZE_MB = 128
    }

    /**
     * Configurações de Otimização
     */
    object Optimization {
        const val SYSTEM_OPTIMIZATION_INTERVAL_MS = 60000L // 1 minuto
        const val MEMORY_CLEANUP_INTERVAL_MS = 300000L // 5 minutos
        const val TELEMETRY_FLUSH_INTERVAL_MS = 120000L // 2 minutos
        const val CACHE_CLEANUP_INTERVAL_MS = 600000L // 10 minutos
        const val PERFORMANCE_MONITORING_INTERVAL_MS = 30000L // 30 segundos
        const val BATTERY_CHECK_INTERVAL_MS = 60000L // 1 minuto
        const val LIFECYCLE_STATE_CHECK_INTERVAL_MS = 5000L // 5 segundos
    }

    /**
     * Configurações de Debouncing
     */
    object Debouncing {
        const val DEFAULT_DEBOUNCE_MS = 300L
        const val HIGH_PRIORITY_DEBOUNCE_MS = 100L
        const val LOW_PRIORITY_DEBOUNCE_MS = 500L
        const val YOUTUBE_DEBOUNCE_MS = 200L
        const val SCROLL_DEBOUNCE_MS = 150L
        const val RESIZE_DEBOUNCE_MS = 250L
        const val NAVIGATION_DEBOUNCE_MS = 500L
    }

    /**
     * Configurações de Qualidade de Vídeo
     */
    object VideoQuality {
        const val ULTRA_HD_4K = "2160p"
        const val QUAD_HD = "1440p"
        const val FULL_HD = "1080p"
        const val HD = "720p"
        const val SD = "480p"
        const val LOW_SD = "360p"
        const val VERY_LOW = "240p"
        const val MINIMUM = "144p"
        const val AUTO = "Auto"
        const val UNKNOWN = "Unknown"
        
        val QUALITY_PRIORITIES = mapOf(
            ULTRA_HD_4K to 100,
            QUAD_HD to 90,
            FULL_HD to 80,
            HD to 70,
            SD to 60,
            LOW_SD to 50,
            VERY_LOW to 40,
            MINIMUM to 30,
            AUTO to 10,
            UNKNOWN to 0
        )
        
        val RESOLUTION_TO_QUALITY = mapOf(
            "3840x2160" to ULTRA_HD_4K,
            "2560x1440" to QUAD_HD,
            "1920x1080" to FULL_HD,
            "1280x720" to HD,
            "854x480" to SD,
            "640x360" to LOW_SD,
            "426x240" to VERY_LOW,
            "256x144" to MINIMUM
        )
    }

    /**
     * Configurações de Formatos de Vídeo
     */
    object VideoFormats {
        const val MP4 = "mp4"
        const val WEBM = "webm"
        const val HLS = "hls"
        const val DASH = "dash"
        const val M3U8 = "m3u8"
        const val MPD = "mpd"
        const val AVI = "avi"
        const val MOV = "mov"
        const val FLV = "flv"
        const val MKV = "mkv"
        
        val SUPPORTED_FORMATS = setOf(
            MP4, WEBM, HLS, DASH, M3U8, MPD, AVI, MOV, FLV, MKV
        )
        
        val STREAMING_FORMATS = setOf(HLS, DASH, M3U8, MPD)
        val PROGRESSIVE_FORMATS = setOf(MP4, WEBM, AVI, MOV, FLV, MKV)
    }

    /**
     * Configurações de Codecs
     */
    object Codecs {
        // Codecs de Vídeo
        const val H264 = "h264"
        const val H265 = "h265"
        const val VP8 = "vp8"
        const val VP9 = "vp9"
        const val AV1 = "av1"
        
        // Codecs de Áudio
        const val AAC = "aac"
        const val MP3 = "mp3"
        const val OPUS = "opus"
        const val VORBIS = "vorbis"
        
        val PREFERRED_VIDEO_CODECS = listOf(AV1, H265, VP9, H264, VP8)
        val PREFERRED_AUDIO_CODECS = listOf(OPUS, AAC, VORBIS, MP3)
    }

    /**
     * Configurações de Detecção YouTube
     */
    object YouTube {
        const val BASE_URL = "youtube.com"
        const val SHORT_URL = "youtu.be"
        const val MOBILE_URL = "m.youtube.com"
        const val EMBED_URL = "youtube.com/embed"
        
        const val YTPLAYER_CONFIG_PATTERN = "ytplayer.config\\s*=\\s*({.+?});\\s*ytplayer"
        const val YT_INITIAL_PLAYER_RESPONSE_PATTERN = "ytInitialPlayerResponse\\s*=\\s*({.+?});\\s*var"
        const val STREAMING_DATA_PATTERN = "\"streamingData\":\\s*({.+?})(?=,\")"
        
        const val GOOGLEVIDEO_DOMAIN = "googlevideo.com"
        const val YOUTUBE_CDN_DOMAIN = "ytimg.com"
        
        val ITAG_QUALITY_MAP = mapOf(
            // 4K
            313 to "2160p", 315 to "2160p", 401 to "2160p", 571 to "2160p",
            // 1440p
            271 to "1440p", 308 to "1440p", 400 to "1440p", 570 to "1440p",
            // 1080p
            137 to "1080p", 248 to "1080p", 299 to "1080p", 399 to "1080p", 569 to "1080p",
            // 720p
            136 to "720p", 247 to "720p", 298 to "720p", 398 to "720p", 568 to "720p",
            // 480p
            135 to "480p", 244 to "480p", 397 to "480p", 567 to "480p",
            // 360p
            134 to "360p", 243 to "360p", 396 to "360p", 566 to "360p",
            // 240p
            133 to "240p", 242 to "240p", 395 to "240p", 565 to "240p",
            // 144p
            160 to "144p", 278 to "144p", 394 to "144p", 564 to "144p"
        )
    }

    /**
     * Configurações de Detecção de Casos Especiais
     */
    object SpecialCases {
        const val QUARANTINE_DURATION_MS = 300000L // 5 minutos
        const val OFFSCREEN_CHECK_INTERVAL_MS = 1000L
        const val PIP_STATE_CHECK_INTERVAL_MS = 500L
        const val LIVE_STREAM_MANIFEST_UPDATE_INTERVAL_MS = 30000L // 30 segundos
        
        const val MIN_VISIBLE_AREA_PERCENTAGE = 50.0
        const val MIN_VIDEO_ELEMENT_SIZE_PX = 100
        
        val QUARANTINE_REASONS = setOf(
            "OFFSCREEN_CONTENT",
            "INVALID_DURATION",
            "LOW_SCORE",
            "DUPLICATE_URL",
            "MALFORMED_URL",
            "UNSUPPORTED_FORMAT",
            "NETWORK_ERROR",
            "PARSING_ERROR"
        )
    }

    /**
     * Configurações de Lifecycle Mobile
     */
    object MobileLifecycle {
        const val BATTERY_LOW_THRESHOLD = 20
        const val BATTERY_CRITICAL_THRESHOLD = 10
        const val MEMORY_LOW_THRESHOLD_MB = 100
        const val MEMORY_CRITICAL_THRESHOLD_MB = 50
        
        const val SCAN_INTERVAL_NORMAL_MS = 1000L
        const val SCAN_INTERVAL_BATTERY_SAVER_MS = 2000L
        const val SCAN_INTERVAL_LOW_MEMORY_MS = 1500L
        const val SCAN_INTERVAL_BACKGROUND_MS = 5000L
        
        const val PERFORMANCE_DEGRADATION_THRESHOLD = 0.7 // 70%
        const val THERMAL_THROTTLING_THRESHOLD = 0.8 // 80%
    }

    /**
     * Configurações de WebView
     */
    object WebView {
        const val USER_AGENT_DESKTOP = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        const val USER_AGENT_MOBILE = "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36"
        
        const val JAVASCRIPT_INTERFACE_NAME = "UltraPreciseDetector"
        const val BRIDGE_CALLBACK_NAME = "onVideoDetected"
        const val ERROR_CALLBACK_NAME = "onDetectionError"
        
        const val DOM_STORAGE_QUOTA_MB = 50
        const val WEB_SQL_DATABASE_QUOTA_MB = 50
        const val APPLICATION_CACHE_QUOTA_MB = 50
        
        const val ZOOM_CONTROLS_ENABLED = false
        const val BUILTIN_ZOOM_CONTROLS = false
        const val DISPLAY_ZOOM_CONTROLS = false
        
        val BLOCKED_DOMAINS = setOf(
            "doubleclick.net",
            "googleadservices.com",
            "googlesyndication.com",
            "googletagmanager.com",
            "facebook.com/tr",
            "analytics.google.com"
        )
    }

    /**
     * Configurações de JavaScript
     */
    object JavaScript {
        const val INJECTION_DELAY_MS = 100L
        const val EXECUTION_RETRY_COUNT = 3
        const val EXECUTION_RETRY_DELAY_MS = 500L
        
        const val VIDEO_DETECTOR_SCRIPT = "video_detector_enhanced.js"
        const val PERFORMANCE_SCRIPT = "performance_monitor.js"
        const val BATTERY_SAVER_SCRIPT = "battery_saver.js"
        const val YOUTUBE_OPTIMIZER_SCRIPT = "youtube_optimizer.js"
        
        val CRITICAL_SCRIPTS = setOf(
            VIDEO_DETECTOR_SCRIPT,
            PERFORMANCE_SCRIPT
        )
        
        val OPTIONAL_SCRIPTS = setOf(
            BATTERY_SAVER_SCRIPT,
            YOUTUBE_OPTIMIZER_SCRIPT
        )
    }

    /**
     * Configurações de Telemetria
     */
    object Telemetry {
        const val MAX_EVENTS_IN_MEMORY = 1000
        const val MAX_METRICS_IN_MEMORY = 500
        const val PERSISTENCE_BATCH_SIZE = 100
        const val DATA_RETENTION_DAYS = 7
        const val REPORT_GENERATION_INTERVAL_MS = 3600000L // 1 hora
        
        const val PERFORMANCE_METRIC_PRECISION = 2
        const val TIMESTAMP_PRECISION_MS = true
        
        val CRITICAL_METRICS = setOf(
            "DETECTION_LATENCY",
            "MEMORY_ALLOCATION",
            "CPU_USAGE",
            "BATTERY_DRAIN",
            "NETWORK_USAGE"
        )
        
        val OPTIONAL_METRICS = setOf(
            "CACHE_HIT_RATE",
            "JAVASCRIPT_EXECUTION_TIME",
            "WEBVIEW_LOAD_TIME",
            "THROUGHPUT"
        )
    }

    /**
     * Configurações de Rede
     */
    object Network {
        const val CONNECTION_TIMEOUT_MS = 10000L
        const val READ_TIMEOUT_MS = 15000L
        const val WRITE_TIMEOUT_MS = 10000L
        
        const val MAX_REDIRECTS = 5
        const val MAX_RETRIES = 3
        const val RETRY_DELAY_MS = 1000L
        
        const val USER_AGENT = "UltraPreciseVideoDetector/1.0"
        
        val ALLOWED_PROTOCOLS = setOf("http", "https")
        val BLOCKED_EXTENSIONS = setOf(".exe", ".zip", ".rar", ".tar", ".gz")
        
        const val MAX_MANIFEST_SIZE_MB = 10
        const val MAX_RESPONSE_SIZE_MB = 50
    }

    /**
     * Configurações de Cache
     */
    object Cache {
        const val VIDEO_CACHE_SIZE_MB = 100
        const val MANIFEST_CACHE_SIZE_MB = 20
        const val THUMBNAIL_CACHE_SIZE_MB = 50
        const val METADATA_CACHE_SIZE_MB = 10
        
        const val VIDEO_CACHE_TTL_MS = 3600000L // 1 hora
        const val MANIFEST_CACHE_TTL_MS = 1800000L // 30 minutos
        const val THUMBNAIL_CACHE_TTL_MS = 86400000L // 24 horas
        const val METADATA_CACHE_TTL_MS = 7200000L // 2 horas
        
        const val CACHE_CLEANUP_THRESHOLD = 0.8 // 80%
        const val CACHE_EVICTION_PERCENTAGE = 0.2 // 20%
    }

    /**
     * Configurações de Logging
     */
    object Logging {
        const val ENABLE_DEBUG_LOGGING = true
        const val ENABLE_PERFORMANCE_LOGGING = true
        const val ENABLE_ERROR_LOGGING = true
        const val ENABLE_TELEMETRY_LOGGING = false
        
        const val MAX_LOG_FILE_SIZE_MB = 10
        const val MAX_LOG_FILES = 5
        const val LOG_ROTATION_INTERVAL_MS = 86400000L // 24 horas
        
        const val LOG_TAG_SYSTEM = "UltraPreciseSystem"
        const val LOG_TAG_DETECTION = "VideoDetection"
        const val LOG_TAG_PERFORMANCE = "Performance"
        const val LOG_TAG_TELEMETRY = "Telemetry"
        const val LOG_TAG_INTEGRATION = "Integration"
    }

    /**
     * Configurações de Segurança
     */
    object Security {
        const val ENABLE_CONTENT_SECURITY_POLICY = true
        const val ENABLE_MIXED_CONTENT_BLOCKING = true
        const val ENABLE_SAFE_BROWSING = true
        
        val ALLOWED_SCHEMES = setOf("http", "https", "data", "blob")
        val BLOCKED_SCHEMES = setOf("file", "ftp", "javascript")
        
        const val MAX_URL_LENGTH = 2048
        const val MAX_TITLE_LENGTH = 200
        const val MAX_METADATA_SIZE_KB = 10
        
        val SANITIZATION_PATTERNS = mapOf(
            "script" to "<script[^>]*>.*?</script>",
            "iframe" to "<iframe[^>]*>.*?</iframe>",
            "object" to "<object[^>]*>.*?</object>",
            "embed" to "<embed[^>]*>.*?</embed>"
        )
    }

    /**
     * Configurações de Experimentação
     */
    object Experiments {
        const val ENABLE_EXPERIMENTAL_FEATURES = false
        const val ENABLE_BETA_YOUTUBE_DETECTION = true
        const val ENABLE_ADVANCED_HLS_PARSING = true
        const val ENABLE_MACHINE_LEARNING_SCORING = false
        const val ENABLE_PREDICTIVE_CACHING = false
        
        const val EXPERIMENT_SAMPLE_RATE = 0.1 // 10%
        const val A_B_TEST_DURATION_MS = 604800000L // 7 dias
    }

    /**
     * Configurações de Desenvolvimento
     */
    object Development {
        const val ENABLE_DEBUG_MODE = false
        const val ENABLE_MOCK_DATA = false
        const val ENABLE_PERFORMANCE_PROFILING = false
        const val ENABLE_MEMORY_LEAK_DETECTION = false
        
        const val DEBUG_OVERLAY_ENABLED = false
        const val VERBOSE_LOGGING_ENABLED = false
        const val CRASH_REPORTING_ENABLED = true
        
        val MOCK_VIDEO_URLS = listOf(
            "https://example.com/video1.mp4",
            "https://example.com/video2.webm",
            "https://example.com/playlist.m3u8"
        )
    }

    /**
     * Validar configurações
     */
    fun validateConfiguration(): List<String> {
        val errors = mutableListOf<String>()
        
        // Validar timeouts
        if (Timeouts.DETECTION_TIMEOUT_MS <= 0) {
            errors.add("DETECTION_TIMEOUT_MS deve ser maior que 0")
        }
        
        // Validar performance
        if (Performance.MAX_CONCURRENT_DETECTIONS <= 0) {
            errors.add("MAX_CONCURRENT_DETECTIONS deve ser maior que 0")
        }
        
        if (Performance.MIN_VIDEO_SCORE < 0) {
            errors.add("MIN_VIDEO_SCORE deve ser maior ou igual a 0")
        }
        
        // Validar otimização
        if (Optimization.SYSTEM_OPTIMIZATION_INTERVAL_MS <= 0) {
            errors.add("SYSTEM_OPTIMIZATION_INTERVAL_MS deve ser maior que 0")
        }
        
        // Validar cache
        if (Cache.VIDEO_CACHE_SIZE_MB <= 0) {
            errors.add("VIDEO_CACHE_SIZE_MB deve ser maior que 0")
        }
        
        return errors
    }

    /**
     * Obter configuração como mapa
     */
    fun toMap(): Map<String, Any> {
        return mapOf(
            "timeouts" to mapOf(
                "detection" to Timeouts.DETECTION_TIMEOUT_MS,
                "javascript" to Timeouts.JAVASCRIPT_EXECUTION_TIMEOUT_MS,
                "qualityMapping" to Timeouts.QUALITY_MAPPING_TIMEOUT_MS,
                "specialCases" to Timeouts.SPECIAL_CASES_TIMEOUT_MS
            ),
            "performance" to mapOf(
                "maxConcurrentDetections" to Performance.MAX_CONCURRENT_DETECTIONS,
                "maxCandidates" to Performance.MAX_CANDIDATES_PER_DETECTION,
                "minVideoScore" to Performance.MIN_VIDEO_SCORE,
                "minVideoDuration" to Performance.MIN_VIDEO_DURATION_SECONDS
            ),
            "optimization" to mapOf(
                "systemInterval" to Optimization.SYSTEM_OPTIMIZATION_INTERVAL_MS,
                "memoryCleanup" to Optimization.MEMORY_CLEANUP_INTERVAL_MS,
                "telemetryFlush" to Optimization.TELEMETRY_FLUSH_INTERVAL_MS
            ),
            "videoQuality" to mapOf(
                "priorities" to VideoQuality.QUALITY_PRIORITIES,
                "resolutionMapping" to VideoQuality.RESOLUTION_TO_QUALITY
            ),
            "formats" to mapOf(
                "supported" to VideoFormats.SUPPORTED_FORMATS,
                "streaming" to VideoFormats.STREAMING_FORMATS,
                "progressive" to VideoFormats.PROGRESSIVE_FORMATS
            ),
            "youtube" to mapOf(
                "itagMapping" to YouTube.ITAG_QUALITY_MAP,
                "domains" to listOf(YouTube.BASE_URL, YouTube.SHORT_URL, YouTube.MOBILE_URL)
            ),
            "cache" to mapOf(
                "videoSize" to Cache.VIDEO_CACHE_SIZE_MB,
                "manifestSize" to Cache.MANIFEST_CACHE_SIZE_MB,
                "thumbnailSize" to Cache.THUMBNAIL_CACHE_SIZE_MB,
                "videoTTL" to Cache.VIDEO_CACHE_TTL_MS
            ),
            "security" to mapOf(
                "allowedSchemes" to Security.ALLOWED_SCHEMES,
                "blockedSchemes" to Security.BLOCKED_SCHEMES,
                "maxUrlLength" to Security.MAX_URL_LENGTH
            ),
            "experiments" to mapOf(
                "experimentalFeatures" to Experiments.ENABLE_EXPERIMENTAL_FEATURES,
                "betaYouTube" to Experiments.ENABLE_BETA_YOUTUBE_DETECTION,
                "advancedHLS" to Experiments.ENABLE_ADVANCED_HLS_PARSING
            )
        )
    }
}
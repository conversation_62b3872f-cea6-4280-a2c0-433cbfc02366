package com.anonymous.videodownloaderapp.services

import android.content.Context
import android.webkit.WebView
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import com.anonymous.videodownloaderapp.features.browser.VideoQuality
import com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore

/**
 * Serviço híbrido que combina o melhor da interceptação nativa Android
 * com filtros rigorosos refinados, eliminando a detecção excessiva (26→3-5 vídeos válidos).
 * 
 * Integra:
 * - NetworkInterceptorService (interceptação OkHttp nativa)
 * - EnhancedVideoDetector (filtros anti-spam ultra-rigorosos)
 * - NetworkVideoCache (cache LRU inteligente)
 * - Fallback para JavaScript quando necessário
 */
class HybridVideoDetectionService private constructor(
    private val context: Context
) {
    
    companion object {
        @Volatile
        private var INSTANCE: HybridVideoDetectionService? = null
        
        fun getInstance(context: Context): HybridVideoDetectionService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: HybridVideoDetectionService(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private val enhancedDetector = EnhancedVideoDetector.getInstance(context)
    private val networkInterceptor = NetworkInterceptorService.getInstance()
    private val cache = NetworkVideoCache.getInstance(context)
    
    // Estado de detecção
    private val _detectedVideos = MutableStateFlow<List<VideoQuality>>(emptyList())
    val detectedVideos: StateFlow<List<VideoQuality>> = _detectedVideos.asStateFlow()
    
    // Cache de páginas ativas
    private val activePages = mutableSetOf<String>()
    
    init {
        setupDetectionPipeline()
    }
    
    private fun setupDetectionPipeline() {
        // Observar resultados do EnhancedVideoDetector
        scope.launch {
            enhancedDetector.results
                .debounce(500) // Debounce para evitar atualizações excessivas
                .collect { candidates ->
                    val videoQualities = convertCandidatesToVideoQualities(candidates)
                    _detectedVideos.value = videoQualities
                }
        }
    }
    
    /**
     * Executar detecção híbrida em uma página
     */
    suspend fun detectVideos(
        webView: WebView?,
        pageUrl: String,
        tabId: String? = null
    ): List<VideoQuality> {
        if (webView == null) return emptyList()
        
        activePages.add(pageUrl)
        
        return try {
            // 1. Detecção nativa aprimorada (método principal)
            val candidates = enhancedDetector.detectVideos(
                webView = webView,
                pageUrl = pageUrl,
                isBackgrounded = false
            )
            
            // 2. Converter para formato compatível com UI existente
            val videoQualities = convertCandidatesToVideoQualities(candidates)
            
            // 3. Atualizar estado global
            _detectedVideos.value = videoQualities
            
            // 4. Atualizar VideoDetectionStore se tabId fornecido
            tabId?.let { updateVideoDetectionStore(it, videoQualities) }
            
            videoQualities
            
        } catch (e: Exception) {
            // Fallback para detecção JavaScript existente em caso de erro
            detectVideosJavaScriptFallback(webView)
        }
    }
    
    /**
     * Fallback para detecção JavaScript existente
     */
    private suspend fun detectVideosJavaScriptFallback(webView: WebView): List<VideoQuality> = 
        suspendCancellableCoroutine { continuation ->
            
            val fallbackScript = """
                (function() {
                    try {
                        const videos = [];
                        const videoElements = document.querySelectorAll('video');
                        
                        videoElements.forEach((video, index) => {
                            if (video.src || video.currentSrc) {
                                const rect = video.getBoundingClientRect();
                                if (rect.width > 200 && rect.height > 200) {
                                    videos.push({
                                        url: video.currentSrc || video.src,
                                        quality: inferQuality(rect.height),
                                        format: inferFormat(video.currentSrc || video.src)
                                    });
                                }
                            }
                        });
                        
                        function inferQuality(height) {
                            if (height >= 2000) return "4K";
                            if (height >= 1000) return "1080p";
                            if (height >= 650) return "720p";
                            if (height >= 400) return "480p";
                            return "360p";
                        }
                        
                        function inferFormat(url) {
                            if (url.includes('.m3u8')) return "hls";
                            if (url.includes('.mpd')) return "dash";
                            if (url.includes('.webm')) return "webm";
                            return "mp4";
                        }
                        
                        return JSON.stringify(videos);
                    } catch (e) {
                        return JSON.stringify([]);
                    }
                })();
            """.trimIndent()
            
            webView.evaluateJavascript(fallbackScript) { result ->
                try {
                    val videoQualities = parseJavaScriptResult(result)
                    continuation.resume(videoQualities, null)
                } catch (e: Exception) {
                    continuation.resume(emptyList(), null)
                }
            }
        }
    
    private fun parseJavaScriptResult(result: String?): List<VideoQuality> {
        if (result.isNullOrBlank() || result == "null") return emptyList()
        
        return try {
            val cleanResult = result.removeSurrounding("\"").replace("\\", "")
            val jsonArray = org.json.JSONArray(cleanResult)
            
            (0 until jsonArray.length()).map { i ->
                val item = jsonArray.getJSONObject(i)
                VideoQuality(
                    quality = item.getString("quality"),
                    url = item.getString("url"),
                    format = item.getString("format"),
                    isDownloaded = false // Será verificado pelo ViewModel
                )
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * Converter candidatos do EnhancedVideoDetector para VideoQuality (formato da UI)
     */
    private fun convertCandidatesToVideoQualities(
        candidates: List<EnhancedVideoDetector.VideoCandidate>
    ): List<VideoQuality> {
        return candidates
            .filter { !it.hasDrm } // Filtrar conteúdo protegido por DRM
            .sortedByDescending { it.score } // Ordenar por relevância
            .take(10) // Limitar a 10 melhores resultados
            .map { candidate ->
                VideoQuality(
                    quality = candidate.quality,
                    url = candidate.url,
                    format = candidate.format,
                    isDownloaded = false // Será verificado pelo ViewModel se necessário
                )
            }
    }
    
    /**
     * Atualizar VideoDetectionStore (compatibilidade com sistema existente)
     */
    private fun updateVideoDetectionStore(tabId: String, videoQualities: List<VideoQuality>) {
        // Converte para pares (quality, url) e aplica no store compatível
        val pairs = videoQualities.map { it.quality to it.url }
        VideoDetectionStore.upsertPairs(tabId, pairs)
    }
    
    /**
     * Configurar interceptador de rede em cliente OkHttp
     */
    fun configureOkHttpClient(builder: okhttp3.OkHttpClient.Builder): okhttp3.OkHttpClient.Builder {
        return builder.addInterceptor(networkInterceptor)
    }
    
    /**
     * Configurar interceptação no WebView
     */
    fun shouldInterceptWebViewRequest(
        request: android.webkit.WebResourceRequest?,
        pageUrl: String
    ): android.webkit.WebResourceResponse? {

        // O NetworkInterceptorService já fará a interceptação via OkHttp
        // Aqui apenas registramos a página como ativa para correlação
        if (request?.url != null) {
            activePages.add(pageUrl)
        }

        return null // Não bloquear requests, apenas observar
    }

    /**
     * Processar manifesto interceptado pelo WebView
     * Este método é chamado quando um manifesto (.m3u8 ou .mpd) é interceptado
     */
    fun onManifestResponse(
        request: android.webkit.WebResourceRequest,
        manifestContent: String
    ) {
        scope.launch(Dispatchers.IO) {
            try {
                val url = request.url.toString()
                val pageUrl = activePages.firstOrNull() ?: url

                // Determinar tipo de manifesto
                val mediaType = when {
                    url.endsWith(".m3u8") -> NetworkInterceptorService.MediaType.HLS_MASTER
                    url.endsWith(".mpd") -> NetworkInterceptorService.MediaType.DASH_MANIFEST
                    else -> NetworkInterceptorService.MediaType.UNKNOWN
                }

                if (mediaType != NetworkInterceptorService.MediaType.UNKNOWN) {
                    // Criar MediaRequest para compatibilidade com sistema existente
                    val mediaRequest = NetworkInterceptorService.MediaRequest(
                        id = generateRequestId(url),
                        url = url,
                        method = request.method ?: "GET",
                        contentType = getContentTypeFromUrl(url),
                        contentLength = manifestContent.length.toLong(),
                        userAgent = request.requestHeaders?.get("User-Agent"),
                        referer = request.requestHeaders?.get("Referer"),
                        timestamp = System.currentTimeMillis(),
                        mediaType = mediaType,
                        frameOrigin = pageUrl
                    )

                    // Processar manifesto através do EnhancedVideoDetector
                    enhancedDetector.processManifestContent(mediaRequest, manifestContent)
                }

            } catch (e: Exception) {
                android.util.Log.w("HybridVideoDetection", "Erro ao processar manifesto: ${e.message}")
            }
        }
    }

    /**
     * Gerar ID único para requisição
     */
    private fun generateRequestId(url: String): String {
        return "${System.currentTimeMillis()}_${url.hashCode()}"
    }

    /**
     * Determinar content-type baseado na URL
     */
    private fun getContentTypeFromUrl(url: String): String {
        return when {
            url.endsWith(".m3u8") -> "application/vnd.apple.mpegurl"
            url.endsWith(".mpd") -> "application/dash+xml"
            else -> "application/octet-stream"
        }
    }
    
    /**
     * Notificar que página foi para background
     */
    fun onPageBackgrounded(pageUrl: String) {
        enhancedDetector.onBackgrounded()
        activePages.remove(pageUrl)
    }
    
    /**
     * Notificar que página voltou para foreground
     */
    fun onPageForegrounded(pageUrl: String) {
        enhancedDetector.onForegrounded()
        activePages.add(pageUrl)
    }
    
    /**
     * Limpar dados de uma página
     */
    fun clearPage(pageUrl: String) {
        enhancedDetector.clearPage(pageUrl)
        activePages.remove(pageUrl)
    }
    
    /**
     * Obter estatísticas de detecção para debugging
     */
    fun getDetectionStats(): Map<String, Any> {
        val enhancedStats = enhancedDetector.getDetectionStats()
        val networkStats = networkInterceptor.getStats()
        val cacheStats = cache.getStats()
        
        return mapOf(
            "enhanced" to enhancedStats,
            "network" to networkStats,
            "cache" to cacheStats,
            "activePages" to activePages.size,
            "currentVideoCount" to _detectedVideos.value.size
        )
    }
    
    /**
     * Limpar cache e estado antigo
     */
    fun performMaintenance() {
        networkInterceptor.cleanup()
        cache.invalidateExpired()
        
        // Remover páginas inativas há mais de 10 minutos
        val inactivePages = activePages.filter { pageUrl ->
            // Esta é uma simplificação - idealmente teríamos timestamp por página
            false // Por enquanto, manter todas as páginas ativas
        }
        inactivePages.forEach { clearPage(it) }
    }
    
    /**
     * Compatibilidade com sistema existente - LiveData
     */
    fun getDetectedVideosLiveData(): LiveData<List<VideoQuality>> {
        return detectedVideos.asLiveData()
    }
    
    /**
     * Compatibilidade com sistema existente - callback
     */
    fun observeDetectedVideos(callback: (List<VideoQuality>) -> Unit) {
        scope.launch {
            detectedVideos.collect { videos ->
                callback(videos)
            }
        }
    }
}
package com.anonymous.videodownloaderapp.data

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import kotlinx.coroutines.flow.Flow

@Dao
interface DownloadDao {

    @Query("SELECT * FROM downloads ORDER BY createdAt DESC")
    fun observeAll(): Flow<List<DownloadItem>>

    @Query("SELECT * FROM downloads WHERE id = :id")
    fun observeById(id: Long): Flow<DownloadItem?>

    @Query("SELECT * FROM downloads WHERE status IN (:statuses) ORDER BY createdAt ASC")
    fun observeByStatuses(statuses: List<String>): Flow<List<DownloadItem>>

    @Query("SELECT * FROM downloads ORDER BY createdAt DESC")
    suspend fun getAll(): List<DownloadItem>

    @Query("SELECT * FROM downloads WHERE id = :id")
    suspend fun getById(id: Long): DownloadItem?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun upsert(item: DownloadItem): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun upsertAll(items: List<DownloadItem>): List<Long>

    @Update
    suspend fun update(item: DownloadItem)

    @Delete
    suspend fun delete(item: DownloadItem)

    @Query("DELETE FROM downloads WHERE id = :id")
    suspend fun deleteById(id: Long)

    @Query("DELETE FROM downloads WHERE status IN ('Completed','Canceled','Failed')")
    suspend fun pruneFinished()

    @Transaction
    suspend fun markStatus(id: Long, status: DownloadItem.Status, progress: Int? = null, bytesDownloaded: Long? = null, totalBytes: Long? = null, error: String? = null) {
        val current = getById(id) ?: return
        val updated = current.copy(
            status = status,
            progress = progress ?: current.progress,
            bytesDownloaded = bytesDownloaded ?: current.bytesDownloaded,
            totalBytes = totalBytes ?: current.totalBytes,
            error = error,
            updatedAt = System.currentTimeMillis()
        )
        upsert(updated)
    }
}
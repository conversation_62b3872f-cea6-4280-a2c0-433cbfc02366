package com.anonymous.videodownloaderapp.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0005\u001a\u00020\u00062\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00060\bH\u0007J\u0006\u0010\n\u001a\u00020\tJ\u0006\u0010\u000b\u001a\u00020\u0006R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/anonymous/videodownloaderapp/utils/PermissionManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "RequestStoragePermission", "", "onPermissionResult", "Lkotlin/Function1;", "", "hasStoragePermission", "openAppSettings", "app_debug"})
public final class PermissionManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    
    public PermissionManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    public final boolean hasStoragePermission() {
        return false;
    }
    
    @androidx.compose.runtime.Composable()
    public final void RequestStoragePermission(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onPermissionResult) {
    }
    
    public final void openAppSettings() {
    }
}
-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:1:1-50:12
MERGED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:1:1-50:12
INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42f2efb6e1c2feb482286d46a2a42547\transformed\navigation-common-2.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff709226474a1a224df1d31ae0d8e807\transformed\navigation-runtime-2.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5361153845ee84d3852ecd7b6bd6f76\transformed\navigation-common-ktx-2.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2795b1c73330331c65c4dd2fd951187f\transformed\navigation-runtime-ktx-2.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1961c7034dacb2c8624b328c862c6ce8\transformed\navigation-compose-2.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eb3bbe4a59da4a3f188126812803312\transformed\material3-window-size-class-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\576ca5b088d8fb564e2e136af4f08d54\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-swiperefresh:0.35.1-alpha] C:\Users\<USER>\.gradle\caches\8.13\transforms\baf5168309fa1a88a5502e6f651b6a96\transformed\accompanist-swiperefresh-0.35.1-alpha\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0462baff763d7d75430196175f50da7\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a7ba347013c0420cafd6958727d1505\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c594a6b1ad90298d1781df40da764bd0\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6955b04fa776635707c03906f73e606\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\60d715a0f2b0d7f533caf35e6c874ddb\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c7c9afc6246127cb50c9dbc61048925\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\60c7fe9ec885825eaa97feb2891aada3\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c60997e857577228325859e2a3ef1e8\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\96561170e128b1f332b861dfc151c9de\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e627fd0143224879b291aadc32cf72\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\8756a9a14c5afa095585149a63006113\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7de50430aee34e7ad4001b6681e4a670\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\070263b76bf216e0a6600155939e2ee7\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6ed5766e9b98889fbf72d82c0c83570\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3131c1aa82c9f72e2ded93fb374bdf9d\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:17:1-145:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b6440c5b30b54fb1514c977cfe8efa\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\33125a302820e97b01cd8e714906cf59\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39302d5a572df16ea421447d4c3926f9\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\366aa02d18db07ca95bf2d64251e5a94\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4205109c592e5053e756eb92ed65bdb\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d68ad7f9e9d72fa86057d68a6c8bb2\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8472d1bf5b69b0590fe04f3e0b5de8e3\transformed\activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd6dee2f2e0ec81c5fe5338f5d2077f6\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c32d3dfe6a15962a6af8118d9a55d93\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1943ff4d6753f16cfb82cc2a03ce381e\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42be2ddf01e65d0ba777529ca7adab26\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fc1cb71603aaa9009cbd662e9e56d65\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8501eba9630911da327198a3d907b6e6\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\19dbdb4d446295775d71c6ca0010df0c\transformed\media3-ui-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\47e7eab8c09c0fb8e0c511843a157f01\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource-rtmp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d213cfaad7750714465c63884fe9a5b\transformed\media3-datasource-rtmp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\01a918daa784908ad14dcb46448b577a\transformed\media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cb7dc3a2ef05b55e2bd0c29f7433b4c\transformed\media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe422cf646ba1f08e7f6fd8489616849\transformed\media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8c46adf27a907ed33bb305b0a2133d4\transformed\media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dc944b690617b4500a80642a3ad8e7\transformed\media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b62bde14152264e7bbe36d172c73168\transformed\media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0072bdaefbbb2d2efbe17a9c10efa45\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e08d908e33c874025070dc6fe97f249\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c83662cb1041180272524de55f94ad06\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1af143e343a27f72976b8e454f981c64\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92fbbc92b6fb684da8fa5eb2bdc5b936\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbbb5a09ebeae53de810c1415003f4b7\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\685547573664f2b9f1874dafb81c065a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00196df4360fdd02cea71ecf06fd5cb4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfe0fde506fd633617799f78ffca6685\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f34ae211ebb29ecec89a2408f59e7d33\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bdf18766bf9c656aac6ec994af10bba\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f8b30d41471ed88ee9203944d4519cc\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8db2d918435e0e7889f137a96988ede6\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2168f7f41a709f9570575d13a575b23c\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff13efd507c8bff6d9117dc5a6d9fd03\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25046cabc353ac08acf12b5a6b9d8d6f\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6a4f675a1c39917bb551dba38e0122f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ce70839567d6c4b60cef98a2de9dc28\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\067f53ebc327f7e73160416fb7ee5e52\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\44392e9eaeb95e11579e7264d96efa4d\transformed\lifecycle-livedata-core-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d648d02bd3084711a826d69b78d2ea71\transformed\lifecycle-viewmodel-2.8.4\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e14f8c6727631997a484d4a521a56074\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cb5c83a7fd9d1ac42d374663d903f72\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\acd0465328e31512eeaedb149195a780\transformed\lifecycle-livedata-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edf9dd1b1432cabec18939fa40ae857\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f5c123459af543de6d6fc8802133ea5\transformed\lifecycle-viewmodel-savedstate-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a382d1a4dd45244ed339c43a26eea89\transformed\lifecycle-livedata-core-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd53b8ef6bfca0d1c4d3a6c21291aacf\transformed\lifecycle-viewmodel-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4867b39c0d118fe8d524a580714cfb92\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\08fa2be6652353aee575f6704a655f70\transformed\lifecycle-service-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c94064379ea8a3c6f5bc3ed7e64ba53\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\33b434b7cde7a9983ca8fed82a866d86\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c80aed54b7d31ca95e13b7eeb196de5\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea0d069a154b88d6448f277841df52b5\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\44f81569fc33f73baf272e6c5a360806\transformed\activity-ktx-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\935efe43e299539c2bb49920eaec6c87\transformed\activity-compose-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a800c847769fe95420e73637849cf0da\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b17c8c96d9a0739f7d9b43fad36928\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8794d0b128aa3edcc5d4470ed36e80bc\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f04692d5a3597d3d6f1c6f03a2b7e41\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30f985ba229c640713145bb9b4d038a0\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3484a38a6de73c7f5686e30856b0bfd8\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8c747399e306837bd3acd3510494939\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb5a61ec94c32e9a750e1a2f24f76f91\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fad4e0870873d41876759bc89acd897\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8ef0843abdc62dd84f259f796c32cd8\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac89a66b5a39999755d51f1932f49949\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72eb4bcae497dc27c30c360c216bb7e2\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0574105a6b6fcaf77bb4bdffe1dd6396\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41a19ed2afa29892250d8872d0b64e5a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b72fae786aa48ac13d67133e4abbd7dd\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6f97ad691811ff5496be94151be13c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cac95faefb62d5cde8f72c20acfb640a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5d181eaad50a78cc3f3241c03c1cea7\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7d96272c582f4947b822c6d2dcafa25\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c995188b444e3a1bb1db4d1c7ed8b2e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c25600e127767caa79a1d4bbb09a84d\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d751f784209f974fa289af4c49f2648b\transformed\rtmp-client-3.2.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d751f784209f974fa289af4c49f2648b\transformed\rtmp-client-3.2.0\AndroidManifest.xml:9:5-67
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d751f784209f974fa289af4c49f2648b\transformed\rtmp-client-3.2.0\AndroidManifest.xml:9:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:6:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:6:22-72
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:7:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:7:22-74
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:10:5-77
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:10:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:11:5-87
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:11:22-84
application
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:13:5-48:19
MERGED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:13:5-48:19
MERGED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:13:5-48:19
INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6ed5766e9b98889fbf72d82c0c83570\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6ed5766e9b98889fbf72d82c0c83570\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:28:5-143:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b6440c5b30b54fb1514c977cfe8efa\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b6440c5b30b54fb1514c977cfe8efa\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\33125a302820e97b01cd8e714906cf59\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\33125a302820e97b01cd8e714906cf59\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c32d3dfe6a15962a6af8118d9a55d93\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c32d3dfe6a15962a6af8118d9a55d93\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2168f7f41a709f9570575d13a575b23c\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2168f7f41a709f9570575d13a575b23c\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edf9dd1b1432cabec18939fa40ae857\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edf9dd1b1432cabec18939fa40ae857\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a800c847769fe95420e73637849cf0da\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a800c847769fe95420e73637849cf0da\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac89a66b5a39999755d51f1932f49949\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac89a66b5a39999755d51f1932f49949\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6f97ad691811ff5496be94151be13c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6f97ad691811ff5496be94151be13c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:18:9-54
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:18:9-54
	android:icon
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:16:9-43
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:16:9-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:19:9-35
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:19:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:17:9-41
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:17:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:21:9-43
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:21:9-43
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:22:9-29
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:15:9-35
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:15:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:20:9-41
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:20:9-41
	tools:replace
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:14:9-40
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:14:9-40
activity#com.anonymous.videodownloaderapp.MainActivity
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:24:9-32:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:26:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:27:13-45
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:25:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:28:13-31:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:30:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:30:27-74
receiver#com.anonymous.videodownloaderapp.services.DownloadActionReceiver
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:35:9-43:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:37:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:36:13-60
intent-filter#action:name:com.anonymous.videodownloaderapp.action.CANCEL+action:name:com.anonymous.videodownloaderapp.action.PAUSE+action:name:com.anonymous.videodownloaderapp.action.RESUME
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:38:13-42:29
action#com.anonymous.videodownloaderapp.action.PAUSE
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:39:17-88
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:39:25-85
action#com.anonymous.videodownloaderapp.action.RESUME
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:40:17-89
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:40:25-86
action#com.anonymous.videodownloaderapp.action.CANCEL
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:41:17-89
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\main\AndroidManifest.xml:41:25-86
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml:4:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml:4:22-75
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml
MERGED from [androidx.navigation:navigation-common:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42f2efb6e1c2feb482286d46a2a42547\transformed\navigation-common-2.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42f2efb6e1c2feb482286d46a2a42547\transformed\navigation-common-2.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff709226474a1a224df1d31ae0d8e807\transformed\navigation-runtime-2.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff709226474a1a224df1d31ae0d8e807\transformed\navigation-runtime-2.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5361153845ee84d3852ecd7b6bd6f76\transformed\navigation-common-ktx-2.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5361153845ee84d3852ecd7b6bd6f76\transformed\navigation-common-ktx-2.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2795b1c73330331c65c4dd2fd951187f\transformed\navigation-runtime-ktx-2.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2795b1c73330331c65c4dd2fd951187f\transformed\navigation-runtime-ktx-2.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1961c7034dacb2c8624b328c862c6ce8\transformed\navigation-compose-2.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1961c7034dacb2c8624b328c862c6ce8\transformed\navigation-compose-2.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eb3bbe4a59da4a3f188126812803312\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9eb3bbe4a59da4a3f188126812803312\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\576ca5b088d8fb564e2e136af4f08d54\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\576ca5b088d8fb564e2e136af4f08d54\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-swiperefresh:0.35.1-alpha] C:\Users\<USER>\.gradle\caches\8.13\transforms\baf5168309fa1a88a5502e6f651b6a96\transformed\accompanist-swiperefresh-0.35.1-alpha\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-swiperefresh:0.35.1-alpha] C:\Users\<USER>\.gradle\caches\8.13\transforms\baf5168309fa1a88a5502e6f651b6a96\transformed\accompanist-swiperefresh-0.35.1-alpha\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0462baff763d7d75430196175f50da7\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0462baff763d7d75430196175f50da7\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a7ba347013c0420cafd6958727d1505\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a7ba347013c0420cafd6958727d1505\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c594a6b1ad90298d1781df40da764bd0\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c594a6b1ad90298d1781df40da764bd0\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6955b04fa776635707c03906f73e606\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6955b04fa776635707c03906f73e606\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\60d715a0f2b0d7f533caf35e6c874ddb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\60d715a0f2b0d7f533caf35e6c874ddb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c7c9afc6246127cb50c9dbc61048925\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c7c9afc6246127cb50c9dbc61048925\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\60c7fe9ec885825eaa97feb2891aada3\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\60c7fe9ec885825eaa97feb2891aada3\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c60997e857577228325859e2a3ef1e8\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c60997e857577228325859e2a3ef1e8\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\96561170e128b1f332b861dfc151c9de\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\96561170e128b1f332b861dfc151c9de\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e627fd0143224879b291aadc32cf72\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e627fd0143224879b291aadc32cf72\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\8756a9a14c5afa095585149a63006113\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\8756a9a14c5afa095585149a63006113\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7de50430aee34e7ad4001b6681e4a670\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7de50430aee34e7ad4001b6681e4a670\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\070263b76bf216e0a6600155939e2ee7\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\070263b76bf216e0a6600155939e2ee7\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6ed5766e9b98889fbf72d82c0c83570\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6ed5766e9b98889fbf72d82c0c83570\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3131c1aa82c9f72e2ded93fb374bdf9d\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3131c1aa82c9f72e2ded93fb374bdf9d\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b6440c5b30b54fb1514c977cfe8efa\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b6440c5b30b54fb1514c977cfe8efa\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\33125a302820e97b01cd8e714906cf59\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\33125a302820e97b01cd8e714906cf59\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39302d5a572df16ea421447d4c3926f9\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39302d5a572df16ea421447d4c3926f9\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\366aa02d18db07ca95bf2d64251e5a94\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\366aa02d18db07ca95bf2d64251e5a94\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4205109c592e5053e756eb92ed65bdb\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4205109c592e5053e756eb92ed65bdb\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d68ad7f9e9d72fa86057d68a6c8bb2\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\42d68ad7f9e9d72fa86057d68a6c8bb2\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8472d1bf5b69b0590fe04f3e0b5de8e3\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8472d1bf5b69b0590fe04f3e0b5de8e3\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd6dee2f2e0ec81c5fe5338f5d2077f6\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd6dee2f2e0ec81c5fe5338f5d2077f6\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c32d3dfe6a15962a6af8118d9a55d93\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c32d3dfe6a15962a6af8118d9a55d93\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1943ff4d6753f16cfb82cc2a03ce381e\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1943ff4d6753f16cfb82cc2a03ce381e\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42be2ddf01e65d0ba777529ca7adab26\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42be2ddf01e65d0ba777529ca7adab26\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fc1cb71603aaa9009cbd662e9e56d65\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fc1cb71603aaa9009cbd662e9e56d65\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8501eba9630911da327198a3d907b6e6\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8501eba9630911da327198a3d907b6e6\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\19dbdb4d446295775d71c6ca0010df0c\transformed\media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\19dbdb4d446295775d71c6ca0010df0c\transformed\media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\47e7eab8c09c0fb8e0c511843a157f01\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\47e7eab8c09c0fb8e0c511843a157f01\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-rtmp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d213cfaad7750714465c63884fe9a5b\transformed\media3-datasource-rtmp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-rtmp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d213cfaad7750714465c63884fe9a5b\transformed\media3-datasource-rtmp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\01a918daa784908ad14dcb46448b577a\transformed\media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\01a918daa784908ad14dcb46448b577a\transformed\media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cb7dc3a2ef05b55e2bd0c29f7433b4c\transformed\media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cb7dc3a2ef05b55e2bd0c29f7433b4c\transformed\media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe422cf646ba1f08e7f6fd8489616849\transformed\media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe422cf646ba1f08e7f6fd8489616849\transformed\media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8c46adf27a907ed33bb305b0a2133d4\transformed\media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8c46adf27a907ed33bb305b0a2133d4\transformed\media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dc944b690617b4500a80642a3ad8e7\transformed\media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dc944b690617b4500a80642a3ad8e7\transformed\media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b62bde14152264e7bbe36d172c73168\transformed\media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b62bde14152264e7bbe36d172c73168\transformed\media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0072bdaefbbb2d2efbe17a9c10efa45\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0072bdaefbbb2d2efbe17a9c10efa45\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e08d908e33c874025070dc6fe97f249\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e08d908e33c874025070dc6fe97f249\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c83662cb1041180272524de55f94ad06\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c83662cb1041180272524de55f94ad06\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1af143e343a27f72976b8e454f981c64\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1af143e343a27f72976b8e454f981c64\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92fbbc92b6fb684da8fa5eb2bdc5b936\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92fbbc92b6fb684da8fa5eb2bdc5b936\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbbb5a09ebeae53de810c1415003f4b7\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbbb5a09ebeae53de810c1415003f4b7\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\685547573664f2b9f1874dafb81c065a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\685547573664f2b9f1874dafb81c065a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00196df4360fdd02cea71ecf06fd5cb4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00196df4360fdd02cea71ecf06fd5cb4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfe0fde506fd633617799f78ffca6685\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfe0fde506fd633617799f78ffca6685\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f34ae211ebb29ecec89a2408f59e7d33\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f34ae211ebb29ecec89a2408f59e7d33\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bdf18766bf9c656aac6ec994af10bba\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bdf18766bf9c656aac6ec994af10bba\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f8b30d41471ed88ee9203944d4519cc\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f8b30d41471ed88ee9203944d4519cc\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8db2d918435e0e7889f137a96988ede6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8db2d918435e0e7889f137a96988ede6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2168f7f41a709f9570575d13a575b23c\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2168f7f41a709f9570575d13a575b23c\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff13efd507c8bff6d9117dc5a6d9fd03\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff13efd507c8bff6d9117dc5a6d9fd03\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25046cabc353ac08acf12b5a6b9d8d6f\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25046cabc353ac08acf12b5a6b9d8d6f\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6a4f675a1c39917bb551dba38e0122f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6a4f675a1c39917bb551dba38e0122f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ce70839567d6c4b60cef98a2de9dc28\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ce70839567d6c4b60cef98a2de9dc28\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\067f53ebc327f7e73160416fb7ee5e52\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\067f53ebc327f7e73160416fb7ee5e52\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\44392e9eaeb95e11579e7264d96efa4d\transformed\lifecycle-livedata-core-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\44392e9eaeb95e11579e7264d96efa4d\transformed\lifecycle-livedata-core-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d648d02bd3084711a826d69b78d2ea71\transformed\lifecycle-viewmodel-2.8.4\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d648d02bd3084711a826d69b78d2ea71\transformed\lifecycle-viewmodel-2.8.4\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e14f8c6727631997a484d4a521a56074\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e14f8c6727631997a484d4a521a56074\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cb5c83a7fd9d1ac42d374663d903f72\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cb5c83a7fd9d1ac42d374663d903f72\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\acd0465328e31512eeaedb149195a780\transformed\lifecycle-livedata-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\acd0465328e31512eeaedb149195a780\transformed\lifecycle-livedata-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edf9dd1b1432cabec18939fa40ae857\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edf9dd1b1432cabec18939fa40ae857\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f5c123459af543de6d6fc8802133ea5\transformed\lifecycle-viewmodel-savedstate-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f5c123459af543de6d6fc8802133ea5\transformed\lifecycle-viewmodel-savedstate-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a382d1a4dd45244ed339c43a26eea89\transformed\lifecycle-livedata-core-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a382d1a4dd45244ed339c43a26eea89\transformed\lifecycle-livedata-core-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd53b8ef6bfca0d1c4d3a6c21291aacf\transformed\lifecycle-viewmodel-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd53b8ef6bfca0d1c4d3a6c21291aacf\transformed\lifecycle-viewmodel-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4867b39c0d118fe8d524a580714cfb92\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4867b39c0d118fe8d524a580714cfb92\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\08fa2be6652353aee575f6704a655f70\transformed\lifecycle-service-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\08fa2be6652353aee575f6704a655f70\transformed\lifecycle-service-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c94064379ea8a3c6f5bc3ed7e64ba53\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c94064379ea8a3c6f5bc3ed7e64ba53\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\33b434b7cde7a9983ca8fed82a866d86\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\33b434b7cde7a9983ca8fed82a866d86\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c80aed54b7d31ca95e13b7eeb196de5\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c80aed54b7d31ca95e13b7eeb196de5\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea0d069a154b88d6448f277841df52b5\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea0d069a154b88d6448f277841df52b5\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\44f81569fc33f73baf272e6c5a360806\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\44f81569fc33f73baf272e6c5a360806\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\935efe43e299539c2bb49920eaec6c87\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\935efe43e299539c2bb49920eaec6c87\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a800c847769fe95420e73637849cf0da\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a800c847769fe95420e73637849cf0da\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b17c8c96d9a0739f7d9b43fad36928\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b17c8c96d9a0739f7d9b43fad36928\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8794d0b128aa3edcc5d4470ed36e80bc\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8794d0b128aa3edcc5d4470ed36e80bc\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f04692d5a3597d3d6f1c6f03a2b7e41\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f04692d5a3597d3d6f1c6f03a2b7e41\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30f985ba229c640713145bb9b4d038a0\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30f985ba229c640713145bb9b4d038a0\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3484a38a6de73c7f5686e30856b0bfd8\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3484a38a6de73c7f5686e30856b0bfd8\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8c747399e306837bd3acd3510494939\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8c747399e306837bd3acd3510494939\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb5a61ec94c32e9a750e1a2f24f76f91\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb5a61ec94c32e9a750e1a2f24f76f91\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fad4e0870873d41876759bc89acd897\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fad4e0870873d41876759bc89acd897\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8ef0843abdc62dd84f259f796c32cd8\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8ef0843abdc62dd84f259f796c32cd8\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac89a66b5a39999755d51f1932f49949\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac89a66b5a39999755d51f1932f49949\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72eb4bcae497dc27c30c360c216bb7e2\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72eb4bcae497dc27c30c360c216bb7e2\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0574105a6b6fcaf77bb4bdffe1dd6396\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0574105a6b6fcaf77bb4bdffe1dd6396\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41a19ed2afa29892250d8872d0b64e5a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41a19ed2afa29892250d8872d0b64e5a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b72fae786aa48ac13d67133e4abbd7dd\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b72fae786aa48ac13d67133e4abbd7dd\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6f97ad691811ff5496be94151be13c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6f97ad691811ff5496be94151be13c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cac95faefb62d5cde8f72c20acfb640a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cac95faefb62d5cde8f72c20acfb640a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5d181eaad50a78cc3f3241c03c1cea7\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5d181eaad50a78cc3f3241c03c1cea7\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7d96272c582f4947b822c6d2dcafa25\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7d96272c582f4947b822c6d2dcafa25\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c995188b444e3a1bb1db4d1c7ed8b2e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c995188b444e3a1bb1db4d1c7ed8b2e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c25600e127767caa79a1d4bbb09a84d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c25600e127767caa79a1d4bbb09a84d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d751f784209f974fa289af4c49f2648b\transformed\rtmp-client-3.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d751f784209f974fa289af4c49f2648b\transformed\rtmp-client-3.2.0\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\app navegador\projeto-3\android\app\src\debug\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6ed5766e9b98889fbf72d82c0c83570\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6ed5766e9b98889fbf72d82c0c83570\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6ed5766e9b98889fbf72d82c0c83570\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:23:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b62bde14152264e7bbe36d172c73168\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b62bde14152264e7bbe36d172c73168\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c83662cb1041180272524de55f94ad06\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c83662cb1041180272524de55f94ad06\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:24:22-76
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:22-78
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c32d3dfe6a15962a6af8118d9a55d93\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c32d3dfe6a15962a6af8118d9a55d93\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edf9dd1b1432cabec18939fa40ae857\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edf9dd1b1432cabec18939fa40ae857\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6f97ad691811ff5496be94151be13c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6f97ad691811ff5496be94151be13c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c4b12a84dcb0c82f99d95051f2b807\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c32d3dfe6a15962a6af8118d9a55d93\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c32d3dfe6a15962a6af8118d9a55d93\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c32d3dfe6a15962a6af8118d9a55d93\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2168f7f41a709f9570575d13a575b23c\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2168f7f41a709f9570575d13a575b23c\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2168f7f41a709f9570575d13a575b23c\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2168f7f41a709f9570575d13a575b23c\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2168f7f41a709f9570575d13a575b23c\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2168f7f41a709f9570575d13a575b23c\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.anonymous.videodownloaderapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.anonymous.videodownloaderapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2b74cc75dcc4061ff406ef39af9f1c\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edf9dd1b1432cabec18939fa40ae857\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edf9dd1b1432cabec18939fa40ae857\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7edf9dd1b1432cabec18939fa40ae857\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a800c847769fe95420e73637849cf0da\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a800c847769fe95420e73637849cf0da\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a800c847769fe95420e73637849cf0da\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a800c847769fe95420e73637849cf0da\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a800c847769fe95420e73637849cf0da\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\285559031beb598746b8b596c8896164\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92

package com.anonymous.videodownloaderapp.optimization

import android.content.Context
import com.anonymous.videodownloaderapp.data.DownloadItem
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Semaphore
import okhttp3.OkHttpClient
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

class ParallelDownloadManager(private val context: Context) {
    
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val activeDownloads = ConcurrentHashMap<String, Job>()
    private val downloadProgress = ConcurrentHashMap<String, AtomicLong>()
    
    // Connection pool for parallel downloads
    private val httpClient = run {
        val builder = OkHttpClient.Builder()
            .connectionPool(okhttp3.ConnectionPool(10, 5, java.util.concurrent.TimeUnit.MINUTES))
        
        // Configurar com HybridVideoDetectionService para interceptação de rede
        com.anonymous.videodownloaderapp.services.HybridVideoDetectionService
            .getInstance(context)
            .configureOkHttpClient(builder)
        
        builder.build()
    }
    
    // Limit concurrent downloads
    private val maxConcurrentDownloads = 3
    private val semaphore = Semaphore(maxConcurrentDownloads)
    
    // Chunk size for parallel downloads (5MB)
    private val chunkSize = 5 * 1024 * 1024L
    
    data class DownloadChunk(
        val chunkId: Int,
        val startByte: Long,
        val endByte: Long,
        val file: File,
        var downloaded: Boolean = false
    )
    
    data class ParallelDownload(
        val downloadItem: DownloadItem,
        val chunks: List<DownloadChunk>,
        val totalSize: Long,
        var completedChunks: Int = 0
    )
    
    fun startParallelDownload(downloadItem: DownloadItem) {
        coroutineScope.launch {
            semaphore.acquire()
            try {
                val parallelDownload = createParallelDownload(downloadItem)
                downloadParallelChunks(parallelDownload)
            } finally {
                semaphore.release()
            }
        }
    }
    
    private fun createParallelDownload(downloadItem: DownloadItem): ParallelDownload {
        val totalSize = downloadItem.totalBytes
        val chunkCount = ((totalSize + chunkSize - 1) / chunkSize).toInt()
        
        val chunks = mutableListOf<DownloadChunk>()
        val downloadDir = File(context.getExternalFilesDir(null), "downloads/${downloadItem.id}")
        downloadDir.mkdirs()
        
        for (i in 0 until chunkCount) {
            val startByte = i * chunkSize
            val endByte = minOf((i + 1) * chunkSize - 1, totalSize - 1)
            val chunkFile = File(downloadDir, "chunk_${i}.tmp")
            
            chunks.add(DownloadChunk(i, startByte, endByte, chunkFile))
        }
        
        return ParallelDownload(downloadItem, chunks, totalSize)
    }
    
    private suspend fun downloadParallelChunks(parallelDownload: ParallelDownload) {
        val downloadId = parallelDownload.downloadItem.id.toString()
        
        // Create parent job
        val parentJob = coroutineScope.launch {
            val chunkJobs = parallelDownload.chunks.map { chunk ->
                async {
                    downloadChunk(parallelDownload.downloadItem, chunk)
                }
            }
            
            chunkJobs.awaitAll()
            
            // Merge chunks when all complete
            mergeChunks(parallelDownload)
        }
        
        activeDownloads[downloadId] = parentJob
        parentJob.invokeOnCompletion {
            activeDownloads.remove(downloadId)
        }
    }
    
    private suspend fun downloadChunk(downloadItem: DownloadItem, chunk: DownloadChunk) {
        if (chunk.downloaded) return
        
        try {
            val request = okhttp3.Request.Builder()
                .url(downloadItem.url)
                .header("Range", "bytes=${chunk.startByte}-${chunk.endByte}")
                .build()
            
            httpClient.newCall(request).execute().use { response ->
                if (response.isSuccessful) {
                    response.body?.byteStream()?.use { input ->
                        chunk.file.outputStream().use { output ->
                            input.copyTo(output)
                        }
                    }
                    chunk.downloaded = true
                }
            }
        } catch (e: Exception) {
            // Retry chunk download
            delay(1000)
            downloadChunk(downloadItem, chunk)
        }
    }
    
    private fun mergeChunks(parallelDownload: ParallelDownload) {
        val finalFile = File(context.getExternalFilesDir(null), "downloads/${parallelDownload.downloadItem.title}")
        
        finalFile.outputStream().use { output ->
            parallelDownload.chunks.sortedBy { it.chunkId }.forEach { chunk ->
                if (chunk.file.exists()) {
                    chunk.file.inputStream().use { input ->
                        input.copyTo(output)
                    }
                    chunk.file.delete()
                }
            }
        }
        
        // Clean up directory
        val downloadDir = File(context.getExternalFilesDir(null), "downloads/${parallelDownload.downloadItem.id}")
        downloadDir.deleteRecursively()
    }
    
    fun pauseDownload(downloadId: String) {
        activeDownloads[downloadId]?.cancel()
    }
    
    fun resumeDownload(downloadId: String, downloadItem: DownloadItem) {
        startParallelDownload(downloadItem)
    }
    
    fun cancelDownload(downloadId: String) {
        activeDownloads[downloadId]?.cancel()
        activeDownloads.remove(downloadId)
    }
    
    fun getDownloadProgress(downloadId: String): Float {
        activeDownloads[downloadId] ?: return 0f
        // Return progress based on completed chunks
        return 0f // Simplified for now
    }
    
    fun shutdown() {
        coroutineScope.cancel()
        activeDownloads.values.forEach { it.cancel() }
        activeDownloads.clear()
    }
    
    // Get parallel download statistics
    fun getParallelStats(): ParallelStats {
        return ParallelStats(
            activeDownloads = activeDownloads.size,
            maxConcurrent = maxConcurrentDownloads,
            totalChunks = activeDownloads.size * 4 // Estimate
        )
    }
    
    data class ParallelStats(
        val activeDownloads: Int,
        val maxConcurrent: Int,
        val totalChunks: Int
    )
}
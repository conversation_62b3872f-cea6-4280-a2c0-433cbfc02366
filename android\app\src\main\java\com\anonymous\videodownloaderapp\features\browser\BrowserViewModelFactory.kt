package com.anonymous.videodownloaderapp.features.browser

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.anonymous.videodownloaderapp.data.DownloadRepository

class BrowserViewModelFactory(
    private val context: Context,
    private val downloadRepository: DownloadRepository
) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(BrowserViewModel::class.java)) {
            return BrowserViewModel(context, downloadRepository) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
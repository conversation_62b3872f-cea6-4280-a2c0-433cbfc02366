package com.anonymous.videodownloaderapp.services;

/**
 * Tratamento de Casos Especiais Críticos
 * Quarentena de Conteúdo Offscreen, PiP, Streams Live
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000~\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u001b\u0018\u0000 F2\u00020\u0001:\u0006FGHIJKB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u000f2\b\u0010 \u001a\u0004\u0018\u00010!J*\u0010\"\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u000f2\u0006\u0010#\u001a\u00020\u000f2\b\u0010$\u001a\u0004\u0018\u00010%2\u0006\u0010&\u001a\u00020\'H\u0002J\u0006\u0010(\u001a\u00020\u001eJ\u0006\u0010)\u001a\u00020\u001eJ\u0014\u0010*\u001a\u0004\u0018\u00010\'2\b\u0010$\u001a\u0004\u0018\u00010%H\u0002J \u0010+\u001a\u00020,2\u0006\u0010\u001f\u001a\u00020\u000f2\u0006\u0010#\u001a\u00020\u000f2\b\u0010$\u001a\u0004\u0018\u00010%J\u000e\u0010-\u001a\u00020.2\u0006\u0010#\u001a\u00020\u000fJ\u0010\u0010/\u001a\u0004\u0018\u00010\b2\u0006\u0010#\u001a\u00020\u000fJ\u0012\u00100\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u000101J\u000e\u00102\u001a\u00020,2\u0006\u0010\u001f\u001a\u00020\u000fJ\u000e\u00103\u001a\u00020,2\u0006\u0010#\u001a\u00020\u000fJ\u0018\u00104\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u000f2\b\b\u0002\u00105\u001a\u00020,J(\u00106\u001a\u00020\u001e2\u0006\u00107\u001a\u00020\u000f2\b\b\u0002\u00108\u001a\u00020,2\u000e\b\u0002\u00109\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007J\u000e\u0010:\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u000fJ\u0010\u0010;\u001a\u00020\u001e2\u0006\u00107\u001a\u00020\u000fH\u0002J\u0010\u0010<\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u000fH\u0002J\u000e\u0010=\u001a\u00020,2\u0006\u0010\u001f\u001a\u00020\u000fJ\u000e\u0010>\u001a\u00020,2\u0006\u0010#\u001a\u00020\u000fJ\b\u0010?\u001a\u00020\u001eH\u0002J\u0016\u0010@\u001a\u00020\u001e2\u0006\u00107\u001a\u00020\u000fH\u0082@\u00a2\u0006\u0002\u0010AJ\u001c\u0010B\u001a\u00020\u001e2\u0006\u00107\u001a\u00020\u000f2\f\u0010C\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007J\b\u0010D\u001a\u00020\u001eH\u0002J\b\u0010E\u001a\u00020\u001eH\u0002R\u001a\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00100\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\b0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\n0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u001a\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00070\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0015R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006L"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_liveStreamsState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$LiveStreamInfo;", "_pipState", "Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$PiPState;", "_quarantineState", "Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$QuarantineEntry;", "cleanupJobs", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lkotlinx/coroutines/Job;", "liveStreamsMap", "liveStreamsState", "Lkotlinx/coroutines/flow/StateFlow;", "getLiveStreamsState", "()Lkotlinx/coroutines/flow/StateFlow;", "pipState", "getPipState", "quarantineMap", "quarantineState", "getQuarantineState", "scope", "Lkotlinx/coroutines/CoroutineScope;", "activatePiP", "", "candidateId", "bounds", "Landroid/graphics/Rect;", "addToQuarantine", "url", "element", "Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$VideoElement;", "reason", "Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$QuarantineReason;", "cleanup", "deactivatePiP", "determineQuarantineReason", "evaluateForQuarantine", "", "getCacheTimeout", "", "getLiveStreamInfo", "getSpecialCasesStats", "", "isInQuarantine", "isLiveStream", "promoteFromQuarantine", "hasUserInteraction", "registerLiveStream", "manifestUrl", "isDynamic", "initialQualities", "removeFromQuarantine", "scheduleManifestRefresh", "scheduleQuarantineCleanup", "shouldHavePiPException", "shouldUseAggressiveCacheInvalidation", "startPeriodicCleanup", "triggerManifestReparse", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLiveStreamQualities", "newQualities", "updateLiveStreamsState", "updateQuarantineState", "Companion", "LiveStreamInfo", "PiPState", "QuarantineEntry", "QuarantineReason", "VideoElement", "app_debug"})
public final class SpecialCasesHandler {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    private static final long QUARANTINE_TIMEOUT_MS = 5000L;
    private static final long PIP_GRACE_PERIOD_MS = 10000L;
    private static final long LIVE_MANIFEST_REFRESH_MS = 300000L;
    private static final int MIN_INTERACTION_SCORE = 2;
    private static final float MIN_VISIBLE_AREA_THRESHOLD = 0.3F;
    private static final int MIN_SIZE_THRESHOLD = 100;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.anonymous.videodownloaderapp.services.SpecialCasesHandler instance;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope scope = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineEntry> quarantineMap = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineEntry>> _quarantineState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineEntry>> quarantineState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.anonymous.videodownloaderapp.services.SpecialCasesHandler.PiPState> _pipState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.services.SpecialCasesHandler.PiPState> pipState = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.anonymous.videodownloaderapp.services.SpecialCasesHandler.LiveStreamInfo> liveStreamsMap = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.anonymous.videodownloaderapp.services.SpecialCasesHandler.LiveStreamInfo>> _liveStreamsState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.anonymous.videodownloaderapp.services.SpecialCasesHandler.LiveStreamInfo>> liveStreamsState = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlinx.coroutines.Job> cleanupJobs = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.Companion Companion = null;
    
    public SpecialCasesHandler(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineEntry>> getQuarantineState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.anonymous.videodownloaderapp.services.SpecialCasesHandler.PiPState> getPipState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.anonymous.videodownloaderapp.services.SpecialCasesHandler.LiveStreamInfo>> getLiveStreamsState() {
        return null;
    }
    
    /**
     * Avaliar se candidato deve ser colocado em quarentena
     */
    public final boolean evaluateForQuarantine(@org.jetbrains.annotations.NotNull()
    java.lang.String candidateId, @org.jetbrains.annotations.NotNull()
    java.lang.String url, @org.jetbrains.annotations.Nullable()
    com.anonymous.videodownloaderapp.services.SpecialCasesHandler.VideoElement element) {
        return false;
    }
    
    /**
     * Determinar razão para quarentena
     */
    private final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineReason determineQuarantineReason(com.anonymous.videodownloaderapp.services.SpecialCasesHandler.VideoElement element) {
        return null;
    }
    
    /**
     * Adicionar à quarentena
     */
    private final void addToQuarantine(java.lang.String candidateId, java.lang.String url, com.anonymous.videodownloaderapp.services.SpecialCasesHandler.VideoElement element, com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineReason reason) {
    }
    
    /**
     * Remover da quarentena
     */
    public final void removeFromQuarantine(@org.jetbrains.annotations.NotNull()
    java.lang.String candidateId) {
    }
    
    /**
     * Promover candidato da quarentena (quando se torna visível)
     */
    public final void promoteFromQuarantine(@org.jetbrains.annotations.NotNull()
    java.lang.String candidateId, boolean hasUserInteraction) {
    }
    
    /**
     * Verificar se candidato está em quarentena
     */
    public final boolean isInQuarantine(@org.jetbrains.annotations.NotNull()
    java.lang.String candidateId) {
        return false;
    }
    
    /**
     * Ativar modo Picture-in-Picture
     */
    public final void activatePiP(@org.jetbrains.annotations.NotNull()
    java.lang.String candidateId, @org.jetbrains.annotations.Nullable()
    android.graphics.Rect bounds) {
    }
    
    /**
     * Desativar modo Picture-in-Picture
     */
    public final void deactivatePiP() {
    }
    
    /**
     * Verificar se candidato deve ter exceção de PiP
     */
    public final boolean shouldHavePiPException(@org.jetbrains.annotations.NotNull()
    java.lang.String candidateId) {
        return false;
    }
    
    /**
     * Registrar stream live
     */
    public final void registerLiveStream(@org.jetbrains.annotations.NotNull()
    java.lang.String manifestUrl, boolean isDynamic, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> initialQualities) {
    }
    
    /**
     * Verificar se URL é stream live
     */
    public final boolean isLiveStream(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return false;
    }
    
    /**
     * Obter informações de stream live
     */
    @org.jetbrains.annotations.Nullable()
    public final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.LiveStreamInfo getLiveStreamInfo(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return null;
    }
    
    /**
     * Atualizar qualidades de stream live
     */
    public final void updateLiveStreamQualities(@org.jetbrains.annotations.NotNull()
    java.lang.String manifestUrl, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> newQualities) {
    }
    
    /**
     * Verificar se deve usar cache invalidation agressiva
     */
    public final boolean shouldUseAggressiveCacheInvalidation(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return false;
    }
    
    /**
     * Obter timeout de cache para URL
     */
    public final long getCacheTimeout(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return 0L;
    }
    
    /**
     * Agendar limpeza de quarentena
     */
    private final void scheduleQuarantineCleanup(java.lang.String candidateId) {
    }
    
    /**
     * Agendar refresh de manifest live
     */
    private final void scheduleManifestRefresh(java.lang.String manifestUrl) {
    }
    
    /**
     * Trigger re-parsing de manifest (implementação específica)
     */
    private final java.lang.Object triggerManifestReparse(java.lang.String manifestUrl, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Limpeza periódica
     */
    private final void startPeriodicCleanup() {
    }
    
    /**
     * Atualizar estado da quarentena
     */
    private final void updateQuarantineState() {
    }
    
    /**
     * Atualizar estado dos streams live
     */
    private final void updateLiveStreamsState() {
    }
    
    /**
     * Obter estatísticas de casos especiais
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getSpecialCasesStats() {
        return null;
    }
    
    /**
     * Limpeza de recursos
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\u0010R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$Companion;", "", "()V", "LIVE_MANIFEST_REFRESH_MS", "", "MIN_INTERACTION_SCORE", "", "MIN_SIZE_THRESHOLD", "MIN_VISIBLE_AREA_THRESHOLD", "", "PIP_GRACE_PERIOD_MS", "QUARANTINE_TIMEOUT_MS", "instance", "Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler;", "getInstance", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.SpecialCasesHandler getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\b\u0012\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B3\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00050\t\u0012\u0006\u0010\n\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0007H\u00c6\u0003J\u000f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00050\tH\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003JA\u0010\u0018\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00050\t2\b\b\u0002\u0010\n\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u00032\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u0005H\u00d6\u0001R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00050\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u000eR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006\u001e"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$LiveStreamInfo;", "", "isLive", "", "manifestUrl", "", "lastManifestUpdate", "", "availableQualities", "", "isDynamic", "(ZLjava/lang/String;JLjava/util/List;Z)V", "getAvailableQualities", "()Ljava/util/List;", "()Z", "getLastManifestUpdate", "()J", "getManifestUrl", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class LiveStreamInfo {
        private final boolean isLive = false;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String manifestUrl = null;
        private final long lastManifestUpdate = 0L;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.String> availableQualities = null;
        private final boolean isDynamic = false;
        
        public LiveStreamInfo(boolean isLive, @org.jetbrains.annotations.NotNull()
        java.lang.String manifestUrl, long lastManifestUpdate, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> availableQualities, boolean isDynamic) {
            super();
        }
        
        public final boolean isLive() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getManifestUrl() {
            return null;
        }
        
        public final long getLastManifestUpdate() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getAvailableQualities() {
            return null;
        }
        
        public final boolean isDynamic() {
            return false;
        }
        
        public final boolean component1() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        public final long component3() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> component4() {
            return null;
        }
        
        public final boolean component5() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.LiveStreamInfo copy(boolean isLive, @org.jetbrains.annotations.NotNull()
        java.lang.String manifestUrl, long lastManifestUpdate, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> availableQualities, boolean isDynamic) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\b\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\tH\u00c6\u0003J5\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\tH\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00032\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u0007H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\rR\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0013\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u0006\u001c"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$PiPState;", "", "isActive", "", "activationTime", "", "originalCandidateId", "", "pipBounds", "Landroid/graphics/Rect;", "(ZJLjava/lang/String;Landroid/graphics/Rect;)V", "getActivationTime", "()J", "()Z", "getOriginalCandidateId", "()Ljava/lang/String;", "getPipBounds", "()Landroid/graphics/Rect;", "component1", "component2", "component3", "component4", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class PiPState {
        private final boolean isActive = false;
        private final long activationTime = 0L;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String originalCandidateId = null;
        @org.jetbrains.annotations.Nullable()
        private final android.graphics.Rect pipBounds = null;
        
        public PiPState(boolean isActive, long activationTime, @org.jetbrains.annotations.Nullable()
        java.lang.String originalCandidateId, @org.jetbrains.annotations.Nullable()
        android.graphics.Rect pipBounds) {
            super();
        }
        
        public final boolean isActive() {
            return false;
        }
        
        public final long getActivationTime() {
            return 0L;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getOriginalCandidateId() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.graphics.Rect getPipBounds() {
            return null;
        }
        
        public final boolean component1() {
            return false;
        }
        
        public final long component2() {
            return 0L;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.graphics.Rect component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.PiPState copy(boolean isActive, long activationTime, @org.jetbrains.annotations.Nullable()
        java.lang.String originalCandidateId, @org.jetbrains.annotations.Nullable()
        android.graphics.Rect pipBounds) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0017\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BC\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\b\b\u0002\u0010\r\u001a\u00020\b\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\nH\u00c6\u0003J\t\u0010 \u001a\u00020\fH\u00c6\u0003J\t\u0010!\u001a\u00020\bH\u00c6\u0003JQ\u0010\"\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\bH\u00c6\u0001J\u0013\u0010#\u001a\u00020$2\b\u0010%\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010&\u001a\u00020\fH\u00d6\u0001J\t\u0010\'\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\r\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0016R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0010\u00a8\u0006("}, d2 = {"Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$QuarantineEntry;", "", "candidateId", "", "url", "element", "Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$VideoElement;", "quarantineStartTime", "", "reason", "Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$QuarantineReason;", "interactionCount", "", "lastInteractionTime", "(Ljava/lang/String;Ljava/lang/String;Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$VideoElement;JLcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$QuarantineReason;IJ)V", "getCandidateId", "()Ljava/lang/String;", "getElement", "()Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$VideoElement;", "getInteractionCount", "()I", "getLastInteractionTime", "()J", "getQuarantineStartTime", "getReason", "()Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$QuarantineReason;", "getUrl", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
    public static final class QuarantineEntry {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String candidateId = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String url = null;
        @org.jetbrains.annotations.Nullable()
        private final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.VideoElement element = null;
        private final long quarantineStartTime = 0L;
        @org.jetbrains.annotations.NotNull()
        private final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineReason reason = null;
        private final int interactionCount = 0;
        private final long lastInteractionTime = 0L;
        
        public QuarantineEntry(@org.jetbrains.annotations.NotNull()
        java.lang.String candidateId, @org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.Nullable()
        com.anonymous.videodownloaderapp.services.SpecialCasesHandler.VideoElement element, long quarantineStartTime, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineReason reason, int interactionCount, long lastInteractionTime) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getCandidateId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.VideoElement getElement() {
            return null;
        }
        
        public final long getQuarantineStartTime() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineReason getReason() {
            return null;
        }
        
        public final int getInteractionCount() {
            return 0;
        }
        
        public final long getLastInteractionTime() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.VideoElement component3() {
            return null;
        }
        
        public final long component4() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineReason component5() {
            return null;
        }
        
        public final int component6() {
            return 0;
        }
        
        public final long component7() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineEntry copy(@org.jetbrains.annotations.NotNull()
        java.lang.String candidateId, @org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.Nullable()
        com.anonymous.videodownloaderapp.services.SpecialCasesHandler.VideoElement element, long quarantineStartTime, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineReason reason, int interactionCount, long lastInteractionTime) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$QuarantineReason;", "", "(Ljava/lang/String;I)V", "OFFSCREEN_INVISIBLE", "CAROUSEL_HIDDEN", "TAB_INACTIVE", "SIZE_TOO_SMALL", "SUSPICIOUS_BEHAVIOR", "AUTOPLAY_SPAM", "app_debug"})
    public static enum QuarantineReason {
        /*public static final*/ OFFSCREEN_INVISIBLE /* = new OFFSCREEN_INVISIBLE() */,
        /*public static final*/ CAROUSEL_HIDDEN /* = new CAROUSEL_HIDDEN() */,
        /*public static final*/ TAB_INACTIVE /* = new TAB_INACTIVE() */,
        /*public static final*/ SIZE_TOO_SMALL /* = new SIZE_TOO_SMALL() */,
        /*public static final*/ SUSPICIOUS_BEHAVIOR /* = new SUSPICIOUS_BEHAVIOR() */,
        /*public static final*/ AUTOPLAY_SPAM /* = new AUTOPLAY_SPAM() */;
        
        QuarantineReason() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.anonymous.videodownloaderapp.services.SpecialCasesHandler.QuarantineReason> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0016\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BE\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\u0006\u0010\t\u001a\u00020\u0005\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u000bH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\rH\u00c6\u0003JY\u0010\u001f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u00c6\u0001J\u0013\u0010 \u001a\u00020\u00052\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\"\u001a\u00020\u000bH\u00d6\u0001J\t\u0010#\u001a\u00020$H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0012R\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0012R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u0012R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0012R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006%"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/SpecialCasesHandler$VideoElement;", "", "bounds", "Landroid/graphics/Rect;", "isVisible", "", "hasUserInteraction", "isPiP", "isInCarousel", "isInTab", "zIndex", "", "opacity", "", "(Landroid/graphics/Rect;ZZZZZIF)V", "getBounds", "()Landroid/graphics/Rect;", "getHasUserInteraction", "()Z", "getOpacity", "()F", "getZIndex", "()I", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "toString", "", "app_debug"})
    public static final class VideoElement {
        @org.jetbrains.annotations.NotNull()
        private final android.graphics.Rect bounds = null;
        private final boolean isVisible = false;
        private final boolean hasUserInteraction = false;
        private final boolean isPiP = false;
        private final boolean isInCarousel = false;
        private final boolean isInTab = false;
        private final int zIndex = 0;
        private final float opacity = 0.0F;
        
        public VideoElement(@org.jetbrains.annotations.NotNull()
        android.graphics.Rect bounds, boolean isVisible, boolean hasUserInteraction, boolean isPiP, boolean isInCarousel, boolean isInTab, int zIndex, float opacity) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.graphics.Rect getBounds() {
            return null;
        }
        
        public final boolean isVisible() {
            return false;
        }
        
        public final boolean getHasUserInteraction() {
            return false;
        }
        
        public final boolean isPiP() {
            return false;
        }
        
        public final boolean isInCarousel() {
            return false;
        }
        
        public final boolean isInTab() {
            return false;
        }
        
        public final int getZIndex() {
            return 0;
        }
        
        public final float getOpacity() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.graphics.Rect component1() {
            return null;
        }
        
        public final boolean component2() {
            return false;
        }
        
        public final boolean component3() {
            return false;
        }
        
        public final boolean component4() {
            return false;
        }
        
        public final boolean component5() {
            return false;
        }
        
        public final boolean component6() {
            return false;
        }
        
        public final int component7() {
            return 0;
        }
        
        public final float component8() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.SpecialCasesHandler.VideoElement copy(@org.jetbrains.annotations.NotNull()
        android.graphics.Rect bounds, boolean isVisible, boolean hasUserInteraction, boolean isPiP, boolean isInCarousel, boolean isInTab, int zIndex, float opacity) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}
package com.anonymous.videodownloaderapp.services;

/**
 * Cache LRU inteligente para manifests parseados com gestão automática de memória
 * baseada na RAM disponível do dispositivo.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\b\u0018\u0000 (2\u00020\u0001:\u0006\'()*+,B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u0010\u000e\u001a\u00020\rH\u0002J\u0006\u0010\u000f\u001a\u00020\u0010J\u000e\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0007J\u001a\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00160\u00152\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\u0015J\b\u0010\u0019\u001a\u00020\u0010H\u0002J\b\u0010\u001a\u001a\u00020\u001bH\u0002J\u0010\u0010\u001c\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u0007H\u0002J\u0010\u0010\u001d\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u0013\u001a\u00020\u0007J\u0012\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u001fJ\u0006\u0010 \u001a\u00020\u0010J\u0010\u0010!\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u0007H\u0002J:\u0010\"\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u00072\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00160\u00152\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u00072\u0006\u0010$\u001a\u00020%2\b\b\u0002\u0010&\u001a\u00020\u0012R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\b\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006-"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "accessOrder", "", "", "cache", "Ljava/util/concurrent/ConcurrentHashMap;", "Ljava/lang/ref/WeakReference;", "Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$CachedVideo;", "maxCacheSize", "", "calculateOptimalCacheSize", "clear", "", "contains", "", "url", "convertFromUniversal", "", "Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$ParsedQuality;", "qualities", "Lcom/anonymous/videodownloaderapp/services/UniversalVideoUrlExtractor$Quality;", "enforceSize", "estimateMemoryUsage", "", "generateCacheId", "get", "getStats", "", "invalidateExpired", "normalizeManifestUrl", "put", "manifestContent", "mediaType", "Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$MediaType;", "isLive", "CachedVideo", "Companion", "MediaType", "ParsedQuality", "TrackInfo", "TrackType", "app_debug"})
public final class NetworkVideoCache {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.anonymous.videodownloaderapp.services.NetworkVideoCache INSTANCE;
    private static final int MIN_CACHE_SIZE = 50;
    private static final int MAX_CACHE_SIZE = 200;
    private static final long LIVE_CONTENT_TTL_MS = 900000L;
    private static final long VOD_CONTENT_TTL_MS = 3600000L;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.ref.WeakReference<com.anonymous.videodownloaderapp.services.NetworkVideoCache.CachedVideo>> cache = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> accessOrder = null;
    private final int maxCacheSize = 0;
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.services.NetworkVideoCache.Companion Companion = null;
    
    private NetworkVideoCache(android.content.Context context) {
        super();
    }
    
    private final int calculateOptimalCacheSize() {
        return 0;
    }
    
    /**
     * Armazenar vídeo parseado no cache
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String put(@org.jetbrains.annotations.NotNull()
    java.lang.String url, @org.jetbrains.annotations.NotNull()
    java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.ParsedQuality> qualities, @org.jetbrains.annotations.Nullable()
    java.lang.String manifestContent, @org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.services.NetworkVideoCache.MediaType mediaType, boolean isLive) {
        return null;
    }
    
    /**
     * Recuperar vídeo do cache
     */
    @org.jetbrains.annotations.Nullable()
    public final com.anonymous.videodownloaderapp.services.NetworkVideoCache.CachedVideo get(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return null;
    }
    
    /**
     * Verificar se URL está no cache (sem acessar)
     */
    public final boolean contains(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return false;
    }
    
    /**
     * Invalidar cache para conteúdo live (chamado periodicamente)
     */
    public final void invalidateExpired() {
    }
    
    /**
     * Forçar limpeza do cache
     */
    public final void clear() {
    }
    
    /**
     * Obter estatísticas do cache
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getStats() {
        return null;
    }
    
    private final void enforceSize() {
    }
    
    private final java.lang.String normalizeManifestUrl(java.lang.String url) {
        return null;
    }
    
    private final java.lang.String generateCacheId(java.lang.String url) {
        return null;
    }
    
    private final long estimateMemoryUsage() {
        return 0L;
    }
    
    /**
     * Converter de UniversalVideoUrlExtractor.Quality para ParsedQuality
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.ParsedQuality> convertFromUniversal(@org.jetbrains.annotations.NotNull()
    java.util.List<com.anonymous.videodownloaderapp.services.UniversalVideoUrlExtractor.Quality> qualities) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001d\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BY\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\u0006\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\u0002\u0010\u0012J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010%\u001a\u00020\nH\u00c6\u0003J\t\u0010&\u001a\u00020\fH\u00c6\u0003J\t\u0010\'\u001a\u00020\u000eH\u00c6\u0003J\t\u0010(\u001a\u00020\u0010H\u00c6\u0003J\t\u0010)\u001a\u00020\u0010H\u00c6\u0003Jk\u0010*\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u0010H\u00c6\u0001J\u0013\u0010+\u001a\u00020\f2\b\u0010,\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010-\u001a\u00020.H\u00d6\u0001J\u0010\u0010/\u001a\u00020\f2\b\b\u0002\u00100\u001a\u00020\u000eJ\t\u00101\u001a\u00020\u0003H\u00d6\u0001J\u0006\u00102\u001a\u000203R\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0017R\u0011\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0014R\u0013\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0016R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0016\u00a8\u00064"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$CachedVideo;", "", "id", "", "url", "qualities", "", "Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$ParsedQuality;", "manifestContent", "mediaType", "Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$MediaType;", "isLive", "", "timestamp", "", "accessCount", "Ljava/util/concurrent/atomic/AtomicLong;", "lastAccess", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$MediaType;ZJLjava/util/concurrent/atomic/AtomicLong;Ljava/util/concurrent/atomic/AtomicLong;)V", "getAccessCount", "()Ljava/util/concurrent/atomic/AtomicLong;", "getId", "()Ljava/lang/String;", "()Z", "getLastAccess", "getManifestContent", "getMediaType", "()Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$MediaType;", "getQualities", "()Ljava/util/List;", "getTimestamp", "()J", "getUrl", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "isExpired", "now", "toString", "touch", "", "app_debug"})
    public static final class CachedVideo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String id = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String url = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.ParsedQuality> qualities = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String manifestContent = null;
        @org.jetbrains.annotations.NotNull()
        private final com.anonymous.videodownloaderapp.services.NetworkVideoCache.MediaType mediaType = null;
        private final boolean isLive = false;
        private final long timestamp = 0L;
        @org.jetbrains.annotations.NotNull()
        private final java.util.concurrent.atomic.AtomicLong accessCount = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.concurrent.atomic.AtomicLong lastAccess = null;
        
        public CachedVideo(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.NotNull()
        java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.ParsedQuality> qualities, @org.jetbrains.annotations.Nullable()
        java.lang.String manifestContent, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.NetworkVideoCache.MediaType mediaType, boolean isLive, long timestamp, @org.jetbrains.annotations.NotNull()
        java.util.concurrent.atomic.AtomicLong accessCount, @org.jetbrains.annotations.NotNull()
        java.util.concurrent.atomic.AtomicLong lastAccess) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.ParsedQuality> getQualities() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getManifestContent() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkVideoCache.MediaType getMediaType() {
            return null;
        }
        
        public final boolean isLive() {
            return false;
        }
        
        public final long getTimestamp() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.concurrent.atomic.AtomicLong getAccessCount() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.concurrent.atomic.AtomicLong getLastAccess() {
            return null;
        }
        
        public final void touch() {
        }
        
        public final boolean isExpired(long now) {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.ParsedQuality> component3() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkVideoCache.MediaType component5() {
            return null;
        }
        
        public final boolean component6() {
            return false;
        }
        
        public final long component7() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.concurrent.atomic.AtomicLong component8() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.concurrent.atomic.AtomicLong component9() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkVideoCache.CachedVideo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        java.lang.String url, @org.jetbrains.annotations.NotNull()
        java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.ParsedQuality> qualities, @org.jetbrains.annotations.Nullable()
        java.lang.String manifestContent, @org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.NetworkVideoCache.MediaType mediaType, boolean isLive, long timestamp, @org.jetbrains.annotations.NotNull()
        java.util.concurrent.atomic.AtomicLong accessCount, @org.jetbrains.annotations.NotNull()
        java.util.concurrent.atomic.AtomicLong lastAccess) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\rR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$Companion;", "", "()V", "INSTANCE", "Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache;", "LIVE_CONTENT_TTL_MS", "", "MAX_CACHE_SIZE", "", "MIN_CACHE_SIZE", "VOD_CONTENT_TTL_MS", "getInstance", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkVideoCache getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$MediaType;", "", "(Ljava/lang/String;I)V", "HLS", "DASH", "PROGRESSIVE", "app_debug"})
    public static enum MediaType {
        /*public static final*/ HLS /* = new HLS() */,
        /*public static final*/ DASH /* = new DASH() */,
        /*public static final*/ PROGRESSIVE /* = new PROGRESSIVE() */;
        
        MediaType() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.anonymous.videodownloaderapp.services.NetworkVideoCache.MediaType> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\"\b\u0086\b\u0018\u00002\u00020\u0001Bc\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\b\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\n\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010\u00a2\u0006\u0002\u0010\u0012J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010%\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u001cJ\u0010\u0010&\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u001cJ\u0010\u0010\'\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0014J\u000b\u0010(\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010)\u001a\u00020\u0003H\u00c6\u0003J\t\u0010*\u001a\u00020\fH\u00c6\u0003J\u0010\u0010+\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0019J\u000f\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010H\u00c6\u0003Jx\u0010-\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010H\u00c6\u0001\u00a2\u0006\u0002\u0010.J\u0013\u0010/\u001a\u00020\f2\b\u00100\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00101\u001a\u00020\u0005H\u00d6\u0001J\t\u00102\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010\u0015\u001a\u0004\b\u0013\u0010\u0014R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0015\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u0018\u0010\u0019R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\n\n\u0002\u0010\u001d\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u001eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0017R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0017R\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\n\n\u0002\u0010\u001d\u001a\u0004\b#\u0010\u001c\u00a8\u00063"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$ParsedQuality;", "", "label", "", "width", "", "height", "bandwidth", "", "codecs", "url", "isAudioOnly", "", "frameRate", "", "tracks", "", "Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$TrackInfo;", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/Double;Ljava/util/List;)V", "getBandwidth", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getCodecs", "()Ljava/lang/String;", "getFrameRate", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getHeight", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "()Z", "getLabel", "getTracks", "()Ljava/util/List;", "getUrl", "getWidth", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/Double;Ljava/util/List;)Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$ParsedQuality;", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class ParsedQuality {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String label = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.Integer width = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.Integer height = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.Long bandwidth = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String codecs = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String url = null;
        private final boolean isAudioOnly = false;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.Double frameRate = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackInfo> tracks = null;
        
        public ParsedQuality(@org.jetbrains.annotations.NotNull()
        java.lang.String label, @org.jetbrains.annotations.Nullable()
        java.lang.Integer width, @org.jetbrains.annotations.Nullable()
        java.lang.Integer height, @org.jetbrains.annotations.Nullable()
        java.lang.Long bandwidth, @org.jetbrains.annotations.Nullable()
        java.lang.String codecs, @org.jetbrains.annotations.NotNull()
        java.lang.String url, boolean isAudioOnly, @org.jetbrains.annotations.Nullable()
        java.lang.Double frameRate, @org.jetbrains.annotations.NotNull()
        java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackInfo> tracks) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getLabel() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Integer getWidth() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Integer getHeight() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Long getBandwidth() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getCodecs() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUrl() {
            return null;
        }
        
        public final boolean isAudioOnly() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Double getFrameRate() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackInfo> getTracks() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Integer component2() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Integer component3() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Long component4() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component6() {
            return null;
        }
        
        public final boolean component7() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Double component8() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackInfo> component9() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkVideoCache.ParsedQuality copy(@org.jetbrains.annotations.NotNull()
        java.lang.String label, @org.jetbrains.annotations.Nullable()
        java.lang.Integer width, @org.jetbrains.annotations.Nullable()
        java.lang.Integer height, @org.jetbrains.annotations.Nullable()
        java.lang.Long bandwidth, @org.jetbrains.annotations.Nullable()
        java.lang.String codecs, @org.jetbrains.annotations.NotNull()
        java.lang.String url, boolean isAudioOnly, @org.jetbrains.annotations.Nullable()
        java.lang.Double frameRate, @org.jetbrains.annotations.NotNull()
        java.util.List<com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackInfo> tracks) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J5\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\n\u00a8\u0006\u001a"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$TrackInfo;", "", "type", "Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$TrackType;", "language", "", "label", "url", "(Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$TrackType;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getLabel", "()Ljava/lang/String;", "getLanguage", "getType", "()Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$TrackType;", "getUrl", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    public static final class TrackInfo {
        @org.jetbrains.annotations.NotNull()
        private final com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackType type = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String language = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String label = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String url = null;
        
        public TrackInfo(@org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackType type, @org.jetbrains.annotations.Nullable()
        java.lang.String language, @org.jetbrains.annotations.Nullable()
        java.lang.String label, @org.jetbrains.annotations.NotNull()
        java.lang.String url) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackType getType() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getLanguage() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getLabel() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackType component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackInfo copy(@org.jetbrains.annotations.NotNull()
        com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackType type, @org.jetbrains.annotations.Nullable()
        java.lang.String language, @org.jetbrains.annotations.Nullable()
        java.lang.String label, @org.jetbrains.annotations.NotNull()
        java.lang.String url) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/anonymous/videodownloaderapp/services/NetworkVideoCache$TrackType;", "", "(Ljava/lang/String;I)V", "VIDEO", "AUDIO", "SUBTITLE", "app_debug"})
    public static enum TrackType {
        /*public static final*/ VIDEO /* = new VIDEO() */,
        /*public static final*/ AUDIO /* = new AUDIO() */,
        /*public static final*/ SUBTITLE /* = new SUBTITLE() */;
        
        TrackType() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.anonymous.videodownloaderapp.services.NetworkVideoCache.TrackType> getEntries() {
            return null;
        }
    }
}
/**
 * Test suite for duplicate download prevention
 * This file tests various scenarios where duplicate downloads might occur
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import BackgroundDownloadService from '../services/BackgroundDownloadService';
import DownloadManager from '../components/DownloadManager';
import DownloadTracker from '../services/DownloadTracker';
import DownloadNotificationService from '../services/DownloadNotificationService';

class DuplicateDownloadTest {
  
  static async clearTestData() {
    await AsyncStorage.removeItem('video_downloads');
    await AsyncStorage.removeItem('download_state');
    await AsyncStorage.removeItem('download_tracker_v2');
    BackgroundDownloadService.activeDownloads.clear();
    BackgroundDownloadService.downloadQueue = [];
    BackgroundDownloadService.downloadLocks.clear();
  }

  // Test 1: Concurrent download requests
  static async testConcurrentDownloads() {
    console.log('🧪 Testing concurrent downloads...');
    await this.clearTestData();

    const downloadInfo = {
      url: 'https://example.com/video.mp4',
      originalUrl: 'https://example.com/video.mp4',
      quality: { quality: '720p', label: '720p' }
    };

    try {
      // Simulate rapid clicks - start multiple downloads simultaneously
      const promises = [
        BackgroundDownloadService.startBackgroundDownload(downloadInfo),
        BackgroundDownloadService.startBackgroundDownload(downloadInfo),
        BackgroundDownloadService.startBackgroundDownload(downloadInfo)
      ];

      const results = await Promise.allSettled(promises);
      
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      console.log(`✅ Concurrent test results: ${successful} successful, ${failed} rejected`);
      
      if (successful > 1) {
        console.error('❌ DUPLICATE DETECTED: Multiple concurrent downloads succeeded');
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('❌ Concurrent test failed:', error);
      return false;
    }
  }

  // Test 2: URL variations
  static async testUrlVariations() {
    console.log('🧪 Testing URL variations...');
    await this.clearTestData();

    const baseUrl = 'https://youtube.com/watch?v=dQw4w9WgXcQ';
    const variations = [
      baseUrl,
      baseUrl + '&t=10s',
      baseUrl + '&utm_source=test',
      'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      'https://youtu.be/dQw4w9WgXcQ'
    ];

    const quality = { quality: '720p', label: '720p' };
    let successCount = 0;

    for (const url of variations) {
      try {
        await BackgroundDownloadService.startBackgroundDownload({
          url: url,
          originalUrl: url,
          quality: quality
        });
        successCount++;
        console.log(`✅ Download started for: ${url}`);
      } catch (error) {
        console.log(`🚫 Download blocked for: ${url} - ${error.message}`);
      }
    }

    console.log(`✅ URL variations test: ${successCount} successful downloads`);
    
    if (successCount > 1) {
      console.error('❌ DUPLICATE DETECTED: Multiple URL variations succeeded');
      return false;
    }
    
    return true;
  }

  // Test 3: Quality differences
  static async testQualityDifferences() {
    console.log('🧪 Testing quality differences...');
    await this.clearTestData();

    const url = 'https://example.com/video.mp4';
    const qualities = [
      { quality: '480p', label: '480p' },
      { quality: '720p', label: '720p' },
      { quality: '1080p', label: '1080p' }
    ];

    let successCount = 0;

    for (const quality of qualities) {
      try {
        await BackgroundDownloadService.startBackgroundDownload({
          url: url,
          originalUrl: url,
          quality: quality
        });
        successCount++;
        console.log(`✅ Download started for quality: ${quality.quality}`);
      } catch (error) {
        console.log(`🚫 Download blocked for quality: ${quality.quality} - ${error.message}`);
      }
    }

    console.log(`✅ Quality test: ${successCount} successful downloads`);
    
    // Different qualities should be allowed
    if (successCount !== qualities.length) {
      console.error('❌ QUALITY TEST FAILED: Different qualities should be allowed');
      return false;
    }
    
    return true;
  }

  // Test 4: Download completion race condition
  static async testCompletionRaceCondition() {
    console.log('🧪 Testing completion race condition...');
    await this.clearTestData();

    const downloadInfo = {
      url: 'https://example.com/video.mp4',
      originalUrl: 'https://example.com/video.mp4',
      quality: { quality: '720p', label: '720p' }
    };

    try {
      // Start first download
      const downloadId1 = await BackgroundDownloadService.startBackgroundDownload(downloadInfo);
      
      // Simulate completion by adding to history manually
      await BackgroundDownloadService.addToDownloadHistory({
        id: downloadId1,
        fileName: 'test_video.mp4',
        fileUri: 'file://test.mp4',
        fileSize: 1000000,
        quality: downloadInfo.quality,
        originalUrl: downloadInfo.originalUrl,
        completedTime: new Date().toISOString()
      });

      // Try to start same download again
      const downloadId2 = await BackgroundDownloadService.startBackgroundDownload(downloadInfo);
      
      console.error('❌ RACE CONDITION DETECTED: Second download succeeded after first completed');
      return false;
      
    } catch (error) {
      console.log(`✅ Race condition test passed: ${error.message}`);
      return true;
    }
  }

  // Test 5: DownloadManager duplicate detection
  static async testDownloadManagerDuplicates() {
    console.log('🧪 Testing DownloadManager duplicate detection...');
    await this.clearTestData();

    const downloadInfo = {
      fileName: 'test_video.mp4',
      uri: 'file://test.mp4',
      size: 1000000,
      originalUrl: 'https://example.com/video.mp4',
      quality: { quality: '720p', label: '720p' },
      title: 'Test Video'
    };

    try {
      // Add first download
      await DownloadManager.addDownload(downloadInfo);
      
      // Try to add same download again
      await DownloadManager.addDownload(downloadInfo);
      
      // Check if duplicate was added
      const downloads = JSON.parse(await AsyncStorage.getItem('video_downloads') || '[]');
      
      if (downloads.length > 1) {
        console.error('❌ DOWNLOADMANAGER DUPLICATE DETECTED: Multiple entries found');
        return false;
      }
      
      console.log('✅ DownloadManager duplicate test passed');
      return true;
      
    } catch (error) {
      console.error('❌ DownloadManager test failed:', error);
      return false;
    }
  }

  // Test 6: DownloadTracker functionality
  static async testDownloadTracker() {
    console.log('🧪 Testing DownloadTracker...');
    await this.clearTestData();

    const downloadInfo = {
      originalUrl: 'https://example.com/video.mp4',
      quality: { quality: '720p', label: '720p' },
      fileName: 'test_video.mp4',
      uri: 'file://test.mp4',
      size: 1000000,
      title: 'Test Video'
    };

    try {
      // Add download record
      await DownloadTracker.addDownloadRecord(downloadInfo);

      // Check if it exists
      const check = await DownloadTracker.checkDownloadExists(downloadInfo);

      if (!check.exists) {
        console.error('❌ TRACKER TEST FAILED: Download not found after adding');
        return false;
      }

      // Try to add same download again
      const duplicateCheck = await DownloadTracker.checkDownloadExists(downloadInfo);

      if (!duplicateCheck.exists) {
        console.error('❌ TRACKER TEST FAILED: Duplicate not detected');
        return false;
      }

      console.log('✅ DownloadTracker test passed');
      return true;

    } catch (error) {
      console.error('❌ DownloadTracker test failed:', error);
      return false;
    }
  }

  // Test 7: Enhanced duplicate prevention
  static async testEnhancedDuplicatePrevention() {
    console.log('🧪 Testing enhanced duplicate prevention...');
    await this.clearTestData();

    const downloadInfo = {
      url: 'https://youtube.com/watch?v=dQw4w9WgXcQ&utm_source=test',
      originalUrl: 'https://youtube.com/watch?v=dQw4w9WgXcQ&utm_source=test',
      quality: { quality: '720p', label: '720p' },
      title: 'Never Gonna Give You Up'
    };

    try {
      // Add to tracker first
      await DownloadTracker.addDownloadRecord({
        ...downloadInfo,
        fileName: 'test_video.mp4',
        uri: 'file://test.mp4',
        size: 1000000
      });

      // Try to start download with slightly different URL
      const similarDownloadInfo = {
        ...downloadInfo,
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        originalUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
      };

      await BackgroundDownloadService.startBackgroundDownload(similarDownloadInfo);

      console.error('❌ ENHANCED PREVENTION FAILED: Similar download succeeded');
      return false;

    } catch (error) {
      if (error.message.includes('já foi baixado')) {
        console.log('✅ Enhanced duplicate prevention test passed');
        return true;
      } else {
        console.error('❌ Enhanced prevention test failed with unexpected error:', error);
        return false;
      }
    }
  }

  // Run all tests
  static async runAllTests() {
    console.log('🚀 Starting duplicate download tests...');

    const tests = [
      { name: 'Concurrent Downloads', test: this.testConcurrentDownloads },
      { name: 'URL Variations', test: this.testUrlVariations },
      { name: 'Quality Differences', test: this.testQualityDifferences },
      { name: 'Completion Race Condition', test: this.testCompletionRaceCondition },
      { name: 'DownloadManager Duplicates', test: this.testDownloadManagerDuplicates },
      { name: 'DownloadTracker', test: this.testDownloadTracker },
      { name: 'Enhanced Duplicate Prevention', test: this.testEnhancedDuplicatePrevention }
    ];

    const results = [];
    
    for (const { name, test } of tests) {
      console.log(`\n--- Running ${name} Test ---`);
      try {
        const result = await test.call(this);
        results.push({ name, passed: result });
        console.log(`${result ? '✅' : '❌'} ${name}: ${result ? 'PASSED' : 'FAILED'}`);
      } catch (error) {
        results.push({ name, passed: false, error: error.message });
        console.log(`❌ ${name}: FAILED - ${error.message}`);
      }
    }

    console.log('\n📊 Test Results Summary:');
    results.forEach(({ name, passed, error }) => {
      console.log(`${passed ? '✅' : '❌'} ${name}: ${passed ? 'PASSED' : 'FAILED'}${error ? ` (${error})` : ''}`);
    });

    const passedCount = results.filter(r => r.passed).length;
    console.log(`\n🎯 Overall: ${passedCount}/${results.length} tests passed`);
    
    return results;
  }
}

export default DuplicateDownloadTest;

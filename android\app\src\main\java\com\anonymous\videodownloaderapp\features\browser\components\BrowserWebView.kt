package com.anonymous.videodownloaderapp.features.browser.components

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.webkit.*
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView

import kotlinx.coroutines.delay
import com.anonymous.videodownloaderapp.services.AdBlockerService
import com.anonymous.videodownloaderapp.services.HybridVideoDetectionService
import com.anonymous.videodownloaderapp.services.PrivacyService
import android.app.Activity
import android.content.pm.ActivityInfo
import android.view.View
import android.view.WindowManager
import okhttp3.OkHttpClient
import java.util.concurrent.TimeUnit
import android.widget.FrameLayout
import android.util.Log

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun BrowserWebView(
    url: String,
    isLoading: Boolean,
    onUrlChanged: (String) -> Unit,
    onLoadingChanged: (Boolean) -> Unit,
    onPageStarted: (String) -> Unit,
    onPageFinished: (String) -> Unit,
    onVideoDetectionRequested: (WebView) -> Unit,
    onRefresh: (WebView) -> Unit,
    onWebViewCreated: (WebView) -> Unit = {},
    onCanGoBackChanged: (Boolean) -> Unit = {},
    onCanGoForwardChanged: (Boolean) -> Unit = {},
    adBlockEnabled: Boolean = true,
    modifier: Modifier = Modifier
) {
    val adBlocker = remember { AdBlockerService() }
    val context = LocalContext.current
    val activity = context as Activity
    var webView by remember { mutableStateOf<WebView?>(null) }
    var currentUrl by remember { mutableStateOf(url) }
    // Cleanup effect
    DisposableEffect(Unit) {
        onDispose {
            webView?.apply {
                stopLoading()
                onPause()
                removeAllViews()
                destroy()
            }
        }
    }
    
    Box(modifier = modifier) {
            AndroidView(
                factory = { context ->
                    WebView(context).apply {
                        webView = this
                        onWebViewCreated(this)
                        
                        // Aplicar configurações de privacidade
                        PrivacyService.configurePrivacySettings(this)
                        
                        // Configurações do WebView
                        settings.apply {
                            javaScriptEnabled = true
                            domStorageEnabled = true
                            loadWithOverviewMode = true
                            useWideViewPort = true
                            builtInZoomControls = true
                            displayZoomControls = false
                            setSupportZoom(true)
                            
                            allowFileAccess = true
                            allowContentAccess = true
                            mediaPlaybackRequiresUserGesture = false
                            
                            // Configurações para reduzir crashes de renderização
                            setRenderPriority(WebSettings.RenderPriority.HIGH)
                            
                            // Configurações para melhor gerenciamento de memória
                            cacheMode = WebSettings.LOAD_DEFAULT
                            databaseEnabled = true
                            setSupportMultipleWindows(false)
                            
                            // Configurações de segurança e performance
                            mixedContentMode = WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
                            loadsImagesAutomatically = true
                            blockNetworkImage = false
                            blockNetworkLoads = false
                        }
                        
                        // Desabilitar hardware acceleration para reduzir crashes
                        setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                        
                        // Interface JavaScript para comunicação
                        addJavascriptInterface(object {
                            @JavascriptInterface
                            fun onVideosDetected(videosJson: String) {
                                // Esta interface pode ser usada para comunicação direta do JS
                                // Por enquanto, usamos o método de polling via evaluateJavascript
                            }
                        }, "VideoDetector")
                        
                        // Configurar touch listener para pull-to-refresh
                        setOnTouchListener { view, event ->
                            // Permitir pull-to-refresh apenas no topo da página
                            if (scrollY == 0) {
                                parent.requestDisallowInterceptTouchEvent(false)
                            } else {
                                parent.requestDisallowInterceptTouchEvent(true)
                            }
                            false
                        }
                        
                        // WebChromeClient para suporte a vídeo fullscreen
                        webChromeClient = object : WebChromeClient() {
                            private var customView: View? = null
                            private var customViewCallback: CustomViewCallback? = null
                            private var originalOrientation: Int = 0
                            private var originalSystemUiVisibility: Int = 0

                            override fun onShowCustomView(view: View?, callback: CustomViewCallback?) {
                                if (customView != null) {
                                    onHideCustomView()
                                    return
                                }
                                customView = view
                                customViewCallback = callback
                                originalSystemUiVisibility = activity.window.decorView.systemUiVisibility
                                originalOrientation = activity.requestedOrientation
                                activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                                activity.window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                                        View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                                        View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                                        View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                                        View.SYSTEM_UI_FLAG_FULLSCREEN or
                                        View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
                                activity.window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                                (activity.window.decorView as FrameLayout).addView(customView, FrameLayout.LayoutParams(-1, -1))
                                webView?.visibility = View.GONE
                            }

                            override fun onHideCustomView() {
                                (activity.window.decorView as FrameLayout).removeView(customView)
                                customView = null
                                activity.window.decorView.systemUiVisibility = originalSystemUiVisibility
                                activity.requestedOrientation = originalOrientation
                                customViewCallback?.onCustomViewHidden()
                                customViewCallback = null
                                webView?.visibility = View.VISIBLE
                                activity.window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                            }
                        }
                        
                        // WebViewClient para interceptar navegação
                        webViewClient = object : WebViewClient() {
                            override fun shouldInterceptRequest(
                                view: WebView?,
                                request: WebResourceRequest?
                            ): WebResourceResponse? {
                                if (request == null || request.url == null) {
                                    return super.shouldInterceptRequest(view, request)
                                }

                                // Bloquear anúncios e trackers apenas se habilitado
                                if (adBlockEnabled && (adBlocker.shouldBlockRequest(request) || adBlocker.shouldBlockTracker(request))) {
                                    return adBlocker.createBlockedResponse()
                                }

                                // Interceptar apenas manifestos de vídeo (HLS e DASH)
                                val url = request.url.toString()
                                if (url.endsWith(".m3u8") || url.endsWith(".mpd")) {
                                    try {
                                        // Criar cliente OkHttp para a requisição
                                        val okHttpClient = okhttp3.OkHttpClient.Builder()
                                            .connectTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                                            .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                                            .build()

                                        // Construir requisição OkHttp com headers originais
                                        val requestBuilder = okhttp3.Request.Builder().url(url)
                                        request.requestHeaders?.forEach { (key, value) ->
                                            requestBuilder.addHeader(key, value)
                                        }
                                        val okHttpRequest = requestBuilder.build()

                                        // Executar requisição
                                        val response = okHttpClient.newCall(okHttpRequest).execute()

                                        if (response.isSuccessful && response.body != null) {
                                            val contentType = response.header("content-type")
                                            val encoding = response.header("content-encoding")
                                            val responseBody = response.body!!

                                            // Ler conteúdo do manifesto
                                            val manifestContent = responseBody.string()

                                            // Notificar o HybridVideoDetectionService sobre o manifesto
                                            val hybrid = HybridVideoDetectionService.getInstance(context)
                                            hybrid.onManifestResponse(request, manifestContent)

                                            // Preparar headers de resposta
                                            val responseHeaders = mutableMapOf<String, String>()
                                            for (i in 0 until response.headers.size) {
                                                responseHeaders[response.headers.name(i)] = response.headers.value(i)
                                            }

                                            // Retornar resposta customizada com o conteúdo do manifesto
                                            return WebResourceResponse(
                                                contentType ?: "application/vnd.apple.mpegurl",
                                                encoding ?: "utf-8",
                                                response.code,
                                                response.message,
                                                responseHeaders,
                                                java.io.ByteArrayInputStream(manifestContent.toByteArray())
                                            )
                                        }
                                    } catch (e: Exception) {
                                        // Em caso de erro, deixar o WebView lidar com a requisição normalmente
                                        android.util.Log.w("BrowserWebView", "Erro ao interceptar manifesto: ${e.message}")
                                    }
                                }

                                return super.shouldInterceptRequest(view, request)
                            }
                            
                            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                                super.onPageStarted(view, url, favicon)
                                onLoadingChanged(true)
                                url?.let { 
                                    currentUrl = it
                                    onUrlChanged(it)
                                    onPageStarted(it)
                                }
                            }
                            
                            override fun onPageFinished(view: WebView?, url: String?) {
                                super.onPageFinished(view, url)
                                onLoadingChanged(false)
                                url?.let { onPageFinished(it) }
                                
                                // Atualizar estado de navegação
                                view?.let { webView ->
                                    onCanGoBackChanged(webView.canGoBack())
                                    onCanGoForwardChanged(webView.canGoForward())
                                }
                                
                                // Injetar script anti-tracking
                                view?.let { webView ->
                                    PrivacyService.injectAntiTrackingScript(webView)
                                }
                                
                                // Detectar vídeos após carregamento da página
                                view?.let { webView ->
                                    // Primeira detecção após 3 segundos
                                    view.postDelayed({
                                        onVideoDetectionRequested(webView)
                                    }, 3000)
                                    
                                    // Segunda detecção após 6 segundos para conteúdo dinâmico
                                    view.postDelayed({
                                        onVideoDetectionRequested(webView)
                                    }, 6000)
                                }
                            }
                            
                            override fun shouldOverrideUrlLoading(
                                view: WebView?,
                                request: WebResourceRequest?
                            ): Boolean {
                                return false // Permitir navegação normal
                            }
                            
                            override fun onReceivedError(
                                view: WebView?,
                                request: WebResourceRequest?,
                                error: WebResourceError?
                            ) {
                                super.onReceivedError(view, request, error)
                                onLoadingChanged(false)
                            }
                            
                            override fun onRenderProcessGone(view: WebView?, detail: RenderProcessGoneDetail?): Boolean {
                                Log.e("BrowserWebView", "Render process gone: ${detail?.didCrash()}")
                                
                                // Destruir o WebView atual para evitar memory leak
                                view?.apply {
                                    clearHistory()
                                    clearCache(true)
                                    loadUrl("about:blank")
                                    onPause()
                                    removeAllViews()
                                    destroy()
                                }
                                
                                // Retornar true indica que o app lidou com o crash
                                return true
                            }
                        }
                        
                        // URL será carregada no bloco update
                    }
                },
                update = { webView ->
                    // Só recarregar se a URL mudou
                    if (webView.url != url) {
                        webView.loadUrl(url)
                    }
                },
                modifier = Modifier.fillMaxSize()
            )
    }
}
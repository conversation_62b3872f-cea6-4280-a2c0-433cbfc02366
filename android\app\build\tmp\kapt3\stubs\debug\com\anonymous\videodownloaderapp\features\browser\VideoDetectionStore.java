package com.anonymous.videodownloaderapp.features.browser;

/**
 * In-memory store to keep detected video qualities per tab.
 * Thread-safe and lightweight. Not persisted across app restarts.
 *
 * Data model:
 * - Each entry is a pair of (qualityLabel, url).
 * - We keep insertion order and also de-duplicate by URL.
 * - Limit the list size per tab to avoid unbounded memory growth.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0002\u001f B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000bJ\u000e\u0010\u000f\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0007J\u0017\u0010\u0011\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0012\u001a\u00020\u0007H\u0002\u00a2\u0006\u0002\u0010\u0013J\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\t0\u00152\u0006\u0010\u0010\u001a\u00020\u0007J\u0010\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0007H\u0002J\u000e\u0010\u0017\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000bJ\u0018\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\u0007H\u0002J(\u0010\u001c\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u00072\u0018\u0010\u001d\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\u001e0\u0015R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R \u0010\u0005\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006!"}, d2 = {"Lcom/anonymous/videodownloaderapp/features/browser/VideoDetectionStore;", "", "()V", "MAX_PER_TAB", "", "byTab", "", "", "", "Lcom/anonymous/videodownloaderapp/features/browser/VideoDetectionStore$QualityEntry;", "listeners", "Lcom/anonymous/videodownloaderapp/features/browser/VideoDetectionStore$Listener;", "addListener", "", "listener", "clearTab", "tabId", "extractP", "label", "(Ljava/lang/String;)Ljava/lang/Integer;", "get", "", "notifyTab", "removeListener", "shouldPrefer", "", "newQ", "oldQ", "upsertPairs", "pairs", "Lkotlin/Pair;", "Listener", "QualityEntry", "app_debug"})
public final class VideoDetectionStore {
    private static final int MAX_PER_TAB = 100;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Map<java.lang.String, java.util.List<com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore.QualityEntry>> byTab = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore.Listener> listeners = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore INSTANCE = null;
    
    private VideoDetectionStore() {
        super();
    }
    
    public final void addListener(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore.Listener listener) {
    }
    
    public final void removeListener(@org.jetbrains.annotations.NotNull()
    com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore.Listener listener) {
    }
    
    public final void clearTab(@org.jetbrains.annotations.NotNull()
    java.lang.String tabId) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore.QualityEntry> get(@org.jetbrains.annotations.NotNull()
    java.lang.String tabId) {
        return null;
    }
    
    /**
     * Merge a set of quality::url pairs into the store for a given tab.
     * Ensures uniqueness by URL, preferring the newest label for the same URL.
     */
    public final void upsertPairs(@org.jetbrains.annotations.NotNull()
    java.lang.String tabId, @org.jetbrains.annotations.NotNull()
    java.util.List<kotlin.Pair<java.lang.String, java.lang.String>> pairs) {
    }
    
    private final boolean shouldPrefer(java.lang.String newQ, java.lang.String oldQ) {
        return false;
    }
    
    private final java.lang.Integer extractP(java.lang.String label) {
        return null;
    }
    
    private final void notifyTab(java.lang.String tabId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H&\u00a8\u0006\t"}, d2 = {"Lcom/anonymous/videodownloaderapp/features/browser/VideoDetectionStore$Listener;", "", "onTabUpdated", "", "tabId", "", "entries", "", "Lcom/anonymous/videodownloaderapp/features/browser/VideoDetectionStore$QualityEntry;", "app_debug"})
    public static abstract interface Listener {
        
        public abstract void onTabUpdated(@org.jetbrains.annotations.NotNull()
        java.lang.String tabId, @org.jetbrains.annotations.NotNull()
        java.util.List<com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore.QualityEntry> entries);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005J\t\u0010\t\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\n\u001a\u00020\u0003H\u00c6\u0003J\u001d\u0010\u000b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/anonymous/videodownloaderapp/features/browser/VideoDetectionStore$QualityEntry;", "", "quality", "", "url", "(Ljava/lang/String;Ljava/lang/String;)V", "getQuality", "()Ljava/lang/String;", "getUrl", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    public static final class QualityEntry {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String quality = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String url = null;
        
        public QualityEntry(@org.jetbrains.annotations.NotNull()
        java.lang.String quality, @org.jetbrains.annotations.NotNull()
        java.lang.String url) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getQuality() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.anonymous.videodownloaderapp.features.browser.VideoDetectionStore.QualityEntry copy(@org.jetbrains.annotations.NotNull()
        java.lang.String quality, @org.jetbrains.annotations.NotNull()
        java.lang.String url) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}
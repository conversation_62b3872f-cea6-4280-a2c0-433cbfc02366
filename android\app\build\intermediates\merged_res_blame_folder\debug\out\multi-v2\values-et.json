{"logs": [{"outputFile": "com.anonymous.videodownloaderapp-mergeDebugResources-79:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\19dbdb4d446295775d71c6ca0010df0c\\transformed\\media3-ui-1.4.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,514,702,783,864,940,1031,1124,1194,1258,1342,1425,1490,1554,1617,1687,1807,1925,2044,2116,2200,2269,2338,2432,2526,2591,2657,2710,2770,2818,2879,2944,3014,3079,3145,3209,3269,3334,3386,3448,3524,3600,3655", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,51,61,75,75,54,66", "endOffsets": "318,509,697,778,859,935,1026,1119,1189,1253,1337,1420,1485,1549,1612,1682,1802,1920,2039,2111,2195,2264,2333,2427,2521,2586,2652,2705,2765,2813,2874,2939,3009,3074,3140,3204,3264,3329,3381,3443,3519,3595,3650,3717"}, "to": {"startLines": "2,11,15,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,418,609,5705,5786,5867,5943,6034,6127,6197,6261,6345,6428,6493,6557,6620,6690,6810,6928,7047,7119,7203,7272,7341,7435,7529,7594,8354,8407,8467,8515,8576,8641,8711,8776,8842,8906,8966,9031,9083,9145,9221,9297,9352", "endLines": "10,14,18,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,51,61,75,75,54,66", "endOffsets": "413,604,792,5781,5862,5938,6029,6122,6192,6256,6340,6423,6488,6552,6615,6685,6805,6923,7042,7114,7198,7267,7336,7430,7524,7589,7655,8402,8462,8510,8571,8636,8706,8771,8837,8901,8961,9026,9078,9140,9216,9292,9347,9414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a1b6440c5b30b54fb1514c977cfe8efa\\transformed\\material-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1047,1111,1196,1264,1328,1415,1479,1543,1602,1674,1738,1792,1911,1971,2032,2086,2159,2292,2376,2453,2546,2626,2719,2857,2937,3016,3142,3230,3309,3364,3415,3481,3554,3633,3704,3783,3856,3931,4005,4077,4190,4278,4355,4446,4538,4612,4686,4777,4831,4913,4982,5065,5151,5213,5277,5340,5408,5511,5614,5711,5812,5871,5926,6007,6096,6173", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "261,341,420,505,597,684,783,900,982,1042,1106,1191,1259,1323,1410,1474,1538,1597,1669,1733,1787,1906,1966,2027,2081,2154,2287,2371,2448,2541,2621,2714,2852,2932,3011,3137,3225,3304,3359,3410,3476,3549,3628,3699,3778,3851,3926,4000,4072,4185,4273,4350,4441,4533,4607,4681,4772,4826,4908,4977,5060,5146,5208,5272,5335,5403,5506,5609,5706,5807,5866,5921,6002,6091,6168,6246"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,70,71,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,244,248,249,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "797,3685,3765,3844,3929,4021,4837,4936,5053,5581,5641,9419,9504,9741,16051,16138,16202,16266,16325,16397,16461,16515,16634,16694,16755,16809,16882,17015,17099,17176,17269,17349,17442,17580,17660,17739,17865,17953,18032,18087,18138,18204,18277,18356,18427,18506,18579,18654,18728,18800,18913,19001,19078,19169,19261,19335,19409,19500,19554,19636,19705,19788,19874,19936,20000,20063,20131,20234,20337,20434,20535,20594,20824,21155,21244,21393", "endLines": "22,50,51,52,53,54,62,63,64,70,71,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,244,248,249,251", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "958,3760,3839,3924,4016,4103,4931,5048,5130,5636,5700,9499,9567,9800,16133,16197,16261,16320,16392,16456,16510,16629,16689,16750,16804,16877,17010,17094,17171,17264,17344,17437,17575,17655,17734,17860,17948,18027,18082,18133,18199,18272,18351,18422,18501,18574,18649,18723,18795,18908,18996,19073,19164,19256,19330,19404,19495,19549,19631,19700,19783,19869,19931,19995,20058,20126,20229,20332,20429,20530,20589,20644,20900,21239,21316,21466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c83662cb1041180272524de55f94ad06\\transformed\\media3-exoplayer-1.4.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,263,337,419,490,580,672", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "124,191,258,332,414,485,575,667,744"}, "to": {"startLines": "96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7660,7734,7801,7868,7942,8024,8095,8185,8277", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "7729,7796,7863,7937,8019,8090,8180,8272,8349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c594a6b1ad90298d1781df40da764bd0\\transformed\\foundation-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "259,260", "startColumns": "4,4", "startOffsets": "22064,22154", "endColumns": "89,88", "endOffsets": "22149,22238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ea0d069a154b88d6448f277841df52b5\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,977,1062,1134,1209,1284,1356,1433,1504", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,74,74,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,972,1057,1129,1204,1279,1351,1428,1499,1621"}, "to": {"startLines": "65,66,67,68,69,124,125,242,243,245,246,250,252,253,254,256,257,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5135,5228,5308,5404,5499,9572,9650,20649,20740,20905,20987,21321,21471,21546,21621,21794,21871,21942", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,74,74,71,76,70,121", "endOffsets": "5223,5303,5399,5494,5576,9645,9736,20735,20819,20982,21067,21388,21541,21616,21688,21866,21937,22059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd2b74cc75dcc4061ff406ef39af9f1c\\transformed\\core-1.13.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "55,56,57,58,59,60,61,255", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4108,4203,4305,4403,4506,4612,4717,21693", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "4198,4300,4398,4501,4607,4712,4832,21789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\576ca5b088d8fb564e2e136af4f08d54\\transformed\\material3-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,297,411,530,629,730,848,981,1101,1249,1336,1437,1531,1630,1746,1873,1979,2114,2247,2378,2553,2679,2798,2919,3041,3136,3233,3353,3487,3592,3695,3800,3931,4066,4174,4277,4354,4450,4546,4650,4737,4822,4928,5008,5094,5195,5299,5393,5497,5584,5693,5794,5901,6018,6098,6202", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "170,292,406,525,624,725,843,976,1096,1244,1331,1432,1526,1625,1741,1868,1974,2109,2242,2373,2548,2674,2793,2914,3036,3131,3228,3348,3482,3587,3690,3795,3926,4061,4169,4272,4349,4445,4541,4645,4732,4817,4923,5003,5089,5190,5294,5388,5492,5579,5688,5789,5896,6013,6093,6197,6296"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9805,9925,10047,10161,10280,10379,10480,10598,10731,10851,10999,11086,11187,11281,11380,11496,11623,11729,11864,11997,12128,12303,12429,12548,12669,12791,12886,12983,13103,13237,13342,13445,13550,13681,13816,13924,14027,14104,14200,14296,14400,14487,14572,14678,14758,14844,14945,15049,15143,15247,15334,15443,15544,15651,15768,15848,15952", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "9920,10042,10156,10275,10374,10475,10593,10726,10846,10994,11081,11182,11276,11375,11491,11618,11724,11859,11992,12123,12298,12424,12543,12664,12786,12881,12978,13098,13232,13337,13440,13545,13676,13811,13919,14022,14099,14195,14291,14395,14482,14567,14673,14753,14839,14940,15044,15138,15242,15329,15438,15539,15646,15763,15843,15947,16046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\366aa02d18db07ca95bf2d64251e5a94\\transformed\\appcompat-1.7.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "963,1069,1168,1279,1365,1467,1584,1665,1742,1834,1928,2024,2126,2235,2329,2430,2524,2616,2709,2792,2903,3007,3106,3216,3318,3417,3583,21072", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "1064,1163,1274,1360,1462,1579,1660,1737,1829,1923,2019,2121,2230,2324,2425,2519,2611,2704,2787,2898,3002,3101,3211,3313,3412,3578,3680,21150"}}]}]}
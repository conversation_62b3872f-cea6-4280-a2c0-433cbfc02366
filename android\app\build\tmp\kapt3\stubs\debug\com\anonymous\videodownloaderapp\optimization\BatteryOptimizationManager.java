package com.anonymous.videodownloaderapp.optimization;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\r\u001a\u00020\u000eJ\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010J\u0006\u0010\u0012\u001a\u00020\u0013J\u0006\u0010\u0014\u001a\u00020\u0013J\u0006\u0010\u0015\u001a\u00020\u0013J\u0016\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00112\u0006\u0010\u0019\u001a\u00020\u0017J\u0010\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u0018\u001a\u00020\u0011H\u0007J\u0006\u0010\u001c\u001a\u00020\u0013J\u0006\u0010\u001d\u001a\u00020\u001bJ\u0006\u0010\u001e\u001a\u00020\u001bR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/anonymous/videodownloaderapp/optimization/BatteryOptimizationManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "batteryManager", "Landroid/os/BatteryManager;", "connectivityManager", "Landroid/net/ConnectivityManager;", "coroutineScope", "Lkotlinx/coroutines/CoroutineScope;", "powerManager", "Landroid/os/PowerManager;", "getBatteryLevel", "", "getOptimizationRecommendations", "", "", "isBatteryOptimizationEnabled", "", "isCharging", "isWifiConnected", "optimizeDownloadSpeed", "", "downloadId", "currentSpeed", "scheduleBatteryOptimizedDownload", "", "shouldRestrictDownloads", "startBatteryMonitoring", "stopBatteryMonitoring", "app_debug"})
public final class BatteryOptimizationManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final android.os.BatteryManager batteryManager = null;
    @org.jetbrains.annotations.NotNull()
    private final android.net.ConnectivityManager connectivityManager = null;
    @org.jetbrains.annotations.NotNull()
    private final android.os.PowerManager powerManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope coroutineScope = null;
    
    public BatteryOptimizationManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    public final boolean isBatteryOptimizationEnabled() {
        return false;
    }
    
    public final int getBatteryLevel() {
        return 0;
    }
    
    public final boolean isCharging() {
        return false;
    }
    
    public final boolean shouldRestrictDownloads() {
        return false;
    }
    
    public final boolean isWifiConnected() {
        return false;
    }
    
    public final long optimizeDownloadSpeed(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId, long currentSpeed) {
        return 0L;
    }
    
    @androidx.annotation.RequiresApi(value = android.os.Build.VERSION_CODES.LOLLIPOP)
    public final void scheduleBatteryOptimizedDownload(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadId) {
    }
    
    public final void startBatteryMonitoring() {
    }
    
    public final void stopBatteryMonitoring() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getOptimizationRecommendations() {
        return null;
    }
}